package com.sayweee.weee.react.component;


import android.text.style.TextAppearanceSpan;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.uimanager.SimpleViewManager;
import com.facebook.react.uimanager.ThemedReactContext;
import com.facebook.react.uimanager.ViewManagerDelegate;
import com.facebook.react.uimanager.events.RCTEventEmitter;
import com.facebook.react.viewmanagers.WeeeAtcComponentManagerDelegate;
import com.facebook.react.viewmanagers.WeeeAtcComponentManagerInterface;
import com.sayweee.core.order.SharedOrderViewModel;
import com.sayweee.weee.R;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.module.account.bean.SimplePreOrderBean;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.module.cate.product.adapter.OpHelper;
import com.sayweee.weee.utils.DecimalTools;
import com.sayweee.weee.utils.JsonUtils;
import com.sayweee.weee.widget.op.CartOpLayout;

import java.util.HashMap;
import java.util.Map;

public class ReactAtcButtonManager extends SimpleViewManager<CartOpLayout>
        implements WeeeAtcComponentManagerInterface<CartOpLayout> {

    public static final String REACT_CLASS = "WeeeAtcComponent";

    ReactApplicationContext mCallerContext;


    public ReactAtcButtonManager(ReactApplicationContext reactApplicationContext) {
        mCallerContext = reactApplicationContext;
    }

    @NonNull
    @Override
    public String getName() {
        return REACT_CLASS;
    }

    @NonNull
    @Override
    protected CartOpLayout createViewInstance(@NonNull ThemedReactContext themedReactContext) {
        CartOpLayout opLayout = new CartOpLayout(themedReactContext);
        opLayout.setLayoutParams(new CartOpLayout.LayoutParams(200,
                100));
        initOpLayout(opLayout);
//        setupEventListeners(opLayout, themedReactContext);

        return opLayout;
    }


    @Override
    public void setProductJson(CartOpLayout view, @Nullable String value) {
        if (value != null) {
            try {
                ProductBean bean = JsonUtils.parseObject(value, ProductBean.class);
                // 存储 ProductBean 用于事件回调
                view.setTag(R.id.tag_product_bean, bean);
                
                // 参数不全会导致接口失败
                String source = "mweb_me_my_watchlist-item_list-null";
                Map<String, Object> element = new HashMap<>();
                Map<String, Object> ctx = new HashMap<>();
                
                // 使用自定义的 OpHelper 调用，集成事件监听
                helperOpWithEvents(view, bean, source, element, ctx);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
    
    private void helperOpWithEvents(CartOpLayout layoutOp, ProductBean bean, String source, Map<String, Object> element, Map<String, Object> ctx) {
        // 创建一个自定义的监听器，集成原有逻辑和事件回调
        CartOpLayout.OnCartOpListener customListener = new CartOpLayout.OnCartOpListener() {
            @Override
            public void operateLeft(View view) {
                // 执行原有的减少逻辑（从 OpHelper 中提取）
                executeLeftOperation(layoutOp, bean, source, element, ctx);
                
                // 延迟发送事件，确保数量更新完成
                view.post(() -> {
                    int currentQuantity = layoutOp.getTextNum();
                    sendQuantityChangeEvent(layoutOp, (ThemedReactContext) layoutOp.getContext(), currentQuantity, bean.getProductId());
                    sendStatusChangeEvent(layoutOp, (ThemedReactContext) layoutOp.getContext(), "decrease", bean.getProductId());
                });
            }

            @Override
            public void operateRight(View view) {
                // 执行原有的增加逻辑（从 OpHelper 中提取）
                executeRightOperation(layoutOp, bean, source, element, ctx);
                
                // 延迟发送事件，确保数量更新完成
                view.post(() -> {
                    int currentQuantity = layoutOp.getTextNum();
                    sendQuantityChangeEvent(layoutOp, (ThemedReactContext) layoutOp.getContext(), currentQuantity, bean.getProductId());
                    sendStatusChangeEvent(layoutOp, (ThemedReactContext) layoutOp.getContext(), "increase", bean.getProductId());
                });
            }

            @Override
            public void onNumClick(View view) {
                // 执行原有的数量点击逻辑
                executeNumClickOperation(layoutOp, bean);
                
                sendStatusChangeEvent(layoutOp, (ThemedReactContext) layoutOp.getContext(), "numClick", bean.getProductId());
            }

            @Override
            public int getProductId() {
                return bean.getProductId();
            }
        };
        
        // 先调用 OpHelper 设置基础状态
        OpHelper.helperOp(layoutOp, bean, bean, source, element, ctx);
        
        // 然后覆盖监听器为我们的自定义监听器
        layoutOp.setOnOperateListener(customListener);
    }

    @Override
    protected ViewManagerDelegate<CartOpLayout> getDelegate() {
        return delegate;
    }

    private final WeeeAtcComponentManagerDelegate<CartOpLayout, ReactAtcButtonManager> delegate =
            new WeeeAtcComponentManagerDelegate<>(this);


    private void initOpLayout(CartOpLayout layoutOp) {
        int factor = 1;
        layoutOp.setTransValue((int) (layoutOp.getTransValue() * factor));

        ConstraintLayout layoutOpView = layoutOp.findViewById(R.id.layout_op_view);
        if (layoutOpView != null) {
            ConstraintLayout.LayoutParams clp = (ConstraintLayout.LayoutParams) layoutOpView.getLayoutParams();
            clp.height = (int) (mCallerContext.getResources().getDimensionPixelOffset(R.dimen.prop_size_atc_mini) * factor);
            layoutOpView.setLayoutParams(clp);

            TextView tvTips = layoutOp.findViewById(R.id.tv_tips);
            if (tvTips != null) {
                layoutOp.setClipChildren(false);
                ConstraintLayout layoutProduct = null; //layoutOpView.findViewById(R.id.layout_product);

                if (layoutProduct != null) layoutProduct.setClipChildren(false);

                layoutOp.setTipsTextSizeFactor(factor);
                TextAppearanceSpan normal = layoutOp.getTipsNormalTextAppearanceSpan();
                layoutOp.setTipsNormalTextAppearanceSpan(new TextAppearanceSpan(
                        normal.getFamily(),
                        normal.getTextStyle(),
                        (int) (normal.getTextSize() * factor),
                        normal.getTextColor(),
                        normal.getLinkTextColor()
                ));
                TextAppearanceSpan bold = layoutOp.getTipsBoldTextAppearanceSpan();
                layoutOp.setTipsBoldTextAppearanceSpan(new TextAppearanceSpan(
                        bold.getFamily(),
                        bold.getTextStyle(),
                        (int) (bold.getTextSize() * factor),
                        bold.getTextColor(),
                        bold.getLinkTextColor()
                ));
            }
        }
    }

    private void setupEventListeners(CartOpLayout opLayout, ThemedReactContext context) {
        // 创建一个包装的监听器，在原有逻辑执行后发送事件
        CartOpLayout.OnCartOpListener originalListener = new CartOpLayout.OnCartOpListener() {
            @Override
            public void operateLeft(View view) {
                // 延迟发送事件，确保数量更新完成
                view.post(() -> {
                    ProductBean bean = (ProductBean) opLayout.getTag(R.id.tag_product_bean);
                    if (bean != null) {
                        int currentQuantity = opLayout.getTextNum();
                        sendQuantityChangeEvent(opLayout, context, currentQuantity, bean.getProductId());
                        sendStatusChangeEvent(opLayout, context, "decrease", bean.getProductId());
                    }
                });
            }

            @Override
            public void operateRight(View view) {
                // 延迟发送事件，确保数量更新完成
                view.post(() -> {
                    ProductBean bean = (ProductBean) opLayout.getTag(R.id.tag_product_bean);
                    if (bean != null) {
                        int currentQuantity = opLayout.getTextNum();
//                        sendQuantityChangeEvent(opLayout, context, currentQuantity, bean.getProductId());
//                        sendStatusChangeEvent(opLayout, context, "increase", bean.getProductId());
                    }
                });
            }

            @Override
            public void onNumClick(View view) {
                ProductBean bean = (ProductBean) opLayout.getTag(R.id.tag_product_bean);
                if (bean != null) {
                    // 执行数量点击的业务逻辑
                    executeNumClickOperation(opLayout, bean);
                    
                    // 延迟发送事件，确保状态更新完成
                    view.post(() -> {
                        int currentQuantity = opLayout.getTextNum();
                        sendQuantityChangeEvent(opLayout, context, currentQuantity, bean.getProductId());
                        sendStatusChangeEvent(opLayout, context, "numClick", bean.getProductId());
                    });
                }
            }

            @Override
            public int getProductId() {
                ProductBean bean = (ProductBean) opLayout.getTag(R.id.tag_product_bean);
                return bean != null ? bean.getProductId() : 0;
            }
        };
        
        // 存储原始监听器引用，以便与现有的 OpHelper 逻辑配合
        opLayout.setTag(R.id.tag_rn_event_listener, originalListener);
    }

    private void sendQuantityChangeEvent(CartOpLayout view, ThemedReactContext context, int quantity, int productId) {
        WritableMap event = Arguments.createMap();
        event.putInt("quantity", quantity);
        event.putInt("productId", productId);
        
        context.getJSModule(RCTEventEmitter.class).receiveEvent(
            view.getId(),
            "onQuantityChange",
            event
        );
    }

    private void sendStatusChangeEvent(CartOpLayout view, ThemedReactContext context, String status, int productId) {
        WritableMap event = Arguments.createMap();
        event.putString("status", status);
        event.putInt("productId", productId);
        
        context.getJSModule(RCTEventEmitter.class).receiveEvent(
            view.getId(),
            "onStatusChange",
            event
        );
    }

    @Override
    public Map<String, Object> getExportedCustomDirectEventTypeConstants() {
        Map<String, Object> export = super.getExportedCustomDirectEventTypeConstants();
        if (export == null) {
            export = new HashMap<>();
        }
        
        Map<String, String> quantityChangeEvent = new HashMap<>();
        quantityChangeEvent.put("registrationName", "onQuantityChange");
        export.put("onQuantityChange", quantityChangeEvent);
        
        Map<String, String> statusChangeEvent = new HashMap<>();
        statusChangeEvent.put("registrationName", "onStatusChange");
        export.put("onStatusChange", statusChangeEvent);
        
        return export;
    }

    /**
     * 执行左侧操作（减少数量或删除）
     */
    private void executeLeftOperation(CartOpLayout layoutOp, ProductBean bean, String source, Map<String, Object> element, Map<String, Object> ctx) {
        SimplePreOrderBean.ItemsBean simpleOrderItem = OrderManager.get().getSimpleOrderItem(bean.id, bean.product_key);
        int lastNum = simpleOrderItem != null ? simpleOrderItem.quantity : 0;
        int volumeThreshold = bean.getVolumeThreshold();
        
        // 计算新数量
        int num = OrderHelper.editNum(false, lastNum, bean.min_order_quantity, bean.getOrderMaxQuantity(), volumeThreshold);
        
        // 显示价格提示
        if (lastNum == volumeThreshold && lastNum - num == 1) {
            double diff = DecimalTools.subtract(bean.volume_price, bean.price);
            layoutOp.showVolumePriceTips(diff, volumeThreshold);
        }
        
        // 更新UI状态
        layoutOp.setOpStyle(num, bean.min_order_quantity, bean.getOrderMaxQuantity());
        layoutOp.setTextNum(num, true);
        
        // 提交购物车更新
        String newSource = buildNewSource(element, bean);
        OrderManager.get().setProductChanged(bean.id, num, source, bean.getProductType(), bean.getReferValue(), bean.product_key, null, newSource);
        
        // 记录分析事件
        logCartAction(element, ctx, bean, lastNum, num, source);
    }

    /**
     * 执行右侧操作（增加数量）
     */
    private void executeRightOperation(CartOpLayout layoutOp, ProductBean bean, String source, Map<String, Object> element, Map<String, Object> ctx) {
        SimplePreOrderBean.ItemsBean simpleOrderItem = OrderManager.get().getSimpleOrderItem(bean.getProductId(), bean.getProductKey());
        int lastNum = simpleOrderItem != null ? simpleOrderItem.quantity : 0;
        int volumeThreshold = bean.getVolumeThreshold();
        boolean showVolume = volumeThreshold > 0;
        
        // 计算新数量
        int num = OrderHelper.editNum(true, lastNum, bean.min_order_quantity, bean.getOrderMaxQuantity(), volumeThreshold);
        
        // 通知其他组件状态变化
        SharedOrderViewModel.get().productOpStatusData.postValue(bean.getProductId());
        
        // 更新UI状态
        layoutOp.setOpStyle(num, bean.min_order_quantity, bean.getOrderMaxQuantity());
        
        boolean reachedMaxNum = lastNum > 0 && lastNum == num;
        if (reachedMaxNum) {
            layoutOp.setTextNum(num, true);
            layoutOp.showReachedTips();
        } else {
            if (lastNum <= 0 && num > 1) {
                layoutOp.setTextNum(num, true);
                if (!showVolume) {
                    layoutOp.showMinPurchaseTips(num);
                }
            } else {
                layoutOp.setTextNum(num, true);
                // 显示首次添加介绍（酒类、Pantry等）
                OpHelper.showFirstAddIntroduce(layoutOp.getContext(), layoutOp, bean, lastNum, num, 5);
            }
            
            // 提交购物车更新
            String newSource = buildNewSource(element, bean);
            OrderManager.get().setProductChanged(bean.id, num, source, bean.getProductType(), bean.getReferValue(), bean.product_key, null, newSource);
            
            // 记录分析事件
            logCartAction(element, ctx, bean, lastNum, num, source);
        }
    }

    /**
     * 执行数量点击操作
     */
    private void executeNumClickOperation(CartOpLayout layoutOp, ProductBean bean) {
        SimplePreOrderBean.ItemsBean simpleOrderItem = OrderManager.get().getSimpleOrderItem(bean.getProductId(), bean.getProductKey());
        int num = simpleOrderItem != null ? simpleOrderItem.quantity : 0;
        
        if (num > 0) {
            // 如果已有数量，则展开编辑界面
            SharedOrderViewModel.get().productOpStatusData.postValue(bean.getProductId());
            layoutOp.setOpStyle(num, bean.min_order_quantity, bean.getOrderMaxQuantity());
            layoutOp.expandWithAnim();
        } else {
            // 如果数量为0，则执行增加操作（相当于点击加号）
            String source = "rn_atc_numclick";
            Map<String, Object> element = new HashMap<>();
            Map<String, Object> ctx = new HashMap<>();
            executeRightOperation(layoutOp, bean, source, element, ctx);
        }
    }

    /**
     * 构建新的来源标识
     */
    private String buildNewSource(Map<String, Object> element, ProductBean bean) {
        // 这里可以根据具体需求构建来源标识
        // 暂时返回默认值
        return "rn_atc_component";
    }

    /**
     * 记录购物车操作分析事件
     */
    private void logCartAction(Map<String, Object> element, Map<String, Object> ctx, ProductBean bean, int lastNum, int newNum, String source) {
        // 这里可以添加具体的分析事件记录逻辑
        // 例如 Firebase Analytics, 自定义埋点等
        try {
            // 示例：记录操作类型
            String action = newNum > lastNum ? "add_to_cart" : (newNum == 0 ? "remove_from_cart" : "decrease_quantity");
            
            // 可以在这里添加具体的埋点逻辑
            // AppAnalytics.track(action, productId, quantity, etc.)
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
