import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
//import HttpApi from 'i18next-http-backend';
import resourcesToBackend from 'i18next-resources-to-backend';


// import translations from './locales/en/common.json';

const isChannelLatino = process.env.CHANNEL === 'latino';

const locales = isChannelLatino
  ? ['en', 'pt', 'es', 'default-ln']
  : ['zht', 'en', 'zh', 'ko', 'ja', 'vi', 'default-ln'];

// const resources = translations;
// const availableLanguages = Object.keys(resources);
// const availableNamespaces = availableLanguages.length > 0 
//   ? Object.keys(resources[availableLanguages[0]]) 
//   : [];

let languageCode = 'en';
// if (Array.isArray(locales) && locales.length > 0) {
//   languageCode = locales[0].languageCode;
// }

const context = require.context(
  './locales',
  true,        // 是否扫描子目录
  /\.json$/   // 文件匹配的正则表达式
);

const translations = {};
context.keys().forEach((key) => {
  // key 的格式是 './en/common.json'
  const [lang, namespace] = key.substring(2).split('/');
  if (!translations[lang]) {
    translations[lang] = {};
  }
  translations[lang][namespace.replace('.json', '')] = context(key);
  // const path = './locales/${key}';
  // translations[lang][namespace.replace('.json', '')] = () => import('./locales/${key}');
});

i18n
  //.use(HttpApi)
  .use(initReactI18next)
  // .use(resourcesToBackend((language, namespace, callback) => {
  //   if (!translations[language] || !translations[language][namespace]) {
  //     return callback(new Error(`Translation not found for lang: ${language}, ns: ${namespace}`), null);
  //   }
    
  //  translations[language][namespace]()
  //     .then((resources) => {
  //       callback(null, resources);
  //     })
  //     .catch((error) => {
  //       callback(error, null);
  //     });
  // }))
  .on('failedLoading', (lng, ns, msg) => console.error(msg))

  .init({
    compatibilityJSON: 'v3',
    lng: languageCode,
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false,
    },
    ns: ['common'],
    defaultNS: 'common',
    resources: translations,
    // resources,
    // resources: {
    //   en: {
    //     common: translations
    //   }
    // },
    // backend: {
    //   loadPath: '/locales/{{lng}}/{{ns}}.json',
    // },
    debug: true,
  });

export default i18n;
