{"system_error": "System is busy please try again later", "max_order_quantity_limit": "<PERSON>ty Limit Reached", "min_order_quantity_limit": "<PERSON><PERSON> {{count}}", "product_view_min_quantity": "Minimum purchase: {{num}} (${{price}} total)", "referral_invited": "Invited", "referral_purchased": "Purchased", "purchased": "Purchased", "change_other_day": "Change Delivery Date", "sold_out_remind": "Notify me", "sold_out_reminded": "Notification set", "sold_out": "Sold Out", "enter_zipcode": "Please enter your delivery zipcode", "welcome": "Welcome to Weee!", "all": "All", "warning": "Warning", "checkout": "Checkout", "zipcode_placeholder": "Enter your zipcode", "share_copy_success": "Link copied!", "share_fb": "Facebook", "share_copy_link": "Copy link", "sms": "SMS", "share_wechat_session": "Wechat", "share_save_image": "Get Image", "share_wechat_moment": "Moments", "share_more": "More", "share_messager": "<PERSON>", "share_whatsapp": "Whatsapp", "sorry": "Page not found", "page_not_found": "Sorry, we can't find the page you're looking for.", "page_not_found_top_x": "Page not found", "page_inner_error_title": "Something went wrong", "page_inner_error_desc": "Don't worry. We are currently looking into the issue. \nPlease try again in few minutes.", "page_inner_error_btn": "TRY AGAIN", "page_fail_to_load": "Page failed to load", "page_check_try_again": "Please check your connection or try again later.", "page_fail_try": "Try again", "back_to_weee": "Go back to", "generic_back_nav_short": "Back", "page_server_unavailabe": "System maintenance", "page_server_unavailabe_desc": "We apologize for any inconvenience. Our system is in the process of maintenance and upgrades. We'll be back online shortly. Thank you for your patience and understanding.", "page_server_unavailabe_refresh": "Refresh", "page_load_fail_title": "Page failed to load", "page_load_fail_desc": "Please check your connection or try again later.", "order_soon": "Only {{quantity}} left - order soon", "popup_tip": "Click here to share", "popup_conten": "with friends, groups, or moments", "close": "Close", "remove_product_all": "Changing the delivery date will remove <span>all products</span> from this order.", "remove_product_these": "Changing the delivery date will remove <span>these products</span> from this order.", "ok_button": "Got it!", "confirm": "Confirm", "cancel": "Cancel", "zipcode": "Your Zip Code is not yet in our delivery region", "weeks_ahead": "Weeks ahead", "weeks_next": "Next week", "this_week": "This week", "delivery_date": "Delivery date", "delivery_window": "Delivery Window", "comingsoon": "Coming Soon to Your Area", "empty_desc": "Meanwhile, you can always order other exciting products from Weee!", "expore_other_product": "Explore Other Products", "share": "Share", "submit": "Submit", "more": "View More", "search_history": "Search history", "popular_search": "Popular search", "result_count_found": "{{productTotal}} result found", "results_count_found": "{{productTotal}} results found", "card_bundle_tag": "Available in bundle", "card_hotdish_tag": "Available in Restaurant To-Go", "search_placeholder": "Search", "search_placeholder_hot": "Search", "search_all": "All", "search_empty_tip": "Can't find what you want? Let us know.", "search_empty": "Sorry, no results for {{keyword}}", "no_result_keyword_message": "Check the spelling or try searching for something more general.", "search_result_recommend__message": "New products added weekly based on customer requests.", "feedback_modal_title": "Tell us what you want!", "feedback_item_name": "<PERSON><PERSON>'s Name", "feedback_item_name_placeholder": "Raw Honeycomb", "feedback_item_info": "Additional Information(Optional)", "feedback_item_info_placeholder": "e.g. Tell us more details such as the category or brand name.", "feedback_item_photo": "Add a Photo(Optional)", "no_results_feedback_thank_you": "Thank you!", "no_results_feedback_content1": "We'll let you know\nwhen this item is here!", "no_results_feedback_continue_shopping": "Continue Shopping", "bought_before": "Bought before", "weekly_sold": "Weekly sold {{soldCount}}", "details": "Product Details", "common_details": "Details", "dietary_needs": "Dietary Needs:", "detail_description": "Description", "account_reviews": "Reviews", "sold_count": "{{soldCount}} Sold", "remaining_count": "{{remainCount}} Left", "btn_view": "View", "remain_count": "Only {{remainCount}} Left", "also_like": "You might also like", "lightning_deals": "Lightning Deals", "lightning_deals_title": "Lightning Deals", "begin_in": "Starts in ", "end_in": "Ends in ", "lightning_going_title": "Available now", "lightning_coming_title": "Coming soon", "lightning_deals_sold": " Sold {{progress}}%", "lightning_deals_empty_title": "Missed it!", "lightning_deals_empty_desc": "Check out what else is new and on sale today.", "lightning_deals_empty_btn": "Back to homepage", "add_cart_count": "{{count}} in cart", "add_to_cart": "Add to cart", "pdp_remind_me_message": "Sorry, this product is sold out. Set an in stock alert.", "pdp_reminded_message": "Added to My List", "pdp_reminded_message_view": "View", "pdp_purchased_message": "Sorry, you have reached the purchase limit of this item.", "pdp_change_date_message": "Sorry, this product isn't available for purchase on the current date.", "pdp_area_unavailable_message": "Sorry, this item is currently unavailable in your area.", "favorite": "favorite", "no_more_data": "No more items to show", "email": "Email", "new_user_email_collect_confirm": "<PERSON><PERSON><PERSON>", "new_user_email_collect_email": "Enter Email address", "new_user_email_collect_cancel_btn": "Continue without offer", "new_user_email_collect_bottom_message": "*Exclusively for first time customers", "new_user_email_collect_success_title": "Success!", "new_user_email_collect_success_mess": "Your coupon will be automatically applied at checkout with this email address.", "new_user_email_collect_success_btn": "Shop Now", "free_shipping_activity": "Free Shipping", "end_of_the_list": "End of the list", "no_result": "No Result", "no_result_filter_message": "Adjust your filters to see more", "tell_us_what_you_want": "Tell us what you want!", "search_placeholder1": "What are you looking for?", "reset": "Reset", "sort": "Sort", "filter": "Filter", "sort_and_filter": "Sort & Filter", "search_input_placeholder": "What are you looking for?", "sf_reset": "Reset", "sf_apply": "Apply", "sf_sort": "Sort", "sf_filter": "Filter", "sf_sort_and_filter": "Sort & Filter", "sf_no_results": "No matches found", "sf_adjust_your_filters_to_see_more": "Try adjusting filters or exploring other categories.", "reply_text": "Reply", "categories": "Categories", "go_to_cart": "Go to cart", "footer_home": "Home", "footer_categories": "Explore", "footer_post": "Community", "footer_cart": "<PERSON><PERSON>", "account_title": "Account", "10th_year_story": "10-Year Story", "footer_like": "Like", "footer_comment": "Comment", "footer_share": "Share", "comment_no_empty": "Comment box cannot be empty", "comment_leave_comment": "Leave your comments", "btn_post": "Post", "btn_show_original": "See Original", "btn_translate": "Translate", "common_today": "Today", "common_tomorrow": "Tmw", "common_view": "View", "common_copy": "Copy", "common_cancel": "Cancel", "common_delete": "Delete", "common_featured": "Featured", "common_oops": "Oops", "common_nothing": "There's nothing yet.", "comment_title": "Comments", "comment_reply": "Reply", "comment_fail_toast": "Something went wrong, please try again.", "comment_delete_tip": "Are you sure you want to delete this comment?", "common_no_data": "No more items to show", "common_collapse": "Collapse", "comment_show_more_replies": "View More Replies", "comment_show_num_replies": "View {{num}} Replies", "common_search_placeholder": "What are you looking for?", "delivery_address": "Delivery Address", "tabbar_item_buy_again": "Buy again", "tabbar_item_home": "Home", "tabbar_item_explore": "Explore", "tabbar_item_restaurant": "Restaurant", "tabbar_item_global": "Global+", "tabbar_item_deals": "Deals", "tabbar_item_menu": "<PERSON><PERSON>", "tabbar_item_community": "Inspiration", "tabbar_item_me": "Account", "enter_a_zipcode": "Enter a Zip Code", "claimed": "{{progress}}% claimed", "cart_empty_title": "Your cart is empty", "cart_empty_desc": "You don't have any items in your cart. Let's get shopping!", "unavailable_product": "Unavailable Product", "clear_all": "Clear All", "clear_invalid_title": "Clear unavailable product", "clear_invalid_desc": "Are you sure you want to remove everything from this area? This is not reversible.", "show_more": "Show More", "show_less": "Show Less", "cart_qty": {"0": "{{count}} item", "one": "{{count}} item", "other": "{{count}} items"}, "share_image_popup_title": "Press and hold screen to save image", "share_image_popup_footer": "Asian Groceries", "share_referral_coupon_subtitle": "From your friend<div></div>", "share_referral_coupon_detail": "$20 OFF across your first<br/>two orders", "common_author": "Author", "comment_delete_success_tips": "Successfully deleted", "discount_countdown": "Enjoy your <span>{{discount}}% OFF</span> discount with <span>{{countDown}}</span>", "unavailable_product_in_cart": "Unavailable", "Change_Date": "Change Date", "cart_go_to": "Go to Cart", "cart_grocery_cart": "Grocery Cart", "cart_shipping_cart": "<PERSON><PERSON>", "cart_bundle_cart": "Bundle Cart", "cart_restaurant_cart": "Restaurant Cart", "cart_delivery_date": "Delivery date", "cart_qty_total": {"0": "{{count}} item", "one": "{{count}} item", "other": "{{count}} items"}, "weeks_preSale": "Pre-order", "you_can_toggle_between_grid_or_list_view_for_your_preferred_browsing_style": "You can toggle between grid or list view for your preferred browsing style.", "find_exactly_what_you_want_with_our_NEW_sort_and_filter_feature": "Find exactly what you want with our NEW sort and filter feature!", "got_it": "Got it", "checkout_delivery_info": "Delivery Info", "checkout_shipping_info": "Shipping Info", "checkout_pay_method": "Payment Method", "checkout_apply_coupon": "Apply Coupon", "checout_delivery_tip": "Delivery Tip<br/><span>(100% of tip goes directly to your driver)</span>", "checkout_purchase_summary": "Purchase Summary", "checkout_pickup_info": "Pickup Info", "checkout_pickup_date_title": "Pickup Time", "order_success_pickup_date_title": "Pickup Time", "checkout_pickup_address_title": "Pickup Address", "checkout_contact_info": "Contact Info", "checkout_contact_info_tip": "Please fill in your contact information", "checkout_contact_name": "Name", "checkout_contact_btn": "Update", "checkout_contact_first_name": "First Name", "checkout_contact_last_name": "Last Name", "checkout_contact_phone": "Phone Number", "checkout_contact_email": "Email", "shipping_method": "Shipping Method", "checkout_estimated_arrival": "Estimated Arrival: ", "estimated_arrival": "ETA ", "order_success_estimated_arrival": "Estimated Arrival ", "order_success_local_delivery_desc": "Estimated delivery window will be provided the day before delivery.", "address_commit_empty": "There are no shipping instructions with this order. (Optional)", "address_form_option_desc2": "*Instruction only applicable to local delivery service.", "unable_change_address_title": "Same-day delivery already scheduled", "unable_change_address_message": "Sorry! You already have a delivery scheduled at another address. You can't add/change the address on the same date.", "unable_change_address_title_shipping": "Unable to change the delivery address", "unable_change_address_message_shipping": "The address cannot be changed temporarily due to unshipped orders.", "unable_change_address_btn_confirm": "Change Dleivery Date", "unable_change_address_btn_cancel": "Back", "change_address_title": "Confirm Address Change", "use_address_title": "Update delivery address?", "use_address_message": "Do you also want to update your current delivery address to: ", "use_address_confirm_btn": "Update Now", "use_address_cancel_btn": "Not Now", "btn_add_address": "Add a New Address", "select_from_my_addresses": "Select from My Addresses", "current_elivery_address": "Current Delivery Address", "address_book": "Address Book", "change_date_effect_products": "Changing the delivery date will remove these products from this order.", "change_date_effect_all_products": "Changing the delivery date will remove <strong>all products</strong> from this order.", "checkout_earn_points": "Load your Weee! Points today, and collect <span>${{points}}</span> bonus instantly!", "checkout_load_points": "Load your Weee! Points today, and upgrade to <span>{{level}} Rewards</span> instantly!", "checkout_new_user_reward_title": "🙌 Welcome reward ", "checkout_new_user_reward_mess": "You'll earn {{reward}}pts once this order is delivered.", "place_order_terms": "By placing this order, you are agreeing to Weee!'s <a>Terms and Conditions.</a>", "place_warning": "Please bring your items inside on the day of arrival to ensure perishables and frozen items remain fresh.", "checkout_vip_trial_btn": "Enroll Now", "checkout_vip_trial_active": "Your free VIP Pass is activated!", "vip_trial_popup_points_title": "5% Back in Points", "vip_trial_popup_points_desc1": "5% for all orders at Weee!", "vip_trial_popup_delivery_fee_title": "Free Delivery", "vip_trial_popup_delivery_fee_desc1": "2X every week for all orders on Weee! no minimum required", "vip_trial_popup_sub_title": "Enjoy 5% back, free delivery, and more", "vip_trial_popup_title_free": "Free 1-month VIP trial", "vip_trial_popup_subtitle": "Unlock extra savings as a Weee! VIP", "vip_trial_popup_tip": "No auto-renew, no hidden fees", "vip_trial_popup_expire_time": "This offer expires on {{time}}", "vip_trial_popup_terms": "By clicking the button, you agree to our <a href='/help/view/m58ar'>Terms and Conditions</a>.", "vip_trial_popup_expire_confirm": "Claim my Free VIP Pass", "vip_trial_popup_expire_enroll": "Enroll VIP now", "vip_trial_active": "Your Free VIP Pass is now activated", "vip_trial_active_subtitle": "Enjoy your 1-month VIP benefits <br/><span>Valid {{time}}</span>", "vip_trial_back": "Back to My VIP", "checkout_apply_coupon_label": "Please select or enter coupon code", "checkout_applied_label": "APPLIED", "checkout_products": "Order Products ({{count}})", "checkout_address_error_dialog_update": "Update address", "checkout_address_error_dialog_modify_cart": "Modify cart", "subtotal_item": "Subtotal ({{count}})", "subtotal_qty": {"0": "{{count}} item", "one": "{{count}} item", "other": "{{count}} items"}, "subtotal_giftcard_qty": {"1": "{{count}} Card", "other": "{{count}} Cards"}, "gift_card_checkout_scheduled_date": "Scheduled date:", "gift_card_checkout_amount": "Amount:", "gift_card_checkout_note": "Note:", "gift_card_intro_title": "Weee! digital gift cards", "gift_card_intro_desc": "Show your appreciation in multiple languages with Weee! digital gift cards!", "gift_card_intro_confirm": "Got it", "preview": "Preview", "sent_you_gift_card": "{{name}} sent you a Weee! Gift Card!", "sent_you_gift_cards": "{{name}} sent you <PERSON>ee! Gift Cards!", "gift_card_reach_limit_toast": "You have reached the maximum of purchasing digital points and gift card products today.", "gift_card_total": "{{num}} gift cards total", "coupon_discount": "Coupon Discount", "taxes": "Taxes", "delivery": "Delivery Fee", "shipping": "Shipping", "porder_order_shipping_free": "Hooray! You've got free delivery on this order!", "free_tag": "FREE", "gift": "Gift", "special": "Special", "tip": "Tip", "total": "Total", "points_deduction": "Weee! Points deduction", "final_payment_amount": "Final payment amount", "others_payment": "Other", "checkout_payment_end_in": "Ending in {{num}}", "checkout_payment_card_end_in": "Ending in <strong>{{num}}</strong>", "checkout_payment_card_expires": "Expires <strong>{{time}}</strong>", "purchase": "Purchase", "error_delivery_contact_info": "Please fill all the required fields.", "error_payment_info": "Please fill all the required fields.", "checkout_confirm_address_title": "Current address", "checkout_confirm_address_message1": "Default address is set to ", "checkout_confirm_address_message2": " based on your most recent delivery. For changes, please use “Delivery Address.”", "place_select_address": "Please enter your delivery address and contact information", "checkout_apply_coupon_tip": "select the coupon you want to apply!", "checkout_apply_coupon_btn": "Redeem", "checkout_apply_coupon_clear_btn": "Skip coupon selection", "checkout_apply_other_coupon_label": "Have another <span>coupon code</span>?", "checkout_apply_coupon_placeholder": "Enter code to redeem a coupon", "checkout_apply_coupon_expire": "Expires on: ", "checkout_apply_coupon_code": "Code: ", "raffle_title": "Lucky Prizes", "raffle_entries": "My Entry Tickets", "raffle_tip": "Winners will be selected on 1/31/2023", "raffle_entry_btn": "Get An Entry Now!", "raffle_lottery_number": "Lottery Number", "raffle_order_number": "Order Number", "raffle_share_success_msg": "Successfully shared! You've earned a bonus raffle entry.", "raffle_rule_more": "Online Sweepstakes Official Rules", "raffle_event_content": "June 24 - July 4", "raffle_event_description": "Winners will be announced on 11/18/2022", "raffle_howtowin_content": "Place an order over $68", "raffle_howtowin_description": "Earn more tickets to secure higher chances of winning", "raffle_lotteryentry_content": "Gain 1 entry ticket each time you<br /> place an order of $68 or more", "raffle_tab_rule": "DETAILS", "raffle_tab_winner": "Winners", "raffle1_rule_1": "From 1/13 12:00 AM PST to 1/29 11:59 PM PST, you will gain an entry ticket each time you place an order of $68 or more.", "raffle1_rule_2": "You can also gain an entry ticket by completing and submitting this <a href='https://docs.google.com/forms/d/1JkpovSxI88pBB7VgX1xzMOwZEWtzQCS1gdfjfgcn6uM'>form</a>. Limit one entry ticket per customer.", "raffle1_rule_3": "On 1/31, Weee! will draw winners from valid entries.", "raffle1_rule_4": "Prizes are as follows: ", "raffle1_rule_5": "Winners will be announced on this page and will be notified by email and push notifications. Please fill in the correct contact information to receive your prize.", "raffle1_rule_6": "Weee! reserves the right to modify or interpret the terms of this event.", "raffle1_rule_prize": "<li><span>Diamond Prize x 1</span>All-inclusive flight, hotel and Disneyland® Resort Theme Park Tickets for 4 people for 3 nights</li><li><span>Gold Prize x 2</span>Two 2-Day Disneyland® Resort Theme Park Tickets</li>", "raffle2_rule_1": "1. From 7/29 12:00 AM PST to 8/18 11:59 PM PST, get a giveaway entry by placing any order of $68 or more. Place more orders to get more tickets. Limit one giveaway entry per order.", "raffle2_rule_2": "2. You can also get a giveaway entry by completing and submitting this <a href='https://forms.gle/hbdyT3yd7mhbzx5MA'>form</a>. Limit one giveaway entry per email. Limit one email entry per customer.", "raffle2_rule_3": "3. On 8/22, <PERSON><PERSON>! will draw winners from valid entries. Prizes are as follows:<br/>Order entry only:", "raffle2_rule_4": "4. Winner will be announced on this page and will be notified by email, push notification & SMS. Please fill in the correct contact information for us to reach out.", "raffle2_rule_5": "5. <PERSON><PERSON>! reserves the right to modify or interpret the terms of this event.", "raffle2_rule_prize": "1. Gold Prize - 1 winners will receive Buydeem All-in-One 5 Quart Food Steamer with 2 Stew Pots.<br/>2. Silver Prize - 3 winners will each receive a Buydeem Mini Kettle Cooker.<br/>3. Bronze Prize - 5 winners will each receive a Buydeem 2-Slice Toaster.<br/>4. Lucky Prize: 10 Weee! Gift Card valued at $20 (Weee! points)<br/>5. Form entry only: 10 winners will each receive $20 in Weee! Points.", "raffle_rule_official_rules": "Official rule >", "raffle_rule_learn_more": "Learn More", "raffle_rule_official_link1": "https://www.sayweee.com/article/view/bnmqt", "raffle_rule_official_link2": "https://www.sayweee.com/article/view/qy3nh?t=165842385650", "raffle_prize_diamond": "Diamond Prize", "raffle_prize_gold": "Gold Prize", "raffle_prize_sliver": "Sliver Prize", "raffle_prize_bronze": "Bronze Prize", "raffle_prize_lucky": "Lucky Prize", "raffle_time": "The Lottery draw is on 11/18/2022", "raffle_email_prize_lucky": "Form entry (only for users who fill out the form to claim the lottery ticket)", "raffle_email_winner": "Email Address", "use_weee_points": "Use Weee! Points", "have_points": " You have <strong>{{points}}</strong> points, worth <strong>${{money}}</strong>", "add_card_zipcode": "Zip code", "cvc": "CVC", "m_y": "MM / YY", "card_number": "Card Number", "pay_method": "Payment Method", "add_payment_method": "Add new payment method", "add_card_title": "Credit or Debit Card", "is_expired": "Expired", "payment_add_card": "Add new card", "add_card": "Add Card", "add_address_btn": "Add a New Address", "or": "Or", "save": "Save", "checkout_delivery_date": "Delivery Date & Time", "checkout_pickup_date": "Pickup Date", "ok": "OK", "next": "Next", "next_question": "Next", "previous_question": "Previous", "pre_sell": "Pre-sale", "invalid_number": "Please enter a valid Card Number.", "incomplete_number": "Please enter a valid Card Number.", "card_declined": "Please enter a valid Card Number.", "invalid_expiry_year_past": "Please enter a valid Card Expiration.", "incomplete_expiry": "Please enter a valid Card Expiration.", "incomplete_cvc": "Please enter a valid Card CVC.", "valid_zipcode": "Please enter a valid Zip code.", "card_error": "There was an error processing your card -- try again in a few seconds", "has_add_notify_after_3_min": "Set successfully, we will remind you 3 minutes before the start", "notify_cancel": "TURN OFF", "others_tip": "Other", "continue": "Continue", "unavailable": "(Unavailable)", "select_time": "Select Window", "cart_valid_to_invalid_title": {"1": "Sorry, this item is out-of-stock", "other": "Sorry, these items are out-of-stock"}, "cart_valid_to_invalid_desc": {"1": "It's been removed from your cart", "other": "They've been removed from your cart"}, "cart_invalid_to_valid_title": {"1": "This item is back in stock", "other": "These items are back in stock"}, "cart_invalid_to_valid_desc": {"1": "It's been added to your cart", "other": "They've been added to your cart"}, "cart_item_diff_btn": "OK", "cart_item_diff_show_invalid": "View more product detail", "cart_bogo_save": "You Save:", "pantry_intro_content": "<div>Access an even <b>BIGGER</b> selection of popular, shelf-stable essentials. </div><div>Find{{logo}}items easily and start shopping!</div>", "pantry_intro_suppl": "<div>*${{freeFee}} minimum for free shipping, calculated separately from local delivery. </div><div>Learn more</div>", "pantry_intro_suppl_free": "<div>*Free shipping, calculated separately from local delivery. </div><div>Learn more</div>", "pantry_faq": "Pantry+ FAQ", "pantry_faq_title1": "How is Pantry+ different?", "pantry_faq_content1": "Pantry+ is accessible nationwide and offers an even bigger selection of popular products, even if fresh delivery isn't available in your area.", "pantry_faq_title2": "Where do you ship?", "pantry_faq_content2": "We ship nationwide daily, on the same day or next day (excluding HI & AK).", "pantry_faq_title3": "What's the free shipping minimum?", "pantry_faq_content3": "${{freeFee}} (below ${{freeFee}} is ${{fee}} flat).", "pantry_faq_content3_free": "Free shipping, no minimum.", "pantry_faq_title4": "Is there a membership fee?", "pantry_faq_content4": "Nope! Just shop as you normally would. You'll be directed to the Pantry+ cart to complete checkout separately.", "pantry_faq_additional": "Additional questions? Please contact us at {{mail}} or reach out to us on Instagram or Facebook!", "pantry_tip_title": "ships via ground", "pantry_tip_content": "Order ${{freeFee}}+ for free shipping (separate from local delivery)", "my_cart": "My cart", "local_delivery_on": "Local delivery on", "hotdish_pick_up_time": "Pickup", "items_total": "items total:", "free_delivery": "Free delivery", "shipping_fee": "Shipping fee:", "cart_checkout": "Checkout", "add_for_free_shipping": "Add <span>${{free}}</span> for <i>FREE SHIPPING</i>", "cart_ready_checkout": "carts ready to checkout", "product_has_removed": "Product has removed", "undo": "UNDO", "cart_shop_more": "Shop More", "special_price": "Special Price:", "view": "View", "cart_yes": "Yes", "cart_no": "No", "item_total": "{{count}} items total:", "bundle_total": "{{count}} bundle total:", "dishes_total": "{{count}} dishes total:", "dish_total": "{{count}} dish total:", "estimated_arrival:": "Estimated Arrival:", "checkout_title": "Checkout", "apply_to_order": "${{amount}} will apply to this order", "signup": "Sign up", "phone": "Phone", "sign_up_with_mobile": "Sign up with Phone", "sign_up_with_email": "Sign Up with Email", "sign_up_with_social_account": "Or sign up with social account", "continue_with_app": "Shop on Weee! App now", "continue_with_browser": "Continue shopping with browser", "ep_invite_succuess_count": "Great! You've unlocked the ${{amount}} OFF your first order", "explore_thousands": "Explore thousands of exciting Asian groceries now", "email_address": "Email address", "password": "Password", "phone_number": "Phone number", "phone_number_placeholder": "Enter your phone number", "address_err_msg_firstname": "Please input your firstname!", "address_err_msg_lastname": "Please input your lastname!", "address_err_msg_phone": "Please enter a valid phone number", "enter_valid_code": "Enter a valid code", "code_placeholder": "Enter code", "phone_resend_time": "Resend: {{time}} sec", "get_code": "GET CODE", "code_resend": "RESEND", "code_has_resend": "Your code has been re-sent", "phone_call_with_code": "Expect a phone call with your code shortly", "resend_via_sms": "Resend via SMS", "let_weee_call_you": "Let Weee! call you", "continue_with": "or continue with", "haveAccount": "Already have an account?", "login_uppercase": "Log In", "signup_terms": "<p>By signing up. You agree to our <term>Terms of Service</term> & <policy>Privacy Policy</policy></p>", "login": "Log in", "signup_text": "Don't have an account?", "forget_text": "Forgot password?", "email_error_msg": "Please enter a valid email address", "std_email_error_msg": "Please input a valid .edu email address.", "forget_passeord": "Forgot password?", "reset_password_verify": "We'll send you a verification code to reset your password.", "reset_password": "Reset password", "confirm_your_number": "Confirm your number", "enter_digit_code": "Enter the 4-digit code sent to: <email></email>", "not_get_reset_password_mail": "Didn't get an email?", "resend": "Resend", "reset_your_password": "Reset your password", "choose_password_error": "Please choose a new password", "choose_confirm_password_error": "Please re-enter your password to confirm", "new_password": "New password", "confirm_new_password": "Confirm new password", "update_password": "Update password", "connet_account": "Connect my account", "skip": "<PERSON><PERSON>", "create_connet_account": "Create and connect your Weee! account to receive order confirmations and other important updates.", "connect": "Connect", "valid_passoword": "Enter a valid passoword", "create_account": "Create Account", "est_title": "Estimated time of arrival", "cart_type_grocery_mail": "Cart on <eta/><span>{{date}}</span>", "cart_type_grocery": "Grocery Cart on <span>{{date}}</span>", "cart_type_bundle": "Bundle Cart on <span>{{date}}</span>", "cart_type_restaurant": "Restaurant Cart on <span>{{date}}</span>", "cart_type_alcohol": "<PERSON><PERSON> on", "est_title_simple": "ETA <span>{{date}}</span>", "est_title_simple_choose_cart": "ETA", "please_input_same_password": "Please ensure your password was entered correctly", "phone_connect_title": "<b>To Secure your ${{amount}} off coupon</b><br/>please connect your phone number", "multiple_coupon_title_verify": "Connect your phone number and get an extra ${{amount}} OFF!", "phone_connect_referral_tip": "If you skip phone verification, you will not receive this referral coupon.", "phone_connect_tip": "If you skip the phone verification, you will not receive the new user coupon.", "recommend_banner_user": "recommends this collection", "recommend_banner_count": "{{number}} people bought from their recommendation", "recommend_banner_login": "Claim it now", "recommend_banner_user_new": "offers exclusive discount to you!", "recommend_banner_enjoy": "Enjoy <span>${{number}} off</span> your first order.", "recommend_banner_welcome": "Get <span>${{number}} OFF</span> across 2 orders", "pickup_time": "Pickup Time", "copy_address": "Address copied", "pickup_go_back": "Go back", "see_original": "See Original", "see_translation": "See Translation", "video_landing_download_title": "Better experience on app", "video_landing_download_btn": "Open App", "post_related_items": "Items", "post_related_item": "<PERSON><PERSON>", "no_data_btn_text": "Go to home page", "store_confirm_title": "You are about to visit the <strong>{{storeName}} store</strong>", "store_go_old_store": "Stay at the {{storeName}} store", "signup_uppercase": "SIGN UP", "pantry_shipping_fee_free": "Free shipping (separate from local delivery)", "deal_view_order_title": "Order details", "order_group_view": "Group order detail", "order_detail_cooler_by_self_info": "Please remember to leave a cooler at the front door on the day of delivery.", "order_detail_delivery_check_btn": "Delivery issue status", "order_detail_delivery_apply_btn": "Report a delivery issue", "order_detail_shipping_track_text": "Tracking number", "order_as_vip_points": "<span>As a VIP member,</span> you'll earn <span>{{points}}</span> points from this order", "order_vip_earned_points": "Weee! Points earned from this order: {{points}}", "order_vip_will_earn_points": "Weee! Points earned from this order: {{points}}", "order_gbuy_progress": "My progress", "order_gbuy_progress_count_down": "Expires in", "order_details": "Order details", "order_number": "Order number", "order_time": "Order time", "order_sequence": "Order sequence", "order_total_price": "Order total", "order_create_date": "Order date", "order_payment_method": "Payment method", "order_shipment_method": "Shipment method", "order_delivery_method": "Delivery method", "order_refunded_amount": "Refunded amount", "order_shipping_code": "Shipping code", "order_delivery_code": "Delivery code", "group_leader": "Group leaders", "order_contact_salers": "Contact", "order_pickup_address": "Pickup address", "order_comments": "Seller Instructions", "order_pickup": "Pick up", "order_shipping": "Shipping", "order_delivery": "Delivery", "order_manage": "Manage", "order_pay": "Pay now", "order_buy_again": "Reorder", "order_resend_gift_card": "Resend gift card", "order_resend_gift_card_reach_limit": "You have sent this gift card 5 times. Please contact Customer Support for assistance.", "order_view_refund": "Refund details", "order_post": "Post a Review", "order_post_marketplace": "Post a Review", "order_cancel": "Cancel order", "order_modify_product": "Modify order", "order_customer_support": "Return/Refund", "order_sync_transaction": "transaction", "order_logistics_tracking": "Track items", "order_get_address": "Get Address", "order_mark_completed": "commplete", "order_order_share": "Share my order", "order_continue_shopping": "Continue Shopping", "order_points_gbuys": "Invite friends to buy", "order_relevant": "Relevant orders", "order_refund_log": "Refund details", "order_shipping_info": "Shipping info", "order_pickup_info": "Pickup info", "order_delivery_info": "Delivery info", "order_shipment_mode": "Shipment method", "order_shipping_arrival": "ETA:", "order_shipment_type": "Standard shipping via ground", "order_pickup_date_time": "Pickup date & time:", "order_rtg_pickup_date": "Pickup date", "order_delivery_time": "Delivery window", "order_shipment_address": "Shipment address", "order_rtg_pickup_address": "Pickup your order at:", "order_delivery_address": "Delivery address", "order_delivery_address_edit_btn": "Change", "order_product_info": "Item Info", "order_bundle_product_tag": "Bundle product", "order_product_vip_price": "VIP", "order_product_actual_price": "Item price", "order_product_quantity": "Qty", "order_case_bundle_judgment": "Bundle", "order_case_list_tab_pending": "In progress", "order_case_list_tab_canceled": "Canceled", "order_case_list_tab_finish": "Completed", "order_refund_approved": "Refunded", "order_check_details_btn": "View Details", "order_apply_refund_btn": "Request refund", "order_total_amout": "Total", "order_payment_total_paid": "Amount to be paid", "order_points_deduction": "Weee! Points Applied", "order_unable_refund_title": "Unable to request a refund", "order_unable_refund_got_it": "Got it", "order_unable_refund_customer": "Weee! customer service", "deal_refund_detail": "Refund Details", "delivery_status": "Delivery status", "shipping_status": "Shipping status", "cancel_message": "Are you sure you want to cancel the order?", "buy_again_chose_all": "Select all", "buy_again_add_cart": "Add to cart", "buy_again_chose_product": "Choose items", "buy_again_chosed_delivery_day": "Current delivery date", "buy_again_sold_out_product": "Unavailable items", "reopen_title": "Do you want to reopen?", "reopen_content": "If you continue, your group order will reopen and guests will be able to make changes.", "reopen_ok_btn": "Reopen group order", "reopen_cancel_btn": "Keep the group order locked", "alcohol": "Alcohol", "alcohol_id_needed": "Valid ID needed", "alcohol_reminder1": "Must present a valid ID of 21+ at delivery", "alcohol_reminder2": "Must be 21+ to purchase alcohol and present a valid government-issued photo ID at delivery.", "alcohol_faq_title": "Alcohol FAQ", "alcohol_faq_question_0": "How does <PERSON><PERSON><PERSON>'s alcohol delivery work?", "alcohol_faq_answer_0": "Weee! is proud to offer fast, efficient, and convenient delivery of wine, beer, and liquor in Washington State. Weee! offers scheduled delivery through our delivery drivers. Weee! does not offer mail order delivery for alcohol at this time. You must be at least 21 years old to purchase. Adult signature (i.e., at least 21 years old) with proof of age in the form of a valid government-issued photo ID (e.g. driver's license, passport, state ID card, US military card, tribal enrollment card) is required at the time of delivery. Recipients must not show signs of intoxication.", "alcohol_faq_question_1": "When can I get alcohol delivered through <PERSON><PERSON>!?", "alcohol_faq_answer_1": "Alcohol will be delivered from 5:30p.m. to 9:30p.m. ", "alcohol_faq_question_2": "Do I need to be present for alcohol delivery?", "alcohol_faq_answer_2": "Either you or someone else 21 years of age or older with valid government-issued photo ID showing proof of age must be present to sign for alcohol delivery. ", "alcohol_faq_question_3": "Can someone other than the person that placed the order accept alcohol delivery?", "alcohol_faq_answer_3": "Yes, as long as the individual accepting the alcohol delivery has proof of legal drinking age (i.e., at least 21 years old) in the form of a valid government-issued photo ID (e.g. driver's license, passport, state ID card, US military card).", "alcohol_faq_question_4": "What is the delivery cost?", "alcohol_faq_answer_4": "Delivery cost will be a flat fee of $5. However, if the cost of the products in any alcohol delivery is $35+, delivery will be free!", "alcohol_faq_question_5": "Can I use my Weee! Points to purchase alcohol?", "alcohol_faq_answer_5": "Yes! As long as you or an individual at least 21 years old with a form of a valid government-issued photo ID is present to sign for the order at the time of delivery, Weee! Points may be used for alcohol purchases.", "alcohol_faq_question_6": "Can I receive an alcohol delivery through <PERSON><PERSON>! if I have been drinking?", "alcohol_faq_answer_6": "<PERSON>ee! can complete a delivery to any person who does not show signs of intoxication; this will be at the driver's discretion.  In the event <PERSON><PERSON>! is unable to complete delivery, the alcohol will be returned to our warehouse and you will receive a refund for your order. ", "alcohol_faq_question_7": "Are there any limitations to what type of alcohol I can order in Washington?", "alcohol_faq_answer_7": "Weee! delivers beer, wine, and spirits products in our inventory in compliance with all federal, state, and local regulations. ", "alcohol_faq_question_8": "Where can I receive alcohol by mail-order?", "alcohol_faq_answer_8": "Alcohol is not available for mail order at this time.", "alcohol_faq_question_9": "Why is the delivery person taking a photo of my ID when I accept an alcohol order?", "alcohol_faq_answer_9": "Under state law, we are required to verify and keep records of photo IDs of individuals signing for and receiving alcohol. The photo of your ID will not be stored on the delivery person's mobile device. ", "alcohol_faq_question_10": "Can I get a refund for alcohol purchases?", "alcohol_faq_answer_10": "Once delivered, no refunds for alcohol purchases, except refunds may be issued for broken or damaged products. ", "alcohol_faq_bottom_message": "Additional questions? Please contact us at <a><EMAIL></a> or reach out to us on <em>Instagram</em> or <strong>Facebook</strong>!", "alcohol_agreement_title": "Alcohol Agreement", "alcohol_agreement_agree_btn": "Agree & Continue", "alcohol_agreement_back_btn": "Go Back", "alcohol_agreement_term": "By placing this order, you are agreeing to Weee!'s <a>Alcohol Agreement</a>.", "alcohol_agreement_top_message": "You are purchasing alcohol on Weee!. By continuing, you have agreed to the following:", "alcohol_agreement_para_title_0": "Must be an adult 21+ years of age to purchase and receive alcohol. ", "alcohol_agreement_para_content_0": "Both you and the recipient of alcohol are legally 21 years of age or older.", "alcohol_agreement_para_title_1": "No-contact deliveries are unavailable. ", "alcohol_agreement_para_content_1": "The recipient of alcohol shall present a valid, non-expired government photo ID to driver at delivery.", "alcohol_agreement_para_title_2": "Refund restrictions apply. ", "alcohol_agreement_para_content_2": "Alcohol will be returned to Weee! and you will receive a refund if the recipient is underage or showing signs of intoxication, or no one is available to receive the delivery.", "alcohol_agreement_para_title_3": "Alcohol purchased for personal consumption. ", "alcohol_agreement_para_content_3": "Alcohol purchased from Weee! is intended for personal consumption and shall not be resold.", "alcohol_agreement_para_title_4": "Compliance with law. ", "alcohol_agreement_para_content_4": "You and the recipient of alcohol shall comply with all local, state, and federal law with respect to the consumption of alcohol.", "alcohol_agreement_para_title_5": "Alcohol FAQ and Terms of Service. ", "alcohol_agreement_para_content_5": "You have read the <strong>Alcohol Delivery FAQ</strong> and agree to the Weee! <a>Terms of Service</a> in connection with your purchase of alcohol.", "alcohol_agreement_bottom_message": "Additional questions? Please contact us at <a><EMAIL></a> or reach out to us on <em>Instagram</em> or <strong>Facebook</strong>!", "edit_bio": "Edit <PERSON>io", "edit_bio_placeholder": "Add a short bio to tell people more about yourself. This could be anything like a favorite quote, or your favorite food.", "edit_bio_submit": "Done", "bio_leave_modal_title": "Unsaved changes", "bio_leave_modal_desc": "You have unsaved changes. Are you sure you want to cancel?", "bio_leave_modal_yes": "Yes, cancel", "bio_leave_modal_no": "No", "nps_modal_earn_more_points": "Want to earn more Weee! points?", "nps_modal_survey_and_earn": "Complete the survey and earn <br/> <i>{{points}} Weee! Points</i> instantly!", "nps_modal_other_specify": "Other (please specify)", "nps_modal_please_elaborate": "Please elaborate", "nps_modal_appreciate_feedback": "We appreciate your feedback", "nps_modal_congrats_to_get_points": "Congrats! You've earned {{points}} Weee! Points. Your feedback will help our team improve your Weee! experience.", "nps_confirm_exit_title": "Are you sure you want to exit the survey?", "nps_confirm_exit_sub_title": "After completing this survey, you'll get 100 Weee! Points. ", "nps_confirm_exit_complete": "Complete survey", "nps_confirm_exit_anyway": "Exit anyway", "send_to_weee": "Send to Weee!", "order_success_confirm": "Order Confirmation", "order_success_text": "Thank you for your purchase.", "order_success_order_info_window": "Delivery Window", "order_success_shipping_info_window": "Shipping Window", "order_success_order_info_address": "Delivery Address:", "order_pickup_address_title": "Pickup Address:", "order_success_shipping_info_address": "Shipping address", "order_success_perks_vip_text": "Get <span> ${{value}} </span> back in points from this order as a VIP", "order_success_perks_vip_sub_text": "& enjoy 5% back in pts everytime", "order_success_perks_vip_btn": "Try Free VIP", "order_success_perks_coupon_text": "Enjoy <span> ${{value}} OFF </span> your next Bundle or To-Go Order", "order_success_perks_coupon_text_nobundle": "Enjoy <span> ${{value}} OFF </span> your next To-Go Order", "order_success_perks_coupon_sub_text": "Offer available until", "order_success_perks_coupon_btn": "Order Now", "order_success_perks_invite_text": "Earn <span> ${{value}} </span> in points for inviting a friend", "order_success_perks_invite_text_vip": "Earn <span> ${{value}} </span> in points and 1-month FREE VIP for inviting a friend", "order_success_perks_invite_btn": "Invite Now", "order_success_perks_coupon_text_rtg": "Offer available until {{date}}", "order_success_perks_coupon_text_bundle": "Offer available until {{date}}", "order_success_perks_coupon_btn_sub": "Order Now", "order_success_perks_coupon_text_sub1": "Enjoy <span>${{amount}} OFF</span> your next Restaurant Delivery", "order_success_perks_coupon_text_sub2": "", "order_success_perks_invite_btn_sub": "Invite Now", "order_success_perks_invite_text_sub": "", "order_success_delivery_info_title": "Got everything you need?", "order_success_delivery_info_no_descriton": "Continue to add more to your order for <strong>{{date}}</strong>, Once the total order value is over ${{amount}}, we will refund the delivery fee you paid.", "order_success_shipping_info_no_descriton": "Continue to add more to your order today. Once the total order value is over ${{amount}}, we will deliver it all together for FREE.", "order_success_delivery_info_descriton": "You can still add more to your order for <span>{{date}}</span>. We will deliver it all together for FREE.", "order_success_shipping_info_descriton": "You can still add more to your order today. We will deliver it all together for FREE.", "order_success_delivery_info_btn": "Continue Shopping", "order_success_share_popup_title": "Earn up to <span>${{value}}</span> back by sharing your order!", "order_success_share_popup_total_earns": "{{value}} local customers are already earning rebates with friends", "order_success_share_popup_earned_today": " earned <span>${{value}}</span> today", "order_success_share_popup_btn_ok": "Earn Now!", "order_success_share_popup_btn_cancel": "Maybe Next Time", "order_success_perks_title": "Don't miss these perks", "order_success_order_details": "Order Details", "order_success_thanks_tittle": "Thank you for shopping at Weee!", "order_success_want_more_title": "Want to save more?", "order_success_invite_rebate": "Share products you just bought with friends & <br/><i>earn points back</i>", "order_success_share_rebate": "Up to <i>{{points}} pts (${{amount}}) back</i> from the following items", "order_success_product_length": "{{numbers}} items", "order_success_how_to_get_points": "How does it work?", "order_success_get_points_1": "1. Select items you want to share.", "order_success_get_points_2": "2. Send the link to your friends or groups in social media.", "order_success_get_points_3": "3. Your friends will get up to a 12% off discount once they've claimed the coupon.", "order_success_get_points_4": "4. You'll earn <strong>1~100 pts back</strong> everytime a friend clicks your shared link.", "order_success_get_points_5": "5. You'll earn <strong>{{rebate}}% back</strong> everytime a friend purchases the product you shared.", "order_success_get_points_6": "6. Points can be applied to your next checkout, <strong>100 pts equal to $1</strong>.", "order_success_get_more_points": "Learn more", "order_success_get_rebate_nearby": "<strong>{{numbers}}</strong> Weee customers in your area are sharing and earning points with friends right now!", "order_success_next_select": "Next: Select items", "order_success_weeebates_rules": "Weeebates: T&C", "order_success_more_rules_1": "1. You can choose to share up to 15 items for each order you place.", "order_success_more_rules_2": "2. You will receive up to 100% of the total value of the items you share.", "order_success_more_rules_3": "3. Rebate will be issued immediately in the form of Weee! Points.", "order_success_more_rules_4": "4. <PERSON><PERSON>! reserves the right to interpret this program and rules.", "order_success_more_rules_5": "5. The rights to interpret the event belongs to <PERSON><PERSON>!", "footer_locations": "Locations", "footer_locations_la": "Los Angeles", "footer_locations_sa": "SF Bay Area", "footer_locations_se": "Seattle", "footer_locations_pd": "Portland", "footer_locations_sd": "San Diego", "footer_locations_ny": "New York", "footer_locations_tp": "Tampa", "footer_locations_mia": "Miami", "footer_locations_orl": "Orlando", "footer_locations_hou": "Houston", "footer_locations_was": "Washington", "footer_locations_nj": "New Jersey", "footer_locations_chi": "Chicago", "footer_locations_bos": "Boston", "footer_locations_atl": "Atlanta", "footer_locations_phx": "Phoenix", "footer_locations_dal": "Dallas", "footer_locations_lv": "Las Vegas", "footer_locations_more": "More", "footer_about": "About Weee!", "footer_company": "Our Company", "footer_about_blog": "Blog", "footer_about_careers": "Careers", "footer_about_media": "News & Media", "footer_about_student": "Student Program", "footer_help": "Need Help?", "footer_help_contact": "Contact us", "footer_help_center": "Help Center", "footer_download": "Get the App", "footer_terms": "Terms", "footer_privacy": "Privacy", "footer_accessibility": "Accessibility Statement", "footer_sell_with_us": "Sell with us", "footer_partner_with_us": "Partner with us", "footer_sell_on_weee": "Sell on <img></img>", "menu_new_arrivals": "New arrivals", "menu_trending": "Bestsellers", "refer_friends": "Refer friends, get ${{amount}}", "expires_in": "Expires in", "discover_what": "Discover What's<br/>Hot and New", "real_customer_reviews": "600,000+ real customer reviews!", "exciting_item_you": "Exciting Items You <br/> Won't Find Elsewhere", "taste_thoughtfully_curated": "Taste thoughtfully curated <br/> and delicious foods.", "frequetly_asked_questions": "Frequently asked questions", "log_in": "Log In", "sign_up": "Sign up", "sorry_email_errors": "Sorry, the zip code you entered was invalid", "asian_hispanic": "Asian", "groceries_delivered": "Groceries, Delivered", "start_shopping": "Start shopping", "enter_zip_code": "Enter zip code", "we_offer_the_highest_quality": "We offer the highest quality <br/> groceriesat the best value.", "start_saving": "Start Saving", "free_delivery_no_fees": "Free Delivery. No Fees.", "enjoy_no_subscription_fees": "Enjoy no subscription fees <br/> and a low minimum order.", "shop_on_ourfree_app": "Shop On Our Free App", "weee_is_better_on_our_free_app": "Weee! is better on our free app.<br/> Download and shop today.", "weee_groceries_delivered": "Weee! - Groceries Delivered", "great_quality_and_price": "Great Quality and Price", "everyday_low_price": "Everyday Low Price", "get_good_shipping_time": "Shipping Date:", "order_date": "Order Date:", "shipping_arrival": "ETA", "get_delivery_date": "Delivery date", "totals": "Total", "order_all": "All", "pending": "Pending", "unshipped": "Unshipped", "shipped": "Shipped", "cancelled": "Canceled", "review": "To review", "have_no_order": "No recent orders", "point_earned": "{{point}} points earned", "vip": "VIP", "point_to_earned": "{{point}} points to earn", "no_search_result": "<span>Sorry, no results for {{word}}</span>", "no_search_result_title": "Check the spelling or try searching for something more general.", "alcohol_reminder3": "Must be 21+ and present a valid government-issued photo ID at delivery", "product_view_added_to_affiliate": "Added to affiliate lists", "product_view_add_to_affiliate": "Add to affiliate lists", "product_view_add_to_affiliate_multiple": "<span>Added to</span> multiple lists", "product_view_add_to_affiliate_one": "<span>Added to</span> {{name}}", "product_view_add_to_affiliate_done": "Done", "product_view_create_new_affiliate": "Create", "product_view_add_to_affiliate_empty": "Your list is empty. Let's start by creating some lists.", "bind_phone_coupon_skip_tips": "If you skip the phone verification, you will not receive the new user coupon.", "choose_language": "choose language", "share_wechat_description": "Weee! Asian Grocery Delivery", "invite_friend_popup_tag": "limited Time offer ONLY FOR YOU", "home_us": "About us", "home_term_of_service": "Terms of Service", "home_privacy_policy": "Privacy Policy", "home_accessibility": "Accessibility Statement", "home_dmca": "DMCA", "home_affiliate": "Affiliate Program", "home_business": "Buy for your business", "home_advertisers": "Advertisers", "enter_cvc_number": "Enter CVC number", "confirm_cvc_number": "*For security reasons, please confirm your CVC number.", "please_enter_valid_cvc_number": "Please enter a valid CVC number", "today_order_pickup_at": "Pickup at {{restaurant}}", "alcohol_reminder4": "Must be 21+ and present valid ID", "verify_phone_page_title": "Verify Phone number", "verify_phone_page_tips": "Verify phone number and secure <span>${{amount}} OFF</span> first order", "verify_phone_page_tips_coupon": "Connect your phone number and get an extra ${{amount}} OFF!", "verify_phone_page_verify_btn": "Verify", "verify_phone_page_verify_desc": "Phone verification is needed in order to receive this coupon.", "verify_phone_modal_title": "Thank you!", "verify_phone_modal_subtitle": "Please enjoy ${{amount}} OFF your first order", "verify_phone_modal_coupon_type": "New User Coupon", "verify_phone_modal_btn": "Continue to shopping", "skip_btn": "<PERSON><PERSON>", "delivery_schedule": "Delivery Schedule", "dealpay_subtotal_item": "Subtotal ({{count}})", "dealpay_purchase": "Purchase", "dealpay_order_review_free_delivery": "Free delivery", "dealpay_order_review_shipping_fee": "shipping fee", "dealpay_total": "Total", "dealpay_items": {"one": "{{count}} item", "other": "{{count}} items"}, "dealpay_review_order": "Review order", "dealpay_order_summary": "Order summary", "dealpay_place_order": "Place order", "checkout_review_order": "Review order", "loyalty_delivery_benefits_title": "{{level}} Rewards benefits", "loyalty_delivery_benefits": "{{level}} Rewards members enjoy ${{amount}} off local delivery fee", "loyalty_delivery_service_fee_intro": "Delivery & service fee", "loyalty_new_delivery_fee_intro": "<strong>Delivery fee: </strong>New shoppers enjoy free local delivery for orders over ${{free_shipping_fee}} on their first 5 orders.", "loyalty_bronze_delivery_fee_intro": "<strong>Delivery fee: </strong>Bronze Rewards members enjoy free delivery for orders over ${{free_shipping_fee}}.", "loyalty_silver_delivery_fee_intro": "<strong>Delivery fee: </strong>Silver Rewards members enjoy free delivery for orders over ${{free_shipping_fee}}, and ${{diff_amount}} off for orders below ${{free_shipping_fee}}.", "loyalty_gold_delivery_fee_intro": "<strong>Delivery fee: </strong>Gold Rewards members enjoy free shipping on orders over ${{free_shipping_fee}}, and a ${{diff_amount}} shipping discount on orders below the threshold.", "loyalty_student_delivery_fee_intro": "<strong>Delivery fee: </strong>UC Davis Students enjoy free local deliveries in zip code 95616, 95618 until 06/12.", "loyalty_student_service_fee_intro": "<strong>Service fee: </strong>It helps to offset packaging, handling and other warehouse costs for local deliveries. UC Davis students enjoy no service fee for deliveries in 95616, 95618 until 06/12.", "loyalty_new_service_fee_intro": "<strong>Service fee: </strong>It helps to offset packaging, handling and other warehouse costs for local deliveries. New shoppers enjoy no service fee for their first 5 orders.", "loyalty_bronze_service_fee_intro": "<strong>Service fee: </strong>It helps to offset packaging, handling and other warehouse costs for local deliveries. Bronze Rewards members' orders over ${{free_service_fee}} have the service fee waived.", "loyalty_silver_service_fee_intro": "<strong>Service fee: </strong>It helps to offset packaging, handling and other warehouse costs for local deliveries. Silver Rewards members enjoy a reduced fee. Orders over ${{free_service_fee}} have the service fee waived.", "loyalty_gold_service_fee_intro": "<strong>Service fee: </strong>It helps to offset packaging, handling and other warehouse costs for local deliveries. Gold Rewards members enjoy no service fee for any orders.", "loyalty_thursday_delivery_fee_intro": "<strong>Delivery fee: </strong>{{level}} Rewards members enjoy free local delivery for orders over <span>${{original_free_shipping_fee}}</span> ${{free_shipping_fee}} every Thursday.", "loyalty_thursday_service_fee_intro": "<strong>Service fee: </strong>It helps to offset packaging, handling and other warehouse costs for local deliveries. {{level}} Rewards members enjoy no service fee on any orders every Thursday.", "dealpay_rewards_upgrade": "Save more with <PERSON><PERSON><PERSON>", "dealpay_rewards_upgrade2": "Save even more with <PERSON><PERSON><PERSON>", "dealpay_points_cant_use": "You can't use Weee! points as a payment method for loading Weee points.", "load_money": "Load amount", "upgrade_level": "Upgrade to {{level}} Rewards", "special_tag": "Special", "grouping_subtotal_item": "{{count}} Options", "onboarding_language": "Language", "onboarding_new_banner_title": "Groceries, Delivered.", "fremont_intro_title": "<div></div>Orders fulfilled since 2017", "fremont_intro_title_two": "<div></div>Customer Reviews", "fremont_intro_title_three": "<div></div>App Downloads", "fremont_intro_download_text": "There's more to love in our app.", "fremont_privilege_title_1": "100% Freshness Guarantee", "fremont_privilege_description_1": "Over 300+ types of local, organic, and seasonal produce hand-picked at the peak of freshness, just for you!", "fremont_privilege_link_text_1": "See what's fresh today", "fremont_privilege_title_2": "Memories of Home & Trends from the East", "fremont_privilege_description_2": "Unique treats and gourmet delicacies from Asia to try, share, and enjoy before anyone else! ", "fremont_privilege_link_text_2": "Check out what's trending", "fremont_privilege_title_3": "Dedicated Delivery & Customer Service Teams", "fremont_privilege_description_3": "Guaranteed to be fresh, high-quality, and on-time with fast and friendly customer support.", "fremont_privilege_link_text_3": "Start shopping", "onboarding_active_title": "Get ${{coupon}} Off Your First Purchase", "onboarding_intro_download": "Download:", "onboarding_intro_tutorial_title": "Deliciously Simple", "onboarding_intro_tutorial_sub_title": "3,500+ products at your fingertips.", "fremont_steps_title_one": "Browse & Shop 24/7", "fremont_steps_subtitle_one": "Enjoy thoughtfully curated products anytime, anywhere.", "fremont_steps_title_two": "Always on Your Schedule", "fremont_steps_subtitle_two": "Reserve your delivery day and receive real-time tracking updates. ", "fremont_steps_title_three": "Real Reviews & Recipes", "fremont_steps_subtitle_three": "Discover ingredients by creative home chefs in your community.", "fremont_post_title": "Authentic Flavors and Tasty Inspiration", "fremont_post_sub_title": "Join our community of foodies & home chefs just like you!", "onboarding_fremont_ccpa_privacy_agreement_content": "    CALIFORNIA RESIDENTS: If you would like to exercise any of your state consumer privacy rights, contact us by clicking here: <a1>Do Not Sell My Info</a1>. See our <a2>Privacy Statement</a2> to learn more.", "coupon_shop_more": "Shop More", "address_delivery_title": "Delivery Info", "address_edit_title_receiver_info": "Recipient Name", "address_edit_placeholder_first_name": "First Name", "address_edit_placeholder_last_name": "Last Name", "address_edit_title_contact_info": "Contact Info", "address_edit_receiver_info_tips": "We use your phone number for delivery purposes.", "address_edit_placeholder_pohone": "Phone Number", "address_edit_placeholder_email": "Email", "address_edit_title_address": "Delivery Address", "address_edit_placeholder_street": "Street Address", "address_edit_placeholder_flats": "Unit or Apartment # (Optional)", "address_edit_placeholder_city": "City", "address_edit_placeholder_state": "States", "address_edit_placeholder_zipcode": "Zip", "address_edit_title_notes": "Delivery Instructions (Optional)", "address_edit_tips_notes": "This will help the driver deliver your orders.", "address_edit_placeholder_notes": "e.g. use gate code 1234; beware of dog; ...", "address_edit_btn_save": "Save", "address_edit_btn_update": "Update", "address_remove_title": "Remove Address", "address_remove_message": "Are you sure you'd like to<br/>remove this address? ", "address_remove_confirm": "Remove", "address_remove_cancel": "Back", "address_valid_name": "Please enter the recipient's name", "address_valid_email": "Please enter a valid email address", "address_valid_phone": "Please enter a valid phone number", "event_shop_more": "Shop More", "pdp_headquartered_california": "Bringing smiles to your doorstep from sunny California!", "global_faq": "Global+ FAQ", "global_faq_title1": "How is Global+ different?", "global_faq_content1": "Global+ is accessible nationwide and offers an even bigger selection of popular products, even if fresh delivery isn't available in your area.", "global_faq_title2": "Where do you ship?", "global_faq_content2": "We ship nationwide daily, on the same day or next day (excluding HI & AK).", "global_faq_title3": "What's the free shipping minimum?", "global_faq_content3": "$60 (below $60 is $3.99 flat).", "global_faq_title4": "Is there a membership fee?", "global_faq_content4": "Nope! Just shop as you normally would. You'll be directed to the Global+ cart to complete checkout separately.", "global_faq_additional": "Additional questions? Please contact us at {{mail}} or reach out to us on Instagram or Facebook!", "global_intro": "Introducing <global></global>", "global_intro_content": "Now you can get hard-to-find products from around the world, from the brands you love.", "find_global_easily": "Find <global></global> items easily and start shopping!", "global_intro_supply": "Learn more", "contact_info": "Contact Info", "member_place_warning": "Once placed, this order can not be canceled", "ends_in": "Ends in:", "inviter": "Inviter", "paypal_autobuy_membership": "By confirming, you agree to the Weee! Terms and authorize us to charge your default payment method after your current membership. Your Weee! Club Member membership continues until cancelled. If you do not wish to continue, you may cancel any time by visiting \"My Membership\".", "wechat_pay_tips": "微信支付将根据中国人民银行本日中间价进行美金对人民币的转换，点击“确认支付”后，根据提示，使用微信进行人民币结算。", "wechat_pay": "微信支付", "service_case_completed": "This case is completed", "service_case_canceled": "This case is canceled", "service_cancel_case_btn": "Cancel request", "service_cancel_case_title": "Are you sure?", "service_cancel_case_desc": "You are about to cancel a request. This action cannot be undone.", "service_cancel_case_yes": "Yes, cancel", "service_cancel_case_no": "No, keep it", "service_in_progress": "In progress", "service_completed": "Closed", "service_submit_new_request": "Submit new request", "service_no_request": "You don't have any support request here.", "service_customer_service": "Customer service", "service_what_happened": "What happened?", "service_what_happened_change": "Change", "service_what_happened_placeholder": "Please select", "service_detailed_desc": "Detailed description", "service_detailed_desc_placeholder": "Please describe your issue here", "service_upload_files": "Upload files", "service_upload_files_limit": "Maximum file size: 8 MB", "service_upload": "Upload", "service_email": "Email", "service_email_placeholder": "Please enter your email address", "service_phone": "Phone", "service_phone_placeholder": "Please enter your phone number", "service_submit": "Submit", "service_wrong_file_type": "Sorry, this file format is not supported. Please only upload jpg or png files.", "service_submit_success": "Request submitted! Our customer support representative will get back to you within 12 hours.", "home_help_center": "Help Center", "home_help_user": "FAQ", "common_contact_us": "Contact Us", "home_help_after_type_name": "Return & Refund", "home_customer_service": "Customer Service", "home_advertisement": "Partnership", "home_career": "We're Hiring!", "home_help_supplier": "Supplier Questions", "home_help_after_sale_title": "Quick Return & Refund", "home_help_after_sale_des": "24/7 self-service quick return/refund channel", "home_customer_service_time": "Business hours：6 am to 12 am Pacific time", "home_partners": "Partners", "home_partners_supply": "Suppliers: ", "marketplace_contact_us_label": "Marketplace: ", "home_partners_ad": "Ad: ", "home_partners_restaurant": "Restaurant: ", "home_weee_workable": "Careers", "home_become_driver": "Become a delivery driver", "home_assistance": "Need immediate assistance?", "home_assistance_time": "Business hours：6 am to 12 am Pacific time", "referral_delivery_coupon": "Delivery coupons", "referral_user_remind_text_btn": "Continue", "invite_friends_share_title_step02": "We ship to your area!", "referral_status_fillin": "Zip code", "referral_status_register": "Claim coupon", "referral_status_begin": "Shopping", "referral_receiver_title": "Asian Groceries, delivered.", "referral_landing_title": "Shop Asian groceries <br/>delivered to <span></span>", "share_referral_coupon_remark": "${{amount}} off", "share_referral_coupon_remark_desc": "Across 2 orders", "referral_landing_coupon_inviter": "<strong>{{name}}</strong> sends you an offer", "referral_landing_coupon_desc1": "<span>GET ${{amount}} OFF</span>", "referral_landing_coupon_desc2": "across 2 orders", "referral_landing_claim": "Claim the offer", "invite_friends_share_subtitle_success": "Browse & shop 24/7, read real reviews & recipes. Always on your schedule!", "activity_download_app_btn": "Download App", "invite_friends_popup_title": "We aren't in {{zipcode}} yet.", "invite_friends_popup_subtitle": "Be the first to order! Enter your email to be notified when Weee! is in your area.", "invite_friends_popup_btn": "Remind me", "invite_friends_send_email_success": "<PERSON><PERSON><PERSON> created", "intro_flavoer_food": "Explore a world of flavor", "intro_flavoer_food_desc": "Unique items you won't find elsewhere", "intro_review": "See what our customers love", "intro_review_desc": "{{num}} real customer reviews!", "intro_delivery": "We deliver coast to coast", "intro_delivery_desc": "{{orders}} orders fuifilled since 2017", "intro_delivery_tip": "Currently unavailable in Alaska and Hawaii", "intro_delivery_label1": "Next day fresh grocery <br/>delivery areas", "intro_delivery_label2": "3-5 days non-perishable <br/>delivery across 48 States", "intro_download": "Enjoy shopping anytime, anywhere", "intro_download_desc": "Download our top-rated app", "intro_download_tip": "4.8 stars on {{num}} reviews", "intro_endding_tagline": "Taste life to the fullest with <i></i>", "order_modify_title": "Modify", "order_modify_product_list": "Product list", "order_modify_refunded_amount": "Refunded amount", "order_modify_tip": "${{tip}} tip", "order_modify_cancel_tip": "Cancel tip", "order_modify_cancel_tip_title": "Are you sure you want to cancel tip for this order?", "order_modify_cancel_tip_success": "Tip canceled", "order_modify_cancel_order": "Special discount has applied in this order, so you can only cancel the entire order.", "order_modify_cancel_order_yes": "Cancel entire order", "order_modify_cancel_order_no": "Exit", "order_modify_confirm_modify": "Confirm to update your order?", "order_modify_remove_guaranteed": "Removing the guaranteed delivery items will make your order ineligible for this service. Confirm to update your order?", "order_modify_confirm_modify_no": "Cancel", "order_modify_success": "Your order is updated successfully.", "order_modify_success_content": "Refund amount of ${{refund}} will be returned to your account in 3 to 5 business days. If you have any quetions, please feel free to contact <NAME_EMAIL>", "order_modify_confirm_cancel_order": "Are you sure you want to cancel the order?", "order_modify_consider_again": "Exit", "order_modify_confirm_to_cancel": "Remove", "service_reason_title_1": "What can we help with you today?", "service_reason_title_2": "Tell us what happened with this", "service_detail_placeholder": "Type your response here", "themes_landing_page_title": "Popular Cravings", "themes_check_more": "Explore more", "themes_check_more_themes": "Explore other Popular Cravings", "special_points_event_modal_title": "👋 Hey<br/>Exclusive offer for you!<br/><span class='subtitle'>Only This Week!</span>", "special_points_event_modal_text": "Get up to <span class='red'>$100 Bonus</span>", "special_points_event_modal_text2": "Enroll VIP at <p><a class='red'>($99)</a><span class='red'>$69/year</span></p>", "special_points_event_modal_remark": "when loading points", "special_points_event_modal_remark2": "<li><strong>FREE delivery</strong>, no minimum</li><li><strong>up to 10%</strong> points back!</li>", "special_points_event_modal_confirm": "Learn more!", "special_points_event_modal_cancel": "Maybe later", "my_coupons": "Coupon Box", "valid": "Available", "used": "Used", "expired": "Expired", "free_capital": "FREE", "no_coupons": "No coupons available", "coupon_redeem_placeholder": "Enter your code to redeem your coupon", "coupon_redeem_intro": "Enter your code to redeem your offer. Offers may include discount coupons, rewards memberships, and more.", "coupon_redeem_cancel_title": "Sorry!", "coupon_redeem_cancel_subtitle": "The code you entered is invalid.", "coupon_redeem_cancel_btn": "Okay", "coupon_redeem_success_title": "Congratulations!", "coupon_redeem_success_subtitle": "You are now Gold reward member.", "coupon_redeem_success_btn": "Check your reward status", "until": "Until: ", "use_on": "Use on: ", "current_address": "Current Address:", "suggested_address": "Suggested Address", "original_address": "Original Address", "verify_your_address": "Verify your address", "select_the_most_accurate_address": "Select the most accurate address:", "unable_locate_address": "Unable to locate the address provided in the system", "please_double_check": "Please double-check your information. Once you confirm that it's correct, you'll take responsibility for the delivery.", "continue_and_save": "Save and Continue", "address_edit": "Edit Address", "use_address": "Use Address", "unable_find_aprtment": "Unable to find your apartment number", "apt_unit_info_uncorrect": "The unit number or address is either missing or incorrect. Once you edit and confirm that it's correct, you'll take responsibility for the delivery.", "vender_info_title": "Ships By <em>{{name}}</em>", "vender_info_delivery": "Free shipping over <em>${{free}}</em>", "global_tip_title": "Ships By Vender", "global_tip_content": "Order ${{free}} from the same vender for free shipping", "global_shipping_fee_free": "Free shipping", "pdp_same_brand": "<PERSON><PERSON> Recommended", "sold_by_vender": "Sold by <em>{{vender}}</em>", "global_order_info": "Shipping to", "vender_page": "<PERSON><PERSON><PERSON>", "global_recommed_cancel": "Back", "cold_pack_fee": "Cold Pack Fee", "what_included": "What's included?", "what_included_fee": "What's included?", "cold_packaging": "Cold Pack", "currently_not_available": "Currently not available", "checkout_place_warning": "To guarantee the quality of your food, please store food indoors or refrigerate it if needed.", "checkout_on_demand_place_warning": "On-demand orders cannot be canceled after the restaurant confirms your order.", "today_order": "Today Orders", "today_order_title": "Your scheduled orders for today {{todayOrderDate}}", "checkout_vendor_is_closed": "Sorry, the restaurant is not available in this address", "change_restaurant": "Change Restaurant", "unavailable_restaurant": "Unavailable Restaurant", "on_demond_deliver_arrival": "ETA:", "call_driver": "Contact driver", "btn_done": "Done", "btn_cancel": "Cancel", "delivery_finish_msg": "Enjoy 🎉", "driver_msg_title": "Driver said ", "shipping_service_fee": "Service Fee", "taxes_with_fees": "Taxes & fees", "cart_type_restaurant_Local_Deliver": "Restaurant Cart Local Delivery: <span>{{date}}</span>", "cart_type_restaurant_Pickup": "Restaurant Cart Pickup: <span>{{date}}</span>", "me_edit_user_name_notes": "* Username can only contain letters, numbers, and periods, and must begin with a letter. <br/><br />* You can only change your username once. <br/><br />* Your friend can find you by your user name.", "me_notification_promotions": "Sales & Promotions", "me_notification_promotions_tip": "Our top picks among new arrivals, bestsellers, and limited-time deals.", "me_notification_email": "Emails", "me_notification_app": "Push notifications", "me_notification_personalized": "Personalized notifications", "me_notification_personalized_tip": "Coupon, restock notifications and community messages.", "me_notification_system": "Order and system notifications", "me_notification_system_tip": "*For your convenience, you can't disable email for these notifications.", "update_save": "Save", "me_user_alias": "Nickname", "me_user_id": "ID", "me_verify_email_verify": "Bundle", "login_bind_phone": "Connect Phone", "email_address_verify_tip": "Please confirm your email and tap 'Send code' to receive a verification code.", "forget_password_resend": "Resend", "me_setting_notifications": "Notifications", "change_password": "Change Password", "me_old_password": "Current password", "me_new_password": "New password", "me_new_password_placeholder": "Retype new password", "me_member_my_member": "My Membership", "me_member_weee_expired": "Expiration date", "me_member_to_renewal": "<PERSON>w", "me_notification_promotion_email_confirm_mess": "We will send you a confirmation email, please click the link in the email to confirm subscription. ", "me_payment_method_title": "Payment Method", "me_payment_add_card_btn": "Add new card", "me_member_member_management": "Member management", "choose_picture": "<PERSON><PERSON>", "confirm_update": "Confirm", "public_cancle": "Cancel", "me_invalid_email": "Invalid Email address", "me_empty_alias": "alias is null", "me_over_alias_length": "The alias field can not exceed 45 characters in length.", "me_username_format_incorrect": "Username can only contain letters, numbers, and periods, and must begin with a letter, can not contain 'weee'.", "me_payment_remove_card": "Remove Card", "me_payment_remove_card_mess": "Are you sure you want to remove this card from your account?", "me_payment_remove_card_confirm": "Yes", "me_payment_remove_card_cancel": "No", "member_vip_savings": "My VIP Savings", "member_start_time_v2": "You've saved ( since {{time}} ):", "member_trial_has_expired": "Free VIP Pass has expired on {{time}}", "member_trial_will_expired": "Free VIP Pass will expire on {{time}}", "member_has_expired": "VIP Status has expired on {{time}}", "member_will_expired": "VIP membership will expire on {{time}}", "member_renew_vip_v2": "Renew VIP", "me_points_redeem": "Redeem", "member_v2_benefit_rebeat_title": "{{amount}}% back in points", "on_demond_finish_arrival": "Delivered at", "me_password_empty": "Password is null", "me_password_disaffinity": "The passwords you entered do not match.", "change_picture": "Change picture", "order_instructions": "Seller Instructions", "enter_order_instructions_here": "Enter order instructions here", "enter_order_instructions_here_max_500": "Enter order instructions here (maximum 500 characters)", "or_lower_case": "or", "continue_with_phone": "Continue with Phone", "continue_with_email": "Continue with <PERSON>ail", "continue_with_google": "Continue with Google", "continue_with_facebook": "Continue with Facebook", "continue_with_wechat": "Continue with WeChat", "signup_terms_new": "<p>By continuing. You agree to our<term>Terms of Service</term> & <policy>Privacy Policy</policy></p>", "reorder_successfully": "Added to cart successfully", "rating_question_index": "{{index}} of {{length}}", "rating_view_order": "View order", "rating_order_comment": "Tap to add other comments", "rating_modal_appreciate_title": "We appreciate your feedback", "rating_modal_appreciate_sub_title": "Your feedback will help our team<br/>improve your Weee! experience.", "rating_feedback_problem": "Have a problem with your order?", "rating_request_refund": "Request refund", "rating_go_shopping": "Go Shopping", "rate_confirm_exit_title": "Are you sure you want to exit the survey?", "rate_confirm_exit_complete": "Complete survey", "rate_confirm_exit_anyway": "Exit anyway", "rate_order": "Rate order", "login_tips": "Log in to like the video or leave a comment and support the creator!", "signup_tips": "Sign up to like or comment on this video.", "student_get_free_vip": "Students get $15 OFF <br/>+ 3 months of Free VIP", "student_create_account": "Create an account with your .edu email", "student_get_offer": "Get my student offer", "student_get_coupon": "Get $15 OFF coupon", "student_get_first_order": "On your first order of $35+", "student_automatically_applied": "Coupon automatically applied at checkout.", "student_enjoy_vip": "Enjoy VIP Pass for 3 months", "student_up_to_back_in_points": "Up to 10% Back in Points", "student_for_grocery_mail_order": "5% for grocery, Pantry+, and direct mail orders. 10% for select restaurants.", "student_every_week_for_grocery": "2x every week for grocery, Pantry+, and direct mail orders. Unlimited for restaurant orders over $12.", "student_free_recepo": "Free RICEPO VIP on us!", "student_back_10_receipo_points": "10% back in RICEPO points. Free delivery on orders over $12. RICEPO is our restaurant food delivery app.", "student_no_auto_renew": "No auto renew after trail ends. ", "student_enter_student_email": "Student Email", "student_password": "Password", "student_why_shop_at_weee": "Why shop at Weee!", "student_exciting_items": "Exciting Items You Won't Find Elsewhere", "student_taste_thoughtfully": "Taste thoughtfully curated and delicious foods.", "student_great_quality": "Great Quality and Price", "student_get_highest_quality": "Get the highest quality groceries at the best value.", "student_free_delivery": "Free Delivery. No Fees.", "student_enjoy_no_subscription": "Enjoy no subscription fees and a low minimum order.", "student_email_registered": "Looks like this email is already registered.", "student_use_a_different_email": "Please use a different email to sign up", "student_already_have_an_account": "Already have an account?", "student_not_qualify_student_discount": "The email address provided does not qualify for the student discount.", "student_continue_to_sign": "If you continue to sign up, you'll still get a $10 OFF coupon.", "student_dismiss": "<PERSON><PERSON><PERSON>", "student_email_sent_successfully": "<PERSON><PERSON> has been sent successfully.", "student_verify_email": "Please verify your student email address", "student_we_sent_email_to": "You're almost there! We sent an email to", "student_complete_your_verification": "Click the link on the email to complete your verification and get <b>{{perks}}</b>.", "student_resend_email": "Resend email", "student_check_spam_folder": "Please check your SPAM FOLDER if you don't see the email in inbox.", "student_dont_see_email": "Still don't see the email?", "student_verify_success": "Verification successful!", "student_verify_failed": "Verification failed!", "student_email_verified": "Your student email has been verified. Enjoy <b>{{desc}}</b>", "student_footer_terms": " © 2015 - {{year}} Weee!, Inc. Terms of Service Disclaimer", "select": "Select", "earned_from_points_back": "Total earned from Points back:", "saved_from_free_delivery": "Total saved from free delivery:", "member_rebeat_des1": "{{amount}}% back for grocery, pantry+ and direct mail orders", "member_rebeat_des3": "Points credited within 3 days", "member_free_delivery_desc1": "2x every week for grocery, pantry+ and direct mail orders", "member_vip_title": "VIP Special Pricing", "member_vip_desc1": "Lowest price available on Weee!", "member_vip_desc2": "Exclusive discount up to 30% off", "saved_from_vip": "Total saved from VIP Pricing", "renew_member_btn": "Renew to Keep Saving $$", "equal": "Equal", "points_inactive": "Redeem your <span>{{amount}}</span> Points back", "activate_points": "Redeem", "become_vip_title": "Unlock exclusive savings with a VIP membership", "choose_vip_plan": "Choose your VIP plan", "member_rebate": "{{amount}}% Points Back", "member_limited_tag": "Limited time offer", "vip_title": "VIP Member", "recepo_modal_title": "About RICEPO", "recepo_modal_body_title": "Best Asian Food Delivery APP", "recepo_modal_body_desc1": "Wide selection of authentic Asian restaurants", "recepo_modal_body_desc2": "Daily exclusive discounts", "recepo_modal_body_desc3": "Super-fast delivery", "recepo_modal_body_desc4": "Get $10 off first order", "vip_renewed": "Your VIP Pass is Renewed", "continue_shopping": "Continue shopping", "back_to_vip": "Back to VIP", "vip_text": "VIP", "redeem_giftcard_page_title": "Redeem Gift Card", "redeem_giftcard_input_title": "Check Your Gift Card Balance", "redeem_giftcard_input_label": "Enter the 9-digit code on the back.", "redeem_giftcard_submit_button": "Submit", "redeem_giftcard_confirm_title": "{{amount}} ready to redeem", "redeem_giftcard_confirm_intro": "Don't worry, your <PERSON><PERSON>! Gift Card never expires!", "redeem_giftcard_confirm_btn": "Apply to My Account", "redeem_giftcard_confirm_cancel_btn": "Not now", "redeem_giftcard_success_title": "{{points}} Weee! Points were applied to your account", "redeem_giftcard_success_tag1": "Previous Balance:", "redeem_giftcard_success_tag2": "Gift Card Redeemed:", "redeem_giftcard_success_tag3": "New Balance:", "redeem_giftcard_success_btn": "Start shopping", "redeem_giftcard_success_restart_btn": "Redeem another gift card", "redeem_giftcard_landing_title": "Your gift awaits!", "redeem_giftcard_landing_description": "Check your <PERSON>ee! Gift Card balance and start using now.", "redeem_giftcard_landing_start_btn": "Check Balance", "continue_to_checkout": "Continue to Checkout", "return_to_cart": "Return to cart", "login_resign_in": "Sign In", "delivery_instructions": "Delivery Instructions", "send_success": "Email has been Sent!", "send_failure": "Fail to send", "me_phone_connected": "Connected", "address_list_title_deal": "Delivery Info", "me_member_comfirm_address_title": "Confirm Address Change", "me_member_update_message_confirm": "Confirm new changes?", "cancle_update": "Cancel", "view_product": "View Products", "coupon_title": "Save<span>${{discount}} OFF</span>Selected items<br/><i>On orders over ${{limit}}</i>", "coupon_title_percentage_limit": "Save<span>{{discount}}% OFF</span>Selected items<br/><i>On orders over ${{limit}}</i>", "coupon_title_percentage": "Save<span>{{discount}}% OFF</span>Selected items", "coupon_expires_time": "*Expires on {{expire}}", "coupon_btn": "Redeem Now", "coupon_btn_success": "Redeemed", "collect_other_related_products": "Other Related Products", "other_products": "Other Products", "seems_like_the_offer_is_expired": "Sorry, this offer has expired", "the_offer_is_only_eligible_for_new_member_with_in_14_days": "The offer is only eligible to non-VIP new members within 14 days of signing up.", "pre_order_later": "later", "pre_order_later_pickup": "Later", "pre_order_asap": "ASAP", "pre_order_asap_pickup": "ASAP", "pre_order_choose_time": "Choose a time", "order_success_delivery_time_label": "Delivery Time:", "order_success_pickup_time_label": "<PERSON><PERSON><PERSON><PERSON> gian nhận hàng:", "ondemand_cart_delivery_title": "Delivery Time", "ondemand_cart_pickup_title": "Pickup Time", "mof_delivery_date": "Delivery date", "shopping_list_empty_cart_button": "Other Products", "extra_discount_deals": "Extra Discount Deals", "enhoy_discount": "Enjoy discounts only if you add items from this list to your cart.", "no_deals_more_in_modal": "<p>Add <span style=\"color:#FE6E5A\">${{price}}</span> more to cart to unlock the discount.</p>", "deals_more_in_modal": "<p>Add <span>${{price}}</span> more to cart to reach ${{limit}}</p>", "selected": "Selected", "grouping_selected": "Selected", "grab_these_deals_now": "Select up to {{maxPurchace}} items. Limit {{maxPurchacePerItem}} per item.", "select_up_to_num": "Select up to {{maxPurchace}} items", "order_rating_guide_info": "Tap this button to rate the delivery experience and product quality.", "order_rating_guide_got_it": "Got it", "ondemand_ricepo": "Delivery", "pickup_ricepo": "Pickup", "new_en": "New", "deal_title": "Extra Discount Deals", "cold_pack_in_pdp": "Cold Pack", "cold_pack_title_in_pdp": "Fee Applies", "cold_pack_title_in_popup": "What is this?", "cold_pack_desc_in_popuo_1": "Cold Pack Fee: ${{fee}}\n\nAdditional packaging, such as dry ice or gel ice packs, is required to ensure the quality of frozen and refrigerated items.The cold pack fee is based on the cost of the packaging materials.\n\n Same day delivery orders, only one cold chain packaging fee.", "cold_pack_desc_in_popuo_2": "The ${{deliveryAmount}} Cold Pack Fee is waived on orders over ${{totalAmount}}.", "cold_pack_desc_in_popuo_3": "More Detail", "see_all": "See all", "view_all": "View all", "points_group_progress_title": "My Progress", "points_group_view2_success_info_v2": "Sorry, you are late!", "points_group_view2_success_tips_v2": "Look how rewarding their great teamwork can be!", "points_group_view2_fail_info_v2": "Oh no!", "points_group_view2_fail_tips_v2": "You let your bonus offer expire. <br/>But now you can recruit your own friend!", "points_group_view_buy_btn": "My turn to try", "points_group_share_again_btn_v2": "Try again", "points_group_share_btn_V2": "Invite a friend now!", "points_group_go_home_btn_v2": "Continue Shopping", "points_group_fail_btn": "Check out today's sale", "points_group_rule_title": "How does it work?", "hot_products_this_week": "Popular This Week", "points_group_view_user_detail_v2": "{{user}} scored ${{price}} with <span>${{rebate}}</span> in bonus points", "points_group_view_success_tips": "<h4>Your extra <span>${{leaderReward}}</span> points already in your account</h4>", "points_group_view_user_success_tips": "<h4><span>${{userReward}}</span> bonus points already in your account</h4>", "points_group_view_fail_restart_title": "Oh no!", "points_group_view_fail_restart_tips_v2": "<h4>Your bonus offer expired. But don't give up just yet, you can recruit another friend!</h4>", "points_group_view_fail_title": "Bummer!", "points_group_view_fail_tips": "<h4>Look like you've used both of your two chances.</h4>", "points_group_invite_friend_tip": "<h2>Invite a friend to stock up</h2><h5>Score extra <strong>{{reward}}</strong> bonus points!</h5>", "points_group_order_success_tip_v2": "You've added ${{reward}} in your Points account.", "points_group_check_points_balance": "Check my Points Balance", "points_group_not_started_tip": "Placing your order...", "points_group_not_success_tip": "You have not invited friends to buy together", "points_group_is_paying_title": "Both of you will earn bouns soon...", "points_group_buy_page_title": "My Bonus Progress", "points_group_buy_footer_price": "Price: ${{amount}}", "points_group_no_group_buy": "You have not recruit friends yet", "points_group_view_success_title": "Success", "points_group_view_ended_title": "Ended", "points_group_view_in_progress_title": "End in: ", "points_btn_select": "Select", "points_page_title": "Purchase Weee! Points", "points_tag_you": "You", "member_order_success": "Purchase Success", "member_order_success_congratulations": "Congratulation", "member_order_success_message": "Your order \"{{message}}\" has been completed!", "member_prolong_title": "Extend VIP membership for FREE?", "member_max_extend": "<day>Extend up to {{duration}}</day><br/><price> Value of ${{amount}}</price>", "member_renew_value": "Share and extend your<br/> VIP membership", "invite_friends_btn": "In<PERSON><PERSON>", "member_shop_now": "Shop Now", "member_share_title": "<PERSON><PERSON><PERSON><PERSON>", "member_share_tab": "VIP Share", "member_current_date": "Current expiration date ", "invite_more_friend_1": "Friends help extend<br/><span class='note_1'>{{day}}days</span><span class='note_2'>(till<em>{{date}}</em>)<br/>Value of ${{amount}}</span>", "invite_more_friend_2": "Invite more friends", "member_share_view_detail": "View details", "share_and_extend_member_owner": "Share and extend VIP membership", "share_and_extend_member": "Share and extend VIP membership", "member_share_down_title": "Expired. You got additional <em>{{day}} days of VIP membership</em>", "member_share_down_sub_title": "Your VIP membership is extended to <em>{{day}}</em>", "member_share_time_limit": "Share expires in", "member_share_owner_invite": "Invite Friends", "friend_helpd_invite": "Your friends already helped<em> extend your VIP membership</em>", "membership_vip_extend": "Your VIP membership has extended to", "see_who_helpd": "See who helped", "nobody_help_yet": "Nobody helped you yet", "nobody_help_yet_1": "Nobody helped you yet", "member_share_bought": "Bought", "order_red_envelope_rules": "Weeebate Program Details", "member_share_owner_rule_1": "1. More people you share to, longer your VIP membership can be extended. If your friends become Weee! VIP through your shared link, your VIP membership will be extended as well.", "member_share_owner_rule_2": "2. You can extend up to 100% of the purchased VIP membership.", "member_share_owner_rule_3": "3. Extended VIP membership will be reflected immediately.", "member_share_owner_rule_4": "4. The rights to interpret the event belongs to <PERSON><PERSON>!", "member_share_rule_1": "1. Help your friend to extend VIP membership, and you will receive discount on becoming a VIP yourself.", "member_share_rule_2": "2. Discount is valid for 1 hour after helping your friend.", "member_share_rule_3": "3. Each friend can only help once per share.", "member_share_rule_4": "4. The rights to interpret the event belongs to <PERSON><PERSON>!", "me_share_you_are_not_member": "You are not a Weee! VIP yet", "join_member": "Become a VIP", "member_share_owner_title6": "Please help {{alias}} <br /> extend their VIP membership", "member_share_view_buy_title": "Just bought", "member_order_info_value": "Value of: ${{price}}", "member_share_owner_title8": "You will also receive<br />special discount", "member_share_owner_title9": "Help Now", "member_share_owner_title17": "VIP Benefits", "member_loading_show_member_privilege_icon_one": "Free<br />shipping", "member_loading_show_member_privilege_icon_two": "{{precent}}%<br />rebate", "member_loading_show_member_privilege_icon_three": "VIP<br />price", "member_loading_show_member_privilege_icon_four": "Hassle-free<br />returns", "account_deleted_tips": "Sorry this account has been deactivated and permanently deleted. Please login with new one or Sign up a new account.", "ethnic_shop_unique_online": "Shop Unique {{ethnic}}<br/>Groceries Online", "ethnic_shop_unique_online_sub": "Discover specialty fruits and vegetables, snacks, and dry goods.", "ethnic_quality_goods_low_price": "Quality {{ethnic}} Goods, Everyday<br/>Low Prices", "ethnic_shop_at_unbeatable_value": "Shop everyday essentials<br/>at an unbeatable value.", "ethnic_free_delivery": "Free {{ethnic}} Grocery Delivery", "ethnic_get_foods_delivery": "Get your favorite {{ethnic}} foods delivered right to your door.", "footer_ethnic": "Ethnic Storefront", "footer_ethnic_asian": "Asian", "footer_ethnic_chinese": "Chinese", "footer_ethnic_filipino": "Filipino", "footer_ethnic_indian": "Indian", "footer_ethnic_japanese": "Japanese", "footer_ethnic_korean": "Korean", "footer_ethnic_mexican": "Mexican", "footer_ethnic_vietnamese": "Vietnamese", "online_ethnic_groceries": "Online {{ethnic}} Groceries Near You Delivered to You", "crumbs_home": "Home", "crumbs_online": "Groceries online", "crumbs_online_ethnic": "Online {{ethnic}} groceries delivered to you", "ethnic_seo_title": "{{ethnic}} Grocery Delivery near me | #1 {{ethnic}} delivery app in the US", "ethnic_seo_description": "Order {{ethnic}} groceries online and get the essentials delivered right to your door. Browse our vast selection of fresh fruits, vegetables, meat, and seafood, plus snacks and dry goods. Weee! has all of your favorite foods typically found in {{str}} {{ethnic}} grocery store. Get same-day delivery on some {{ethnic}} groceries without any fees or subscriptions. Order groceries in bulk like canned, dry goods, snacks, and beverages. Buy household goods, self-care items, and {{ethnic}} groceries online, all in one place.", "ethnic_seo_h1": "Online {{ethnic}} groceries delivered to you", "ethnic_seo_keywords": "asian groceries, asian groceries near me, order asian groceries online, asian groceries online, asian groceries delivery, asian groceries delivered, buy asian groceries online, asian groceries store", "bind_phone_title": "Connect Phone", "home_social_media_influencer_policy": "Brand Endorsement Policy", "invoice_header_title": "Invoice", "invoice_details_title": "Invoice details", "invoice_no": "Invoice No.", "invoice_delivery_date": "Delivery date", "invoice_order_no": "Order No.", "invoice_contact_info": "Contact info", "invoice_name": "Name", "invoice_phone": "Phone", "invoice_address": "Address", "invoice_order_details": "Order details", "invoice_return_item": "Return item", "invoice_remark": "Notes", "invoice_remark_text": "Out of stock items will be refunded within 3-5 business days. Please contact customer <NAME_EMAIL> if you have any questions.", "invoice_paid_points": "Paid by Weee! Points", "invoice_order_discount": "Discount", "invoice_order_price": "Subtotal", "invoice_payment_amount": "Payment amount", "invoice_notes": "Delivery instructions", "invoice_order_date": "Order date", "order_product_pending": "Out-of-stock", "geo_shop_unique_online": "Order Groceries Online in {{location}}", "geo_shop_unique_online_sub": "Shop specialty {{ethnic}} items, fresh produce, snacks, and more.", "geo_quality_goods_low_price": "Quality {{ethnic}} Goods in {{location}}", "geo_shop_at_unbeatable_value": "Get {{ethnic}} groceries and essentials at the best value in town.", "geo_free_delivery": "Free Grocery Delivery to {{location}}", "geo_ethnic_seo_title": "{{location}} {{ethnic}} Groceries Delivered to You | Weee!", "geo_ethnic_seo_description": "Weee!'s {{ethnic}} grocery delivery service is available in {{location}}! Get {{ethnic}} groceries delivered right to your door if you live in or near {{location}}!", "geo_ethnic_seo_keywords": "asian groceries, asian groceries near me, order asian groceries online, asian groceries online, asian groceries delivery, asian groceries delivered, buy asian groceries online, asian groceries store", "geo_ethnic_groceries": "{{location}}<br/>{{ethnic}}<br/>Groceries Delivered to You", "geo_crumbs_online_ethnic": "{{location}} online {{ethnic}} groceries delivered to you", "geo_crumbs_online": "Online groceries", "cancel_order_order_btn": "Cancel order", "cancel_order_success_title": "Your order has been <br/> canceled successfully", "cancel_order_success_subtitle": "You can view it in My Orders under \"Canceled\"", "cancel_order_success_btn_done": "Done", "cancel_order_success_btn_reorder": "Reorder", "cancel_order_confirm_title": "Are you sure you want to cancel the entire order?", "cancel_order_confirm_subtitle": "By clicking 'Cancel entire order', your order will be immediately canceled.", "cancel_order_confirm_tips": "*Refunds for non-USD purchases will be based on real-time exchange rates, which may differ from the original payment amount.", "cancel_order_btn_cancel": "Cancel entire order", "cancel_order_btn_change_modify": "Modify order instead", "cancel_order_select_option_tips": "Can you tell us why you're canceling your order?", "cancel_order_select_option_ps": "(*Optional)", "cancel_order_select_option_note_placeholder": "Tell us your reason...", "cancel_order_select_option_btn_skip": "<PERSON><PERSON>", "cancel_order_select_option_btn_submit": "Submit", "removed_article_can_not_access": "The page can't be accessed", "vip_renewal_title": "Automatic renewal", "vip_renewal_btn": "Cancel auto renewal", "vip_renewal_desc": "Your auto subscription through Paypal is turned off.", "review_a_product": "Review a product", "edit_review": "Edit review", "std_landing_desc_coupon": "Get a $15 OFF coupon", "std_landing_desc_coupon_title": "On your first order of $35+", "std_landing_desc_coupon_summary": "Coupon automatically applies at checkout.", "std_landing_desc_free": "Enjoy free delivery", "std_landing_desc_free_title": "Free grocery delivery", "std_landing_desc_free_summary": "2x every week for grocery, Pantry+, and<br/>direct mail orders.", "std_landing_desc_free_title1": "Free restaurant delivery", "std_landing_desc_free_summary1": "Unlimited for restaurant orders over $12.", "std_landing_desc_vip": "Enjoy Free VIP Pass", "std_landing_desc_vip_title": "Up to 10% Back in Points", "std_landing_desc_vip_summary": "5% for grocery and pantry+ and direct mail orders. 10% for select restaurants.", "std_landing_desc_vip_title1": "Free RICEPO VIP on us!", "std_landing_desc_vip_summary1": "10% back in RICEPO points. Free delivery on orders over $12.<br/> RICEPO is our restaurant food delivery app.", "std_grocery_store_intro": "We are the Largest Asian Grocery Store<br/>in North America!", "std_shop_unique_online": "Exciting Items You Won't<br/>Find Elsewhere", "std_shop_unique_online_sub": "Taste thoughtfully curated<br/>and delicious foods.", "std_quality_goods_low_price": "Great Quality and Price", "std_shop_at_unbeatable_value": "We offer the highest quality<br/>groceries at the best value.", "std_free_delivery": "Free Delivery. No Fees.", "std_free_delivery_sub": "Enjoy no subscription fees and<br/>a low minimum order.", "we_got_covered": "We got your covered.", "std_grocery_store_intro_new": "Authentic Asian flavors delivered to your doorstep", "std_intro_1": "Covers multiple Asian cuisines", "std_intro_1_desc": "We bring you authentic Asian products, sourced locally and directly imported from Asia.", "std_intro_2": "Free delivery, no subscription needed", "std_intro_2_desc": "Free next delivery in most major cities in the United States. No subscription needed.", "std_intro_3": "Peak freshness and great quality", "std_intro_3_desc": "Enjoy the highest quality groceries at the best value.", "std_used_from": "Loved by students from", "std_used_from_tip": "and many more schools across US", "std_get_free_vip": "Students get $15 OFF +<br/>1 month Free Shipping<br/>and Free VIP", "std_get_free_vip_bt": "Students get $15 OFF +1 month<br/>Free Shipping and Free VIP", "std_get_offer": "Get my student offer", "std_redeem_btn": "Redeem", "std_signup_and_verify": "Sign up & verify", "std_verify_account": "Verify .edu account", "std_complete_your_verification": "Click the link on the email to complete your verification and get <b>$15 OFF</b> and <b>1 months of Free VIP</b>", "std_email_verified": "Your student email has been verified. Enjoy <b>$15 OFF</b> your first order and <b>1 months of free VIP</b>", "std_register_create_account": "Sign up with your .edu email and verify student <br/>status to unlock exclusive offer.", "std_what_hot_and_new": "Discover what's hot and new", "std_real_customer_reviews": "600,000+ real customer reviews!", "std_valid_for_month": "Valid for 1 month", "std_valid_no_auto_renew": "Valid for 1 month. No auto renew after trial ends.", "std_review": "See how our customers say", "std_review_desc": "2,000,000+ real customer reviews!", "address_pin_dont_see": "Don't see your address?", "address_pin_create": "Create your address manually", "address_pin_confirm_title": "Adjust pin to confirm your location", "address_pin_confirm_desc": "Help drivers deliver your order faster when you place the pin on the correct street address.", "address_pin_error_title": "You've placed your pin out of range", "address_pin_error_desc": "Your pin is placed outside of where your address is, please move it back into the area.", "address_pin_confirm_btn": "Confirm Location", "address_pin_move_pin_location": "Move pin location", "address_pin_error_tip": "Outside of adjust area", "lightning_remind": "Remind Me", "lightning_reminded": "Reminded", "rtg_share_order_top_bar": "Restaurant", "rtg_share_order_invite_friends": "Invite friends to order<br/>", "order_success_popup_rtg_order_share_content_1": "Earn Up To<br/><span>{{points}} Points (${{amount}})</span>", "rtg_share_order_create_points": "Earn points back up to <span>{{points}} points (${{amount}})</span>", "rtg_share_order_create_recommend_rtg": "Restaurant Recommendation", "rtg_share_order_create_recommend_dish": "Dish Recommendation", "rtg_share_order_create_btn": "Next: Share the link", "rtg_share_order_create_textarea_placeholder": "Share your story (optional)", "rtg_share_order_sales": "Orders", "rtg_share_order_more": "more", "rtg_share_order_add_recommendation": "Next: Recommendation", "rtg_share_order_join_facebook_group_1": "Official Facebook  Account", "rtg_share_order_join_facebook_group_2": "<PERSON><PERSON><PERSON> points together", "rtg_share_order_join_facebook_group_3": "Join Now", "rtg_share_order_receivers": "Friends who also claimed this discount", "rtg_share_order_terms": "How does Restaurant Weeebates work", "rtg_good_review": "好评", "brand_show_more": "Show more", "brand_show_less": "Show less", "personalize_options": "Personalization options", "personalize_options_label": "Enable personalized recommendations", "personalize_tips": "After closing, your preference feature will not be used for content recommendation, and you may see content that is not of interest to you", "no_vip_in_progress": "Renovation in progress", "no_vip_desc": "We are currently working on an update to our VIP program. During this period, new enrollment is paused.", "no_vip_notify": "Notify me", "no_vip_notifed": "Notification set", "reward_points": "Reward Points", "me_notification_sms": "SMS", "reminder_more": "More", "vip_purchase_benefits": ["2X Free delivery per week", "up to 5% points back", "Free Ricepo VIP"], "weee_points": "Weee! Points", "share_progress_expired_text": "Your share has expired", "share_progress_got_points": "You' ve earned <points></points> Weee! Points", "share_progress_got_amount": "(Equal to $<amount></amount>)", "share_progress_expire_label": "Link expires in", "share_progress_share_btn": "Share and earn back $<amount></amount>", "share_progress_share_btn_expire": "Activate earned points", "share_progress_btn_continue": "Continue shopping", "share_progress_share_product_title_rtg": "Shared Restaurant", "share_progress_revicer_title": "Friends invited ( <num></num> )", "share_progress_revicer_tips": "Save money with the Weee! community", "share_progress_fb_group": "Official Weeebates! Group", "share_progress_no_revicer": "Your friend have not invited any friends <br>at this time. Help your friend now!", "share_progress_rules_title": "How does Restaurant Weeebates work", "share_progress_pop_title": "Successful", "share_progress_pop_sub_title": "Earn more rebates by sharing with other <span>friends</span> and <span>groups</span>", "share_progress_pop_sub_title2": "Save money with the Weee! community", "share_progress_pop_inviter_get_desc": "<user></user> earned <amount></amount> today", "share_progress_invite_first_name": "First Invite Bonus", "share_progress_invite_first_label": "Rewards", "share_progress_invite_buy_label": "Bought", "share_progress_join_fb_group": "Join Facebook Group", "share_progress_share_pop_title": "Ordered <name></name> on Weee!", "share_progress_share_imgpop_tips": "Press + Hold to claim now or save as image", "delivery_fee_details": "Delivery Fee Details", "order_success_multiple_orders_tip": "Your order is confirmed! Please check your email for delivery details.", "pdp_post_review_tips": "Be the first person to post a review and earn up to <em>100 Weee! points  ($1) !</em>", "pdp_how_earn_points": "How to earn points", "pdp_reviews_read_guidelines": "Please read our <em>Review Guidelines</em> for more", "order_share_create_title": "<PERSON><PERSON><PERSON><PERSON>", "order_share_create_tip": "Choose up to 15 products", "order_share_create_selected": "Selected", "order_share_create_select_all": "Select all", "order_share_create_earn_points": "Earn up to <span>{{points}}pts</span>", "share_progress_grocery_first_tips": "Earn <span>{{points}} Bonus Points</span> for first invited friend", "share_progress_grocery_first_earned": "You've earned extra <span>{{points}} Bonus Points</span>", "share_progress_grocery_by_friends": "Earned by {{num}} friend{{s}}", "share_progress_grocery_from_viewed": "<span>{{points}}pt{{s}} </span> from {{num}} friend{{s}} who viewed your offer", "share_progress_grocery_from_bought": "<span>{{points}}pt{{s}} </span> from {{num}} friend{{s}} who bought your items", "share_progress_grocery_extra_time_tips": "Take advantage of the extra share period.", "share_progress_grocery_close_countdown": "Earning window closes in <countdown></countdown>", "share_progress_grocery_time_out": "Earning window closed", "share_progress_grocery_btn_keep": "Keep sharing", "share_progress_grocery_btn_activate": "Activate points", "share_progress_grocery_btn_continue": "Continue shopping", "share_progress_grocery_points_briefly": "100 points = $1 which can be used directly for future purchases. ", "share_progress_grocery_points_learn_more": "Learn more about <PERSON><PERSON><PERSON>s", "share_progress_grocery_group_title": "Share to the Weeebates group", "share_progress_grocery_group_subtitle": "Get even more people to view your offer and buy your items", "share_progress_grocery_group_fb": "Join Facebook Group", "share_progress_grocery_group_kakao": "Join <PERSON>", "share_progress_grocery_viewed_tab": "Viewed offer({{num}})", "share_progress_grocery_bought_tab": "Bought items({{num}})", "share_progress_grocery_btn_view_more": "view more", "share_progress_grocery_shared_title": "Your shared items ({{num}})", "share_progress_grocery_rules_title": "Weeebates program details", "share_progress_grocery_no_viewed": "No friend viewed your offer yet.", "share_progress_grocery_no_bought": "No friend bought items you shared yet.", "share_progress_grocery_get_pts": "pt{{s}}", "order_share_create_share_link": "Next: share your link", "order_success_popup_info_order_confirmed": "Order confirmed", "order_success_popup_info_referrer": "You saved ${{saved}} buying <strong>{{alias}}</strong>'s items and they earned ${{earned}}", "order_success_popup_info_earn_title": "Earn up to ${{amount}}", "order_success_popup_info_earn_up_to": "Up to ${{amount}}", "order_success_popup_info_earn_up_to_2": "Plus up to ${{amount}}", "order_success_popup_info_share_bought": "Share what you bought", "order_success_popup_info_you_get": "What you will get", "order_success_popup_info_friends_view": "when friends view your offer", "order_success_popup_info_friends_buy": "when friends buy items you shared", "order_success_popup_info_friends_get": "What your friends will get", "order_success_popup_info_discounts": "They get <strong>discounts</strong> on items you shared", "order_success_popup_info_terms_intro": "Your earnings will be in Weee! Points, which can be used directly for future purchases.", "order_success_popup_info_terms": "Learn more about <PERSON><PERSON><PERSON>s", "order_success_popup_start_earning": "Start earning", "order_success_popup_right_now": "{{amount}} Weee! customers are sharing and earning right now", "see_available_options": "See available options", "confirm_receipt_order_btn": "Confirm delivery", "confirm_receipt": "Have you received your entire order?", "confirm_receipt_desc": "We'll let the seller know that you've received all item(s).", "receipt_confirm": "Confirm", "receipt_cancel": "Not yet", "receipt_success": "Thank you for confirming", "order_share_grocery_landing_title": "<span>Enjoy up to {{discount}}% OFF</span><br/> on products {{name}} recommends for you", "order_share_grocery_landing_newuser_title": "Get ${{amount}} OFF<br/>your first two orders", "order_share_grocery_landing_newuser_tips": "Take ${{amount01}} and ${{amount02}} OFF your next two orders of<br/> $35 or more when you use these coupons", "order_share_grocery_landing_newuser_subTitle": "and enjoy products {{name}} recommends for you ", "order_share_grocery_landing_newuser_offer": "<span></span> people just got this offer!", "order_share_grocery_landing_newuser_product_title": "Products recommended by <span></span>", "order_share_grocery_landing_anonymous_tips": "Returning users <link>log-in here</link> and get up<br/>to a {{discount}}% off discount", "order_share_grocery_landing_sharer_name": "on items {{name}} bought", "order_share_grocery_landing_btn_get": "Get the Discount", "order_share_grocery_landing_btn_claim": "Claim discount now", "order_share_grocery_landing_get_list_title": "Friends who also get this discount", "order_share_grocery_signup_title": "Sign up to help your friend get <br/> points back and you earn new <br/> user's exclusive discount", "order_share_grocery_signup_coupon_off": "${{amount}} OFF", "order_share_grocery_signup_coupon_limit": "On order over ${{amount}}", "order_share_grocery_signup_with_options": "Sign up with follow options", "order_share_grocery_signup_log_fb": "Continue with Facebook", "order_share_grocery_signup_log_wx": "Continue with <PERSON><PERSON><PERSON>", "order_share_grocery_signup_log_email": "Continue with <PERSON>ail", "order_share_grocery_signup_has_account_tips": "Already have an account?", "order_share_grocery_signup_log_btn": "LOG IN", "order_share_grocery_signup_pact": "By signing up. You agree to our <linkTerms>Terms of Service</linkTerms> & <linkPolicy>Privacy Policy</linkPolicy>", "order_share_grocery_follow_btn_see_helped": "See who also helped", "order_share_grocery_follow_bargained_title": "Yeah!", "order_share_grocery_follow_expired_title": "Oops!", "order_share_grocery_follow_new_user_title": "Welcome to Weee!", "order_share_grocery_follow_hooray_title": "HOORAY!", "order_share_grocery_follow_no_bargain_text": "{{name}} has maxed out<br/>their points saving!<br/>Thanks for helping!", "order_share_grocery_follow_finish_text": "{{name}} has finished<br/>collecting their points!<br/>Thanks for helping!", "order_share_grocery_follow_used_text": "You've already used this coupon.<br/>Share your next order with<br/>friends to earn more discounts!", "order_share_grocery_follow_expired_text": "Coupon is expired.", "order_share_grocery_follow_new_user_text": "By claiming this discount, you helped {{name}} get <span>{{points}} points</span> back!", "order_share_grocery_follow_hooray_text": "You just helped {{name}}<br/>get <span>{{points}} points</span> back!", "order_share_grocery_follow_coupon_title_D": "${{amount}} OFF<span>order over ${{limit}}</span>", "order_share_grocery_follow_coupon_title_other": "Get {{discount}}% OFF", "order_share_grocery_follow_coupon_section_title": "Enjoy your coupons", "order_share_grocery_follow_coupon_section_subtitle": "(Coupon automatically applied at checkout)", "order_share_grocery_follow_coupon_section_vaild": "Here's an offer for you!", "order_share_grocery_follow_coupon_expires_in": "Expires in: <span></span>", "order_share_grocery_follow_coupon_expires_on": "Expires on: <span></span>", "order_share_grocery_follow_coupon_used": "Used", "order_share_grocery_follow_coupon_expired": "Expired", "order_share_grocery_follow_more_products": "Explore more products", "order_share_grocery_follow_prods_recommended": "Products recommended by <span></span>", "order_share_grocery_follow_prods_recommended_none": "<span></span> Recommended items are currently not available", "order_share_grocery_follow_prods_recommended_use_discount": "Shop products recommended by <span></span> with this discount", "order_share_grocery_follow_prods_perference": "You may also like", "order_share_grocery_follow_top_message": "Enjoy your <span></span> discount within <countdown></countdown>", "order_share_grocery_header_title": "Asian Groceries", "order_share_grocery_extra_tag": "Extra {{discount}}% OFF", "order_share_grocery_helped_modal_title": "Friends who also get this discount", "order_share_list_title": "<PERSON><PERSON><PERSON><PERSON>", "order_share_list_top_tips": "How to earn points back?", "order_share_list_ongoing_tag": "Ongoing", "order_share_list_ongoing_ends_in": "Ends in <span></span>", "order_share_list_btn_share_again": "Share again", "order_share_list_unshare_tag": "Unshared", "order_share_list_order_expire_in": "Expires in <span></span>", "order_share_list_unshare_text": "Expire in <span>{{day}}</span> days", "order_share_list_btn_share_now": "Share now", "order_share_list_end_tag": "Ends", "order_share_list_end_tag_text": "You've earned: <span>{{points}}</span> pt{{s}}", "order_share_list_currently_earned": "Currently earned: <span>{{points}}</span> pt{{s}}", "admin_commission_order": "Order ID: ", "order_share_list_more_orders": "{{more}} more orders", "order_share_list_hide_orders": "<PERSON>de orders", "order_share_list_restaurant_in_order": "{{products}} item{{ps}} from {{orders}} restaurant{{os}} order", "order_share_list_product_in_order": "{{products}} product{{ps}} from {{orders}} order{{os}}", "order_share_list_no_result": "Looks like you haven't had order to share.", "order_share_list_btn_shop_now": "Shop now", "main_discount_title": "Tatung <span>Lowest</span> prices online", "share_product_image_invite_tip": "\"{{tip}}\"", "account_notification_center": "Notification center", "account_notification_center_empty": "No notifications yet", "account_see_more": "See more", "account_see_less": "Show less", "mkpl_add_for_free_shipping": "Add <span>${{free}}</span> from <i>{{name}}</i> for <i>FREE SHIPPING</i>", "order_share_landing_new_message_title": "Get up to {{discount}}% off", "order_share_landing_new_message_subtitle": "on <span></span> shared items", "order_share_landing_new_btn_Claim": "Claim offer", "order_share_landing_new_friends_title": "{{num}} friend{{s}} also claimed this offer", "order_share_landing_new_share_message_earned": "Claimed! You also helped <span>{{name}}</span> earn <span>{{points}}pt{{s}}</span> by claiming this offer.", "order_share_landing_new_share_message_finished": "Claimed! But <span>{{name}}</span> has finished collecting their points.", "order_share_landing_new_share_message_max": "Claimed! But <span>{{name}}</span>. has maxed out their earning. ", "order_share_landing_new_share_btn_check_friends": "See who also helped", "order_share_landing_new_share_message_tag": "New user exclusive offer", "order_share_landing_new_share_coupon_off": "{{discount}} off", "order_share_landing_new_share_coupon_expires_in": "Expires in <span></span>", "order_share_landing_new_share_coupon_expired": "Expired", "order_share_landing_new_share_coupon_used": "Used", "order_share_landing_new_share_coupon_desc": "any order over ${{limit}}", "order_share_landing_new_share_coupon_desc_check": "on following items at checkout", "order_share_landing_new_share_follow_products_title": "Following items also get this discount", "order_share_landing_new_share_follow_top_message": "Extra <span></span> off applied at checkout in <countdown></countdown>", "order_review_from_vendor": "Ships from <link></link>", "location_banner_slogn": "Asian Groceries, Delivered", "locations_home": "Home", "locations_all": "Locations", "locations_us_cities": "U.S. cities", "locations_see_all": "See all cities in {{state}}", "city_detail_title": "Groceries delivered to {{city}}, {{state_short}}", "city_detail_intro": "The modern way to grocery shop for all your traditional favorites. We bring fresh, local, and peak-season ingredients right to your door. Enjoy no subscription fees and free delivery with a low order minimum. Discover unique noms you won't find elsewhere!", "city_detail_sale": "On sale in {{state_short}}", "city_detail_trending": "Bestsellers in {{state_short}}", "city_detail_new": "New additions in {{state_short}}", "city_detail_categories": "One-stop shop for all your grocery needs", "city_detail_reviews": "450K+ customer reviews", "city_detail_near_city": "Nearby Cities", "confirm_unfollow_user": "Are you sure you want to unfollow this user?", "delete_messages": "Delete messages", "clear_all_unread_messages": "Clear all unread messages", "loyalty_landing_introducing": "Introducing", "loyalty_weee_Rewards": "Weee! Rewards", "loyalty_landing_btn_see": "See my dashboard", "loyalty_landing_btn_log": "Join now", "loyalty_langing_shopping_tips": "Unlock exclusive benefits", "loyalty_landing_btn_learn_more": "Learn more about the program", "loyalty_level_current_status": "Your current status", "loyalty_level_valid": "valid until {{date}}", "loyalty_level_apply_tips": "We will apply your progress once the order has shipped.", "loyalty_level_btn_learn_more": "Learn more about Weee! Rewards program", "loyalty_upgrade_learn_more": "Learn more about <rewards>Weee! Rewards Program</rewards> and <points>Weee! Points</points>.", "loyalty_upgrade": "Load to upgrade", "loyalty_extend": "Load to extend", "loyalty_level_tip": "Your current rewards level: {{level}}", "loyalty_extend_tip": "Your current Gold Rewards status ends on {{date}}", "loyalty_upgrade_buy_tip": "Loaded points can be applied to future purchases", "loyalty_upgrade_btn": "Load & upgrade now", "loyalty_extend_btn": "Load & extend now", "loyalty_upgraded_btn": "Shop and save", "loyalty_upgrade_collapse_tip": "Discounts vary across rewards levels", "loyalty_activity": "Your Rewards activity", "loyalty_benefits": "Your Rewards benefits", "loyalty_student": "Your student benefit", "additional_bonus": "Additional bonus", "share_with_more_friends": "Share it with more friends", "invite_friends_in_school": "Invite your friends in school", "loyalty_offer": "Your rewards offer", "loyalty_offer_landing": "Rewards offer", "loyalty_offer_trip_title": "6% off hotels & 2% off flights on Trip.com", "loyalty_offer_trip_desc": "6% off hotels & 2% off flights", "loyalty_offer_trip_hotels": "6% off on the Hotel booking", "loyalty_offer_trip_flights": "2% off on the flight booking", "loyalty_offer_trip_hotels_tag": "6% off hotels: ", "loyalty_offer_trip_flights_tag": "2% off flights: ", "loyalty_offer_trip_tag": "Gold rewards offer<i></i>Until Dec 31st", "loyalty_offer_trip_steps": "Follow steps to get the discount", "loyalty_offer_trip_step1": "Copy discount code below", "loyalty_offer_trip_step2": "Tap the 'Book' button to visit and paste the code.", "loyalty_offer_trip_book_hotel": "Book a hotel", "loyalty_offer_trip_book_flight": "Book a flight", "loyalty_offer_trip_offer_detail": "Offer Details", "loyalty_offer_trip_offer_terms": "Terms & Conditions", "loyalty_offer_trip_offer_disclaimer": "Disclaimer", "loyalty_offer_trip_offer_how_claim": "How to Claim", "bronze_back_rewards_landing": "-", "silver_back_rewards_landing": "3% back", "gold_back_rewards_landing": "5% back", "bronze_back_freebies": "-", "silver_back_freebies": "-", "gold_back_freebies": "Exclusive", "bronze_back_lightning": "Regular deals", "silver_back_lightning": "Extra discount", "gold_back_lightning": "Extra discount", "footer_cookie_settings": "<PERSON><PERSON>", "email_verification_page_title": "Verification result", "send_email_verification_btn": "verify your email", "btn_review": "Review", "btn_reviewed": "Reviewed", "nothing_available_yet": "Nothing available yet", "please_come_back_later": "Please come back later", "you_have_saved": "You have saved <span>${{amount}}</span>", "sales_promotion_ended": "Sales promotion ended!", "search_for_items_on_sale": "Search for items on sale", "vip_end_tips": "The Weee! VIP Program does not provide renewal anymore. All current VIP members can continue to enjoy your benefits until your current membership ends or the end of the year 2024, whichever comes first.", "vendor_seller_policy": "Seller Policy", "tracking_number": "Tracking Number", "track_num_copy": "Copy", "contact_carrier": "Contact {{carrier}}", "copy_success": "Copied successfully!", "hours_of_operation": "Hours of Operation", "package": "Package {{num}}", "full_name_ins": "Receiver name instructions", "account_security_message": "Your information is secured with encryption.", "payment_security_message": "Your payment information is secured with encryption.", "password_security_message": "Avoid using spaces, your name, phone number, or passwords you have used before.", "address_security_message": "Your contact information will only be used for delivery purpose. We will not share your information with anyone.", "points_text": "Weee! Points", "oops_no_find": "Item unavailable", "cant_found_product": "This item isn't available in your area. Try searching for similar products.", "item_back_to_home": "Back to home", "read_all": "Read All", "use_now": "Use Now", "top_charts": "Top charts", "daily_rank_update": "Ranking charts updated daily", "see_more": "See more", "daily_top10_list": "Daily Top 10 Lists", "brand_reviews": "Reviews for this brand", "brand_you_may_also_like": "You may also like", "brand_you_might_like": "Brands you might like", "favorite_title": "Our favorites", "favorite_view_more": "View more favorites", "favorite_view_product": "View product", "pdp_highlight_title": "Product highlights", "pdp_highlight_content": "The product highlights are summarized from real customer reviews.", "pdp_add_remain_items": "Add remaining {{count}} items", "pdp_add_3_items": "Add 3 items to cart", "pdp_add_all_items": "You added all 3 items", "items": "Items({{count}})", "add_all_items": "Add all items to cart", "add_count_items": "Add {{count}} items", "add_items_to_cart": "{{count}} items added to cart", "promo_commend_title": "Recommendations", "deal_sub_total": "Subtotal:", "enjoy_deal_price": "Enjoy the deal price only if you add items from this list.", "special_limit_count": "Selection is limited to {{count}} items", "shop_deal_title": "Your special deals", "good_to_miss": "Too good to miss", "shop_locked": "Locked", "recommend_empty": "There are no corresponding items found", "product_in_store_price": "In-store price", "no_content_and_fresh": "Nothing to show at the moment. Please refresh and retry.", "refresh": "Refresh", "cms_coupon_btn_claim": "<PERSON><PERSON><PERSON>", "cms_coupon_btn_claimed": "Claimed", "cms_coupon_modal_title": "Coupon", "cms_coupon_modal_expires": "Expires on {{date}}", "cms_coupon_modal_toast_tips": "Coupon claimed", "cms_coupon_modal_toast_tips_all": "All coupons claimed", "cms_coupon_btn_used": "Used", "cms_coupon_btn_expired": "Expired", "cms_coupon_btn_use_now": "Use now", "cms_coupon_btn_claim_all": "Claim all", "cms_jump_to_section": "Jump to section", "volume_add_more": "/ea for {{count}} or more", "volume_add_abbreviation": "/ea for {{count}}+", "volume_save": "Save ${{price}}/ea", "volume_when_buy": "When you buy {{count}}+", "volume_for_one": "${{count}} for 1 qty", "volume_when_buy_more": "When you buy {{count}} or more", "seller_fulfillment": "Seller fulfillment", "address_apt_delivery_tips": "Adding your apartment number and drop-off details will help us deliver your order efficiently.", "payment_method_can_not_use_points": "Weee! Points and EBT SNAP cannot be combined.", "order_guarantee_delivery_fee": "Guaranteed delivery", "order_guarantee_delivery_modal_title": "Plan ahead with guaranteed delivery", "order_guarantee_delivery_modal_desc_1": "Select priority delivery to let your driver know when to deliver your order.", "order_guarantee_delivery_modal_desc_2": "Refund policy: If your priority delivery isn't made by the scheduled time, we will refund any guaranteed delivery fees associated with that order.", "order_guarantee_delivery_modal_btn": "Got it", "order_guarantee_delivery_fbw_desc": "Selected products include complimentary guaranteed delivery.", "weee_info_title": "America's #1 Asian grocery delivery app", "weee_desc_1": "Founded", "weee_record_1": "2015", "weee_desc_2": "Authentic products", "weee_record_2": "100k+", "weee_desc_3": "Orders since 2015", "weee_record_3": "30m", "weee_desc_4": "Reviews since 2015", "weee_record_4": "1.5m", "weee_sign_up": "Sign up today", "ebt_pop_title": "EBT & SNAP Eligible", "ebt_pop_sub_title": "Items with the SNAP badge are eligible for purchase using your EBT & SNAP balance", "ebt_checkout_payment_tips": "Only EBT & SNAP eligible items will be applied to your EBT & SNAP balance", "cms_empty_title": "Sorry, we didn't find the page", "cms_empty_des": "The promotion you are looking for may be expired or not available in your region.", "shop_more": "Shop more", "add_items_to_unlock_upsell": "Add ${{money}} to unlock", "order_detail_get_help": "Get help", "order_case_seller_new": "New request", "crv_fee_title": "Beverage container fee", "crv_fee_tax_title": "Beverage container fee & taxes", "presale_drawer_title": "Pre-sale", "trending_topics": "Trending topics", "asian_store_avaliable": "What asian stores are available?", "brand_name_headline": "{{count}} product{{s}} from {{name}}", "account_case_add_order_id_placeholder": "Please enter your order number", "verify_email_success": "You're all set! Your email is verified.", "update_email_success": "Email updated successfully.", "email_address_not_bind_tip": "You have not yet linked an email address. Please enter your email and save it.", "email_address_update_tip": "Enter your new email and tap 'Send code' to verify the change.", "email_send_code": "Send code to email", "email_verify_title": "Verify your email to secure your account", "email_update_title": "Update your email", "email_address_verified_top_tip": "Your current email is verified.", "email_enter": "Enter your email", "resend_code_in": "Resend code in {{countdown}}s", "sign_in_short": "Sign In", "use_app": "Use app", "search_tabs_filter_by": "Filter by", "search_tabs_everything": "All products", "search_tabs_global_plus": "Global+", "menu_global_plus": "Global+", "search_tabs_videos": "Videos", "student_campaign": "Sign up campaign", "americas_largest_online": "America's No.1<br/>Asian Grocery App", "exclusive_offer_for_students_of": "Exclusive Offer for Students of", "exclusive_students_discont": "Exclusive student discount", "exclusive_students_discont_coupon": "Redeem ${{coupon}} off + one month Free delivery", "enter_your_.edu_email": "Enter your .edu email", "check_my_referral_progress": "Check my referral progress", "see_campaign_details": "See campaign details in <a>terms and conditions</a>", "your_invites_give_friends": "Your invites will also give friends $20,<br/> and get you $20", "free_delivery_benefit": "Free Delivery", "free_delivery_with_no_minimum_required": "for 30 days with no minimum spend", "coupon_off": "${{coupon}} off", "coupon_off_15": "Your first order <br/>over $35", "coupon_off_20": "$15 off 1st order<br/>$5 off 2nd order", "bonus": "BONUS: ", "take_a_look_around": "Take a look around", "sign_up_as_a_regular_user": "Sign up as a regular user", "coupon_free": "Free", "seller_address": "Seller address", "track_empty_title": "No tracking info yet.", "track_empty_message": "Your order is on its way. Check back soon.", "no_matches_found": "No matches found", "adjusting_filters": "Try adjusting filters or exploring other categories.", "estimated_delivery_time": "Estimated delivery:", "mkpl_add_cart": {"common_title_prefix": "Added to ", "common_title_suffix": "'s cart", "popup_title": "<span></span><seller></seller><span>'s cart</span>", "popup_button_to_seller": "Shop {{seller}}", "popup_button_to_cart": "View cart", "popup_seller_follower_label": "followers", "toast_title": "Added to {{seller}}'s cart", "toast_button_to_seller": "Store", "toast_button_to_cart": "<PERSON><PERSON>"}, "what_is_weee": "What is '<PERSON>ee!'?", "what_is_weee_content": "Weee! delivers essential grocery items right to your door. Weee! is the #1 e-grocer that specializes in Asian grocery items. With no shopping fees, no subscription, and free delivery on orders over $35, Weee! is the best place to buy local and organic produce and to explore authentic snacks from around the world.", "issue_with_order": "What happens if there's an issue with my order?", "issue_with_order_content": "If there's an issue with your order you may modify or cancel orders via the \"Order Detail\" section in \"My Account\" or you may contact our customer <NAME_EMAIL>.", "contactless_delivery": "Can I get contactless delivery?", "contactless_delivery_content": "We do not require your presence for delivery. We leave the package at your doorstep, yard, locker, mailroom, or leasing office.", "where_does_weee_deliver": "Where does Weee! Deliver?", "where_does_weee_deliver_content": "Weee! delivers all over North America, ensuring that you enjoy fresh Asian staples no matter where you are located.", "store": "Store", "rewards_special": "Weee! Rewards Special", "for_members": "Silver and Bronze members", "thursday_lower_minimum_desc": "$0 delivery fees <br/>on orders of <span></span> <svg></svg>", "thursday_lower_minimum_desc_2": "Skip both delivery and service fees for local delivery orders of ${{current}}+ (instead of ${{origin}}) every Thursday!", "thursday_lower_minimum_ends": "Ends on <span></span>", "thursday_lower_minimum_tips": "Only available in select grocery delivery regions. ", "student_special": "Weee! Student Special", "for_student": "Exclusive for {{school_name}}", "student_no_minimum_desc": "No fee delivery<br/><span>No minimum <br/>required</span>", "student_no_minimum_desc_for_title": "No fee delivery<br/><span>No minimum <br/>required</span><br/><label>(Was $49)</label>", "student_no_minimum_desc_2": "Students and residences in {{school_name}} can enjoy grocery delivery with $0 delivery and service fees  - no minimum purchase required.", "student_no_minimum_ends": "Available for addresses in Zip codes: {{zipcodes}}, valid through {{end_date}}.", "saving_now": "Save now!", "check_today_special": "Check today's special", "enjoy_the_top_pick": "Enjoy the top pick of the Week!", "enjoy_the_top_pick_desc": "Deals, new arrival, Global Plus and more", "delivery_five_title": "Guaranteed delivery", "delivery_five_subtitle": "Guaranteed delivery by {{time}}", "delivery_five_info": "Add at least one of these items to get your order by {{time}}", "guaranteed_delivery": "Guaranteed delivery", "guaranteed_delivery_desc": "Adding items with the \"Delivery by {{time}}\" badge to your order ensures that your entire order will be deliverers by {{time}} on the scheduled delivery day", "guaranteed_delivery_btn": "Got it", "invoice_sold_by_seller": "Sold by {{vender}}", "payment_method_drawer_title": "Payment method", "payment_method_drawer_benefits": "Benefits", "payment_method_ebt_check_balance": "Check balance", "payment_method_btn_edit_amount": "Edit amount", "payment_method_btn_confirm_payment": "Confirm payment method", "payment_method_updated": "Payment updated", "payment_method_add_ebt_card_title": "Add EBT SNAP card", "payment_method_add_ebt_card_btn_save": "Save", "payment_method_ebt_powered_tips": "Powered by <span>FORAGE</span>", "payment_method_add_ebt_card_desc": "Only EBT SNAP items will be automatically applied to your EBT SNAP card.", "checkout_payment_section_title": "Pay with", "checkout_payment_section_edit_payment": "Edit payment method", "checkout_payment_channel_main_tips": "Applied to eligible items and fees", "checkout_payment_channel_remaining_tips": "Applied to remaining balance", "checkout_enter_cvc_code": "Enter CVC code", "checkout_enter_valid_cvc_code": "Please enter a valid CVC code.", "checkout_add_other_payment_channel_tips": "Please add a payment method to pay for tips and any remaining balance after EBT SNAP is applied", "checkout_ebt_edit_popup_title": "Edit EBT amount", "checkout_ebt_btn_confirm": "Confirm", "checkout_ebt_edit_minimum": "The minimum amount is ${{amount}}", "checkout_ebt_edit_maximum": "Maximum allowed is ${{amount}}", "checkout_ebt_edit_pop_tips": "EBT SNAP will cover all eligible groceries in your order up to your specified amount. Delivery fees, service fees, and any extra charges will be covered by your selected payment method.", "checkout_ebt_check_balance_pop_title": "Enter PIN to check balance", "checkout_ebt_check_balance_btn_enter": "Enter", "checkout_ebt_delete_card_title": "Remove Card", "checkout_ebt_delete_card_tips": "Are you sure you want to remove this card from your account?", "checkout_ebt_delete_card_btn_no": "No", "checkout_ebt_delete_card_btn_yes": "Yes", "checkout_ebt_delete_card_btn_done": "Done", "payment_method_add_main_payment_method_tips": "Please add a primary payment method to continue.", "use_points_pop_title": "Are you sure you want to use Weee! Points for this order?", "use_points_pop_desc": "Weee points cannot be combined with EBT/SNAP", "ebt_checkout_section_sub_title": "We accept EBT/SNAP", "ebt_checkout_check_balance_amount": "Latest balance: ${{amount}}", "checkout_no_payment_channel_tips": "Please select payment method", "ebt_card_abridge_title": "EBT SNAP", "setting_add_ebt_card_btn": "Add EBT SNAP", "ebt_not_enough_balance_tips": "Not enough balance. Your payment was updated based on available funds", "ebt_checkout_balance_section_title": "Enter your PIN", "ebt_add_card_section_title": "EBT card number", "ebt_add_card_number_length_error": "Card number must be between 16-19 digits", "ebt_api_error": "Something went wrong. Please try again.", "ebt_card_add_btn_save": "Save", "ebt_use_applepay_continue_desc": "Continue with Apple Pay", "ebt_use_applepay_continue_btn": "Continue", "referral_redeem_offer_btn": "Redeem your ${{amount}} offer", "referral_zipcode_toast": "Please enter your zip code"}