import React, { useState, useEffect, useCallback } from 'react';
import { StyleSheet, View } from 'react-native';
import WeeeAtcComponent from '../../specs/WeeeAtcNativeComponent';
import { ProductBean } from '@/api/model';

import { Cache } from 'weee-native';

type WeeeAtcButtonProps = {
    product?: ProductBean;
    qty?: number;
    onQuantityChange?: (quantity: number, productId: number) => void;
    onStatusChange?: (status: string, productId: number) => void;
    onCartUpdate?: (productId: number, quantity: number, action: 'add' | 'remove' | 'update') => void;
    onError?: (error: string, productId: number) => void;
}

const WeeeAtcButton = (props: WeeeAtcButtonProps) => {
    const { product, onQuantityChange, onStatusChange, onCartUpdate, onError } = props;
    const [currentQuantity, setCurrentQuantity] = useState(0);
    const [isLoading, setIsLoading] = useState(false);

    // 处理数量变化
    const handleQuantityChange = useCallback((event: any) => {
        const { quantity, productId } = event.nativeEvent;

        try {
            const prevQuantity = currentQuantity;
            setCurrentQuantity(quantity);

            // 触发数量变化回调
            onQuantityChange?.(quantity, productId);

            // 触发购物车更新回调
            if (onCartUpdate) {
                let action: 'add' | 'remove' | 'update' = 'update';
                if (prevQuantity === 0 && quantity > 0) {
                    action = 'add';
                } else if (prevQuantity > 0 && quantity === 0) {
                    action = 'remove';
                }
                onCartUpdate(productId, quantity, action);
            }

        } catch (error) {
            console.error('WeeeAtcButton quantity change error:', error);
            onError?.(`数量更新失败: ${error}`, product?.id || 0);
        }
    }, [currentQuantity, onQuantityChange, onCartUpdate, onError, product?.id]);

    // 处理状态变化
    const handleStatusChange = useCallback((event: any) => {
        const { status, productId } = event.nativeEvent;

        try {
            // 根据状态设置加载状态
            if (status === 'increase' || status === 'decrease' || status === 'numClick') {
                setIsLoading(true);
                // 模拟网络请求延迟
                setTimeout(() => setIsLoading(false), 300);
            }

            onStatusChange?.(status, productId);

        } catch (error) {
            console.error('WeeeAtcButton status change error:', error);
            onError?.(`状态更新失败: ${error}`, productId);
        }
    }, [onStatusChange, onError]);

    // 初始化数量状态
    useEffect(() => {
        const itemJson = Cache.simpleOrderItem(product?.id?.toString() || '', product?.product_key || '');
        if (itemJson != null && itemJson != "") {
            try {
                const item = JSON.parse(itemJson);
                if (item && item.quantity) {
                    setCurrentQuantity(item.quantity);
                }
            } catch (error) {
                console.error('WeeeAtcButton parse item error:', error);
            }
        }
    }, [product?.id]);

    return (
        <View style={[styles.container, isLoading && styles.loading]}>
            <WeeeAtcComponent
                style={styles.nativeComponent}
                productJson={JSON.stringify(product)}
                onQuantityChange={handleQuantityChange}
                onStatusChange={handleStatusChange}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    loading: {
        opacity: 0.7,
    },
    nativeComponent: {
        flex: 1,
    },
});

export default WeeeAtcButton;