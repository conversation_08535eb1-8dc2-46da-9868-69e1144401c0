import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { ProductBean } from '@/api/model';
import WeeeAtcButton from '@/component/weee-atc';
import AddToCartButton from '../AddToCartButton';

//import "@global/css";
import "../../../global.css";


type ProductItemProps = {
  item: ProductBean;
  isEditable?: boolean;
  onToggleSelection?: (id: number) => void;
};

const ProductItem = ({ item, isEditable = false, onToggleSelection }: ProductItemProps) => {
  return (
    <View className="flex-row items-center justify-between bg-white border-b border-gray-100 p-4 ">
      {isEditable && (
        <TouchableOpacity
          style={styles.productCheckbox}
          onPress={() => onToggleSelection?.(item.id)}
        >
          <View style={[styles.checkbox, item.isSelected && styles.checkedCheckbox]}>
            {item.isSelected && <Text style={styles.checkmark}>✓</Text>}
          </View>
        </TouchableOpacity>
      )}

      <Image source={{ uri: item.img }} style={styles.productImage} />

      <View style={styles.productInfo}>
        {/* 品牌名 */}
        <Text className="text-surface-100-fg-minor enki-body-2xs-medium line-clamp-1">{(item as any).brand || '品牌'}</Text>

        {/* 产品名称 */}
        <Text style={styles.productName} numberOfLines={2}>
          {item.name}
        </Text>

        <View style={styles.productSubItem}>

          <View style={styles.productSubInfo}>
             {/* 价格行 */}
              <View style={styles.priceRow}>
                <Text style={styles.price}>{item.price}</Text>
                {item.base_price && (
                  <Text style={styles.originalPrice}>{item.base_price}</Text>
                )}
              </View>

              {/* 促销标签 */}
          </View>

          <View style={styles.buttonContainer}>
            <WeeeAtcButton product={item} />
            {/* <AddToCartButton /> */}
          </View>
         
        </View>

      </View>

      
    </View>
  );
};

const styles = StyleSheet.create({
  productItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },

  productSubItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  productCheckbox: {
    marginRight: 12,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: '#E5E5E5',
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkedCheckbox: {
    backgroundColor: '#0A72BA',
    borderColor: '#0A72BA',
  },
  checkmark: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  productImage: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginRight: 16,
    backgroundColor: '#F8F8F8',
  },
  productInfo: {
    flex: 1,
    paddingRight: 12,
  },
  productSubInfo: {
    width: 100,
  },
  brandName: {
    fontSize: 12,
    color: '#666666',
    marginBottom: 4,
    fontWeight: '500',
  },
  productName: {
    fontSize: 16,
    color: '#333333',
    lineHeight: 22,
    marginBottom: 8,
    fontWeight: '400',
  },
  priceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  price: {
    fontSize: 18,
    color: '#E53E3E',
    fontWeight: 'bold',
    marginRight: 8,
    fontFamily: "PoppinsRegular"
  },
  originalPrice: {
    fontSize: 14,
    color: '#999999',
    textDecorationLine: 'line-through',
  },
  promotionRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
  },
  discountBadge: {
    backgroundColor: '#E53E3E',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  discountText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  cashbackBadge: {
    backgroundColor: '#F7D917',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  cashbackText: {
    color: '#333333',
    fontSize: 10,
    fontWeight: 'bold',
  },
  limitedBadge: {
    backgroundColor: '#38B2AC',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  limitedText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  buttonContainer: {
    flex: 1,
    //alignSelf: 'flex-end',
    //justifyContent: 'center',
    //alignItems: 'center',
    //alignSelf: 'flex-end',
  
  },
});

export default ProductItem;