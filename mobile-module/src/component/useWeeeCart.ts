import { useState, useCallback, useRef } from 'react';
import { ProductBean } from '@/api/model';

export interface CartItem {
    productId: number;
    productKey: string;
    quantity: number;
    product: ProductBean;
    addedAt: number;
}

export interface CartState {
    items: CartItem[];
    totalItems: number;
    totalPrice: number;
    lastAction: string;
}

export interface UseWeeeCartReturn {
    cartState: CartState;
    addToCart: (product: ProductBean, quantity: number) => void;
    removeFromCart: (productId: number) => void;
    updateQuantity: (productId: number, quantity: number) => void;
    clearCart: () => void;
    getItemQuantity: (productId: number) => number;
    isInCart: (productId: number) => boolean;
    // WeeeAtc 组件回调
    handleQuantityChange: (quantity: number, productId: number) => void;
    handleStatusChange: (status: string, productId: number) => void;
    handleCartUpdate: (productId: number, quantity: number, action: 'add' | 'remove' | 'update') => void;
    handleError: (error: string, productId: number) => void;
}

export const useWeeeCart = (
    onCartChange?: (cartState: CartState) => void,
    onError?: (error: string, productId?: number) => void
): UseWeeeCartReturn => {
    const [cartState, setCartState] = useState<CartState>({
        items: [],
        totalItems: 0,
        totalPrice: 0,
        lastAction: '',
    });

    const productsRef = useRef<Map<number, ProductBean>>(new Map());

    // 计算购物车统计信息
    const calculateCartStats = useCallback((items: CartItem[]): Omit<CartState, 'items' | 'lastAction'> => {
        const totalItems = items.reduce((sum, item) => sum + item.quantity, 0);
        const totalPrice = items.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);
        
        return { totalItems, totalPrice };
    }, []);

    // 更新购物车状态
    const updateCartState = useCallback((
        updater: (prevItems: CartItem[]) => CartItem[],
        action: string
    ) => {
        setCartState(prevState => {
            const newItems = updater(prevState.items);
            const stats = calculateCartStats(newItems);
            const newState = {
                items: newItems,
                lastAction: action,
                ...stats,
            };
            
            // 触发外部回调
            onCartChange?.(newState);
            
            return newState;
        });
    }, [calculateCartStats, onCartChange]);

    // 添加商品到购物车
    const addToCart = useCallback((product: ProductBean, quantity: number) => {
        if (quantity <= 0) return;
        
        productsRef.current.set(product.id, product);
        
        updateCartState(
            prevItems => {
                const existingIndex = prevItems.findIndex(item => item.productId === product.id);
                
                if (existingIndex >= 0) {
                    const newItems = [...prevItems];
                    newItems[existingIndex].quantity = quantity;
                    return newItems;
                } else {
                    return [...prevItems, {
                        productId: product.id,
                        productKey: product.product_key,
                        quantity,
                        product,
                        addedAt: Date.now(),
                    }];
                }
            },
            `添加商品 ${product.name} 数量 ${quantity}`
        );
    }, [updateCartState]);

    // 从购物车移除商品
    const removeFromCart = useCallback((productId: number) => {
        updateCartState(
            prevItems => prevItems.filter(item => item.productId !== productId),
            `移除商品 ${productId}`
        );
    }, [updateCartState]);

    // 更新商品数量
    const updateQuantity = useCallback((productId: number, quantity: number) => {
        if (quantity <= 0) {
            removeFromCart(productId);
            return;
        }

        updateCartState(
            prevItems => {
                const existingIndex = prevItems.findIndex(item => item.productId === productId);
                
                if (existingIndex >= 0) {
                    const newItems = [...prevItems];
                    newItems[existingIndex].quantity = quantity;
                    return newItems;
                }
                
                return prevItems;
            },
            `更新商品 ${productId} 数量为 ${quantity}`
        );
    }, [updateCartState, removeFromCart]);

    // 清空购物车
    const clearCart = useCallback(() => {
        updateCartState(() => [], '清空购物车');
    }, [updateCartState]);

    // 获取商品数量
    const getItemQuantity = useCallback((productId: number): number => {
        const item = cartState.items.find(item => item.productId === productId);
        return item?.quantity || 0;
    }, [cartState.items]);

    // 检查商品是否在购物车中
    const isInCart = useCallback((productId: number): boolean => {
        return cartState.items.some(item => item.productId === productId);
    }, [cartState.items]);

    // WeeeAtc 组件回调处理
    const handleQuantityChange = useCallback((quantity: number, productId: number) => {
        console.log(`商品 ${productId} 数量变更为: ${quantity}`);
        // 这里可以添加额外的业务逻辑，比如发送分析事件
    }, []);

    const handleStatusChange = useCallback((status: string, productId: number) => {
        console.log(`商品 ${productId} 状态变更: ${status}`);
        
        // 根据状态执行不同操作
        switch (status) {
            case 'increase':
                // 增加商品时的逻辑
                console.log(`商品 ${productId} 增加操作`);
                break;
            case 'decrease':
                // 减少商品时的逻辑
                console.log(`商品 ${productId} 减少操作`);
                break;
            case 'numClick':
                // 点击数量时的逻辑
                const currentQuantity = getItemQuantity(productId);
                if (currentQuantity > 0) {
                    console.log(`商品 ${productId} 展开编辑界面，当前数量: ${currentQuantity}`);
                } else {
                    console.log(`商品 ${productId} 从数量点击触发首次添加`);
                }
                break;
        }
    }, [getItemQuantity]);

    const handleCartUpdate = useCallback((productId: number, quantity: number, action: 'add' | 'remove' | 'update') => {
        try {
            const product = productsRef.current.get(productId);
            
            if (!product) {
                onError?.(`未找到商品信息: ${productId}`, productId);
                return;
            }

            switch (action) {
                case 'add':
                    addToCart(product, quantity);
                    break;
                case 'remove':
                    removeFromCart(productId);
                    break;
                case 'update':
                    updateQuantity(productId, quantity);
                    break;
            }
        } catch (error) {
            onError?.(`购物车更新失败: ${error}`, productId);
        }
    }, [addToCart, removeFromCart, updateQuantity, onError]);

    const handleError = useCallback((error: string, productId: number) => {
        console.error(`WeeeAtc 错误 [${productId}]:`, error);
        onError?.(error, productId);
    }, [onError]);

    return {
        cartState,
        addToCart,
        removeFromCart,
        updateQuantity,
        clearCart,
        getItemQuantity,
        isInCart,
        handleQuantityChange,
        handleStatusChange,
        handleCartUpdate,
        handleError,
    };
};