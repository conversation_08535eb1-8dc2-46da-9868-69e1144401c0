import React, { useEffect, useRef, useState, memo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Button,
  NativeModules,
} from 'react-native';

import { getBoughtList, getFavoriteList, purchaseNormalV3, deleteFavoriteList } from '@/api/item';
import { ProductBean, PurchaseItemReq } from '@/api/model';
import { RecyclerView } from '@shopify/flash-list';
import { LoadingAnimation } from '@/component/loading';
import { SecondCuisineCategory } from '@/model';
import ContentLoader from 'react-content-loader';
import { MotiView } from 'moti';
import { Skeleton } from 'moti/skeleton';

import { AutoSkeletonView } from 'react-native-auto-skeleton';
import { SceneMap, TabView } from 'react-native-tab-view';
import TabViewExample, { MYLIST_TYPE } from './page-segment';

import { Toast, Cache, Sync } from "weee-native";
import ProductItem from '@/component/product/ProductItem';

import { useTranslation } from 'react-i18next';



// const MyLoader = () => (
//   <ContentLoader
//     height={140}
//     speed={1}
//     backgroundColor={'#333'}
//     foregroundColor={'#999'}
//     viewBox="0 0 380 70"
//   >
//     {/* Only SVG shapes */}
//     <rect x="0" y="0" rx="5" ry="5" width="70" height="70" />
//     <rect x="80" y="17" rx="4" ry="4" width="300" height="13" />
//     <rect x="80" y="40" rx="3" ry="3" width="250" height="10" />
//   </ContentLoader>
// )


function GoToCartButton () {
  const { t } = useTranslation();
  return (
      <View className="enki-button-secondary py-6 mx-8 mb-8 rounded-full justify-center items-center">
          <Text className="enki-display-4xl text-white text-base font-bold">{t('go_to_cart')} 9</Text>
      </View>
  ); 
}

type SegmentProductProps = {
  isEditable: boolean,
  type: string,
  toogleEditable: () => void,
}


const SegmentProduct = (props: SegmentProductProps) => {
  const [selectedTab, setSelectedTab] = useState('');
  const [selectAll, setSelectAll] = useState(false);
  const [isEmpty, setEmpty] = useState(false);

  const [categories, setCategories] = useState<SecondCuisineCategory[]>([]);
  const [products, setProducts] = useState<ProductBean[]>([]);

  const requestMap = {
    [MYLIST_TYPE.watchlist]: getFavoriteList,
    [MYLIST_TYPE.bought]: getBoughtList,
  };

  const queryParamsRef = useRef({
    offset: 0,
    limit: 4,
    hasMore: true,
    filter_sub_category: ''
  })

  const fetchProducts = async (req: any) => {
    try {
      const reqParams = {
        ...queryParamsRef.current
      }
      const response = await req(reqParams);
      const data = JSON.parse(response.data);

      // category data
      const categories = data?.categories || [];
      if (categories.length > 0) {
        setCategories([{ "catalogue_name": "全部", "catalogue_num": "" }, ...categories]);
      } else {
        setCategories(categories)
      }

      // product data
      if (queryParamsRef.current.offset === 0) {
        setProducts(data.products);
        setEmpty((data.products.length == 0))
      } else {
        setProducts([...products, ...data.products]);
      }

      // query params
      queryParamsRef.current.offset += queryParamsRef.current.limit;
      queryParamsRef.current.hasMore = data.filter_total_count > queryParamsRef.current.offset;

    } catch (error) {
      console.error('Error fetching products:', error);
    }
  };

  const filterByCategory = async (category: SecondCuisineCategory) => {
    queryParamsRef.current.filter_sub_category = category.catalogue_num;
    queryParamsRef.current.offset = 0;
    queryParamsRef.current.hasMore = true;
    // setProducts([]);
    fetchProducts(requestMap[props.type]);
  };

  useEffect(() => {

    fetchProducts(requestMap[props.type]);

  }, [props.type]);

  const toggleSelectAll = () => {
    const newSelectAll = !selectAll;
    setSelectAll(newSelectAll);
    setProducts(products.map(product => ({ ...product, isSelected: newSelectAll })));
  };

  const toggleProductSelection = (id: number) => {
    const new_p = products.map(product =>
      product.id === id ? { ...product, isSelected: !product.isSelected } : product
    )
    setProducts(new_p);
    new_p.filter(p => p.isSelected).length === products.length ? setSelectAll(true) : setSelectAll(false);
  };

  const addToCart = (bean: ProductBean) => {
    // Handle add to cart functionality
    const req: PurchaseItemReq[] = [{
      product_id: bean.id,
      delivery_date: Cache.deliveryDate(),
      quantity: 1,
      source: 'mweb_me_my_watchlist-item_list-null',
      refer_type: bean.biz_type,
      refer_value: "",
      new_source: '{"mod_nm":"item_list","mod_pos":1,"prod_pos":0,"page_key":"mweb_me_my_watchlist","referer_page_key":"mweb_me","view_id":"d6f07bdb5b9f4712887b832b48c47eb3","referer_view_id":"c6d2195683dc4c51a2232cdfeb45df0f"}',
    }];

    // purchaseNormalV3(req).then(() => {
    //   // 加购成功后显示成功动画
    //   Toast.show("Added to cart successfully!");
    // }).catch(() => {
    //   Toast.show("Failed to add to cart");
    // });
  };

  const deleteSelected = () => {
    const ids = products.filter(p => p.isSelected).map(product => product.id);
    if (!ids.length) return;
    // sync native 

    // delete api
    const req = {
      is_watch: false,
      product_ids: ids
    }
    const resp = deleteFavoriteList(req);
    if (resp.result) {
      // init 
      Toast.show("Delete success!");
      setProducts(products.filter(p => !p.isSelected))
      props.toogleEditable();
      Sync.collectStatusNotify(ids, "remove", "product")
    } else {
    }
  };


  const renderTabSection = () => (
    <View>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.tabScrollView}>
        {categories.map((category) => (
          <TouchableOpacity
            key={category.catalogue_name}
            style={[styles.tab, selectedTab === category.catalogue_num && styles.activeTab]}
            onPress={() => {
              setSelectedTab(category.catalogue_num)
              filterByCategory(category)
            }
            }
          >
            <Text style={[styles.tabText, selectedTab === category.catalogue_num && styles.activeTabText]}>
              {category.catalogue_name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderSelectAllSection = () => (
    props.isEditable ? (
      <View style={styles.selectAllSection}>
        <TouchableOpacity style={styles.selectAllButton} onPress={toggleSelectAll}>
          <View style={[styles.checkbox, selectAll && styles.checkedCheckbox]}>
            {selectAll && <Text style={styles.checkmark}>✓</Text>}
          </View>
          <Text style={styles.selectAllText}>Select All</Text>
        </TouchableOpacity>
      </View>
    ) : (
      <View />
    )
  );

  const Spacer = ({ height = 16 }) => <View style={{ height }} />;

  const colorMode = 'light';

  const motionSkeleton = () => (
    <View>
      <MotiView
        transition={{
          type: 'timing',
        }}
        style={{ backgroundColor: '#ffffff', borderRadius: 8 }}
        animate={{ backgroundColor: '#ffffff' }}
      >
        <Skeleton colorMode={colorMode} radius="round" height={75} width={75} />
        <Spacer />
        <Skeleton colorMode={colorMode} width={250} />
        <Spacer height={8} />
        <Skeleton colorMode={colorMode} width={'100%'} />
        <Spacer height={8} />
        <Skeleton colorMode={colorMode} width={'100%'} />
      </MotiView>
    </View>
  );



  const renderListItem = ({ item }: { item: { itemType: string; item: any } }) => {
    switch (item.itemType) {
      case "select":
        return renderSelectAllSection();
      case "product":
        return (
          <ProductItem
            item={item.item}
            isEditable={props.isEditable}
            onToggleSelection={toggleProductSelection}
          />
        );
      default:
        return <View />;
    }
  };

  const renderFooter = () => {
    // load more animation
    if (queryParamsRef.current.hasMore) {
      return (
        <View style={{ height: 50, justifyContent: 'center', alignItems: 'center' }}>
          <LoadingAnimation />
        </View>
      );
    }
    // 如果没有在加载，则不渲染任何东西
    return null;
  };

  const renderEmpty = () => {
    return <View style={{ height: "100%", justifyContent: 'center', alignItems: 'center' }}>
      <Text style={{ alignContent: 'center' }}>Empty</Text>
    </View>
  }

  if (isEmpty) {
    return renderEmpty();
  }

  return (
    <View style={styles.container}>
      {renderTabSection()}
      {/* <AutoSkeletonView isLoading={true}>
        {renderSelectAllSection()}
      </AutoSkeletonView> */}
      {/* {motionSkeleton()} */}

      <RecyclerView
        data={[{ itemType: "select", item: "" }, ...products.map((item: ProductBean) => ({ itemType: "product", item }))]}
        renderItem={renderListItem}
        keyExtractor={(item, index) => index.toString()}
        style={styles.productList}
        showsVerticalScrollIndicator={false}
        estimatedItemSize={15}
        onEndReached={() => {
          // Load more products
          if (!queryParamsRef.current.hasMore) {
            return;
          }
          fetchProducts(requestMap[props.type]);
        }}
        onStartReached={() => {
          // Load products
        }}
        onEndReachedThreshold={0.5}
        ListFooterComponent={renderFooter}

      />

      {!(props.isEditable) ? (
        <GoToCartButton />
      ) : (
        <View style={styles.bottomButton}>
          <TouchableOpacity style={styles.goToCartButton} onPress={deleteSelected}>
            <Text style={styles.goToCartText}>Delete</Text>
          </TouchableOpacity>
        </View>
      )}

    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  safeArea: {
    backgroundColor: '#FFFFFF',
  },
  tabSection: {
    backgroundColor: '#F8F8F8',
    paddingVertical: 12,
  },
  savedItemsSection: {
    paddingHorizontal: 16,
    marginBottom: 12,
  },
  savedItemsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
  },
  savedItemsLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  bookmarkIcon: {
    fontSize: 16,
    marginRight: 8,
  },
  savedItemsText: {
    fontSize: 16,
    color: '#333333',
    fontWeight: '500',
  },
  buyAgainSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  refreshIcon: {
    fontSize: 16,
    marginRight: 4,
    color: '#666666',
  },
  buyAgainText: {
    fontSize: 14,
    color: '#666666',
  },
  tabScrollView: {
    paddingHorizontal: 16,
  },
  tab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E5E5',
  },
  activeTab: {
    backgroundColor: '#0A72BA',
    borderColor: '#0A72BA',
  },
  tabText: {
    fontSize: 14,
    color: '#666666',
  },
  activeTabText: {
    color: '#FFFFFF',
    fontWeight: '500',
  },
  selectAllSection: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
  },
  selectAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: '#E5E5E5',
    borderRadius: 4,
    marginRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkedCheckbox: {
    backgroundColor: '#0A72BA',
    borderColor: '#0A72BA',
  },
  checkmark: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  selectAllText: {
    fontSize: 16,
    color: '#666666',
  },
  productList: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  promotionBadge: {
    backgroundColor: '#FF8C14',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  promotionText: {
    fontSize: 10,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  deliveryInfo: {
    fontSize: 12,
    color: '#666666',
    lineHeight: 16,
  },
  addButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#0A72BA',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 4,
  },
  addButtonText: {
    fontSize: 20,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  bottomButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#F3F3F3',
  },
  goToCartButton: {
    backgroundColor: '#00BFFF',
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  goToCartText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  shape: {
    justifyContent: 'center',
    height: 250,
    width: 250,
    borderRadius: 25,
    marginRight: 10,
    backgroundColor: 'white',
  },
  padded: {
    padding: 16,
  },
});

export default SegmentProduct;