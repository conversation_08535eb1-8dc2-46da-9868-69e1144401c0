// This file is for syncing up the latest variables to Storybook

const fs = require('fs');
const path = require('path');
const Constants = require('../../src/config/constants')

// _______________________________________
// _______________________________________
// _______________________________________  
// Get args
const args = process.argv.slice(2).reduce((acc, arg) => {
  const [key, value] = arg.replace('--', '').split('=');
  acc[key] = value;
  return acc;
}, {});

// _______________________________________
// _______________________________________
// _______________________________________  
// Input array of directories

const directories = [
    // [from, to],
    [`${Constants.V2_TAILWIND_EMBEDDED_OUTPUT_DIR}${Constants.V2_TAILWIND_EMBEDDED_CONFIG_FILENAME}`, `${Constants.V2_EXAMPLES_ROOT}${Constants.V2_STORYBOOK_DIR}-${args.TOKEN_LIB}/${Constants.V2_TAILWIND_CONFIG_ROOT_DIR}${Constants.V2_TAILWIND_CONFIG_FILENAME}`],
    [`${Constants.V2_TAILWIND_EMBEDDED_OUTPUT_DIR}lib/`, `${Constants.V2_EXAMPLES_ROOT}${Constants.V2_STORYBOOK_DIR}-${args.TOKEN_LIB}/${Constants.V2_TAILWIND_CONFIG_ROOT_DIR}lib/`],
    [`${Constants.V2_TAILWIND_EMBEDDED_OUTPUT_DIR}styles/`, `${Constants.V2_EXAMPLES_ROOT}${Constants.V2_STORYBOOK_DIR}-${args.TOKEN_LIB}/${Constants.V2_TAILWIND_CONFIG_ROOT_DIR}styles/`],
    [`${Constants.V2_TAILWIND_EMBEDDED_OUTPUT_DIR}lib/`, `${Constants.V2_EXAMPLES_ROOT}${Constants.V2_STORYBOOK_DIR}-${args.TOKEN_LIB}/${Constants.V2_NEXTJS_DATA_DIR}`],
    [`${Constants.V2_TAILWIND_EMBEDDED_OUTPUT_DIR}styles/`, `${Constants.V2_EXAMPLES_ROOT}${Constants.V2_STORYBOOK_DIR}-${args.TOKEN_LIB}/${Constants.V2_NEXTJS_DATA_DIR}`],
    [`${Constants.V2_TAILWIND_EMBEDDED_OUTPUT_DIR}css/min/`, `${Constants.V2_EXAMPLES_ROOT}${Constants.V2_STORYBOOK_DIR}-${args.TOKEN_LIB}/public/`],
    [`${Constants.V2_SCSS_OUTPUT_DIR}${Constants.V2_SCSS_BUNDLE_FILENAME}`, `${Constants.V2_EXAMPLES_ROOT}${Constants.V2_STORYBOOK_DIR}-${args.TOKEN_LIB}/${Constants.V2_NEXTJS_STYLES_DIR}${Constants.V2_SCSS_BUNDLE_FILENAME}`]
];

// Function to copy files
function copyFile(fromPath, toPath) {
  // Ensure the target directory exists
  const toDir = path.dirname(toPath);
  if (!fs.existsSync(toDir)) {
      fs.mkdirSync(toDir, { recursive: true });
  }

  // Read the file content
  fs.readFile(fromPath, (err, data) => {
      if (err) {
          console.error(`⛔️ Failed to read ${fromPath}:`, err);
          return;
      }

      // Write the file content to the destination
      fs.writeFile(toPath, data, (err) => {
          if (err) {
              console.error(`⛔️ Failed to write ${toPath}:`, err);
          } else {
              console.log(`📥 Copied ${fromPath} to ${toPath}`);
          }
      });
  });
}

// Function to copy directories recursively
function copyDirectory(fromDir, toDir) {
  if (!fs.existsSync(toDir)) {
      fs.mkdirSync(toDir, { recursive: true });
  }

  fs.readdir(fromDir, { withFileTypes: true }, (err, files) => {
      if (err) {
          console.error(`Failed to read directory ${fromDir}:`, err);
          return;
      }

      files.forEach(file => {
          const fromPath = path.join(fromDir, file.name);
          const toPath = path.join(toDir, file.name);

          if (file.isDirectory()) {
              copyDirectory(fromPath, toPath);
          } else {
              copyFile(fromPath, toPath);
          }
      });
  });
}

// Function to process the directories array
function processDirectories(directories) {
  directories.forEach(([fromPath, toPath]) => {
      fs.stat(fromPath, (err, stats) => {
          if (err) {
              console.error(`Failed to stat ${fromPath}:`, err);
              return;
          }

          if (stats.isDirectory()) {
              copyDirectory(fromPath, toPath);
          } else if (stats.isFile()) {
              copyFile(fromPath, toPath);
          } else {
              console.error(`${fromPath} is neither a file nor a directory.`);
          }
      });
  });
}

function init() {
  console.log("");
  console.log("");
  console.log("┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓");
  console.log("┃                                                    ┃");
  console.log("┃     💠 Updating Storybook with fresh tokens...     ┃");
  console.log("┃                                                    ┃");
  console.log("┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛");
  console.log("");
  console.log("");

  processDirectories(directories);

  setTimeout(() => {
    // console.log("");
    // console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    // console.log("✨ Example projects updated!");
    // console.log("👀 Select an example project to test your tokens ↴");
    // console.log("");
    // console.log(`> ✅ cd ${Constants.V2_EXAMPLES_ROOT}${Constants.V2_EXAMPLE_NEXTJS_WITH_TAILWIND_DIR}`);
    // console.log(`> ✅ cd ${Constants.V2_EXAMPLES_ROOT}${Constants.V2_EXAMPLE_NEXTJS_EMBEDDED_DIR}`);
    console.log("");
    console.log("");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    console.log("📚 Storybook updated!");
    console.log("👀 Navigate to Storybook to test or build components with your new tokens ↴");
    console.log("");
    console.log(`> ✅ cd ${Constants.V2_EXAMPLES_ROOT}${Constants.V2_STORYBOOK_DIR}-${args.TOKEN_LIB}`);
    console.log("");
    console.log("");
  }, 2000)
}

init()