interface LANG_GROUP_LIST {
  LATIN: string;
  TALL: string;
  CJK: string;
}

const LANG_GROUP: LANG_GROUP_LIST = {
  LATIN: `latin`,
  TALL: `tall`,
  CJK: `cjk`
};

interface LangKeys {
  [key: string]: string; // Index signature
}

const LANG_KEYS: LangKeys = {
  en: LANG_GROUP.LATIN,
  vi: LANG_GROUP.TALL,
  zh: LANG_GROUP.CJK,
  zht: LANG_GROUP.CJK,
  ko: LANG_GROUP.CJK,
  ja: LANG_GROUP.CJK
};

export function getLangGroup(lang: string): string  {
  if (LANG_KEYS[lang]) {
    return LANG_KEYS[lang];
  } else {
    return LANG_GROUP.LATIN;
  }
}
