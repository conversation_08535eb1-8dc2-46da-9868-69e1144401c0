const fs = require("fs-extra");
const path = require("path");

const submodulePath = path.join(__dirname, "../../");

const paths = {
  // key: [copy from, copy to],
  // This folder (/utils/tailwind/) must be in your-repo/vendor/weee-ui
  stylesScss: [
    path.join(submodulePath, 'dist/scss/enki.scss'),
    path.join(__dirname, '../../../../src/assets/styles/enki.scss')
  ],
  langGroupUtil: [
    path.join(submodulePath, 'utils/tailwind/getLangGroup.ts'),
    path.join(__dirname, '../../../../src/utils/getLangGroup.ts')
  ]

  // _______________________________________________
  // Uncomment once we have production ready components. 

  // enkiComponents: [
  //   path.join(submodulePath, "storybook/src/components/"),
  //   path.join(__dirname, "../../../../src/components-enki/"),
  // ],

  // _______________________________________________
  // Uncomment if we don't want to load from CDN.

  // publicLatinCss: [
  //   path.join(submodulePath, "dist/tailwind/embedded/css/min/main.latin.min.css"),
  //   path.join(__dirname, "../src/assets/styles/enki-inline/main.latin.min.css"),
  // ],
  // publicCjkCss: [
  //   path.join(submodulePath, "dist/tailwind/embedded/css/min/main.cjk.min.css"),
  //   path.join(__dirname, "../src/assets/styles/enki-inline/main.cjk.min.css"),
  // ],
  // publicTallCss: [
  //   path.join(submodulePath, "dist/tailwind/embedded/css/min/main.tall.min.css"),
  //   path.join(__dirname, "../src/assets/styles/enki-inline/main.tall.min.css"),
  // ],
};

// Define file extensions to ignore
const ignoreExtensions = [".stories.ts", ".mdx"];

const shouldIgnoreFile = (filePath) => {
  return ignoreExtensions.some((ext) => filePath.endsWith(ext));
};

const addHeaderToFile = async (srcFile, destFile, header = ``) => {
  if (shouldIgnoreFile(srcFile)) return;

  // Ensure the destination directory exists
  await fs.ensureDir(path.dirname(destFile));

  // Read the file content
  const content = await fs.readFile(srcFile, "utf8");

  // Add header and write the file to the destination
  await fs.writeFile(destFile, `${header ? `${header}\n\n\n` : ``}${content}`);
};

const processDirectory = async (srcDir, destDir, header = ``) => {
  const files = await fs.readdir(srcDir);
  for (const file of files) {
    const srcFile = path.join(srcDir, file);
    const destFile = path.join(destDir, file);
    await processPath(srcFile, destFile, header); // Recurse into the directory
  }
};

const processPath = async (srcPath, destPath, header = ``) => {
  const stat = await fs.stat(srcPath);
  if (stat.isDirectory()) {
    await fs.ensureDir(destPath);
    await processDirectory(srcPath, destPath, header);
  } else {
    await addHeaderToFile(srcPath, destPath, header);
  }
};

async function syncFiles() {
  try {
    // Ensure destination directories exist for all paths and empty them
    await Promise.all(
      Object.values(paths).map(async ([srcPath, destPath]) => {
        const stat = await fs.stat(srcPath);
        if (stat.isDirectory()) {
          console.log(`🔎 Checking and emptying directory '${destPath}'`);
          await fs.emptyDir(destPath); // Empty the destination directory
        } else {
          console.log(`🔎 Ensuring directory for file '${destPath}'`);
          await fs.ensureDir(path.dirname(destPath)); // Ensure parent directory exists for files
        }
      }),
    );

    // Copy files and add headers where necessary
    for (const [key, [srcPath, destPath]] of Object.entries(paths)) {
      console.log(`🔗 Linking file '${srcPath}' to '${destPath}'`);

      let header = ``;

      if (key === "enkiComponents" || key === "langGroupUtil") {
        header = `
/**
 * 🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴
 * ⛔️ DO NOT EDIT THIS FILE DIRECTLY, THIS FILE IS SYMLINKED TO THE GIT SUBMODULE '${submodulePath}'
 * 🟢 INSTEAD, EDIT THIS FILE AT ${srcPath} AND RUN \`npm run enki-sync\` TO RELINK YOUR FILE
 * 🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴🔴
 **/
`.trim();

        await processPath(srcPath, destPath, header);
      } else {
        await processPath(srcPath, destPath);
      }
    }

    console.log("✅ Files synchronized successfully.");
  } catch (err) {
    console.error("⛔️ Error synchronizing files:", err);
  }
}

syncFiles();
