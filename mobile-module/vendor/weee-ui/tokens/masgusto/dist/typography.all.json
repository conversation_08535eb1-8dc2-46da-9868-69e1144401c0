{"font": {"family": {"latin": {"main": {"value": "<PERSON><PERSON><PERSON>"}, "mono": {"value": "Roboto Mono"}, "expressive": {"value": "Random Grotesque Standard Medium"}, "alt": {"1": {"value": "Playfair Display"}, "2": {"value": "Brownhill Script"}}, "body": {"value": "{font.family.latin.main}"}, "heading": {"value": "{font.family.latin.main}"}, "display": {"value": "{font.family.latin.expressive}"}}, "tall": {"main": {"value": "Be Vietnam Pro"}, "expressive": {"value": "Random Grotesque Standard Medium"}, "body": {"value": "{font.family.tall.main}"}, "heading": {"value": "{font.family.tall.main}"}, "display": {"value": "{font.family.tall.expressive}"}}, "cjk": {"main": {"value": "SF UI Text"}, "body": {"value": "Noto Sans SC"}, "heading": {"value": "Noto Sans SC"}, "display": {"value": "Noto Sans SC"}}}, "latin": {"display": {"5xl": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.7xl}"}, "lineheight": {"value": "{font.lineheight.100}"}, "family": {"value": "{font.family.latin.display}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}, "4xl": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.6xl}"}, "lineheight": {"value": "{font.lineheight.100}"}, "family": {"value": "{font.family.latin.display}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}, "3xl": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.5xl}"}, "lineheight": {"value": "{font.lineheight.100}"}, "family": {"value": "{font.family.latin.display}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}, "2xl": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.4xl}"}, "lineheight": {"value": "{font.lineheight.100}"}, "family": {"value": "{font.family.latin.display}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}, "xl": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.3xl}"}, "lineheight": {"value": "{font.lineheight.100}"}, "family": {"value": "{font.family.latin.display}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}, "lg": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.2xl}"}, "lineheight": {"value": "{font.lineheight.100}"}, "family": {"value": "{font.family.latin.display}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}, "sm": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.xl}"}, "lineheight": {"value": "{font.lineheight.100}"}, "family": {"value": "{font.family.latin.display}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}}, "heading": {"5xl": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.6xl}"}, "lineheight": {"value": "{font.lineheight.110}"}, "family": {"value": "{font.family.latin.heading}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}, "4xl": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.5xl}"}, "lineheight": {"value": "{font.lineheight.110}"}, "family": {"value": "{font.family.latin.heading}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}, "3xl": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.4xl}"}, "lineheight": {"value": "{font.lineheight.110}"}, "family": {"value": "{font.family.latin.heading}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}, "2xl": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.3xl}"}, "lineheight": {"value": "{font.lineheight.110}"}, "family": {"value": "{font.family.latin.heading}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}, "xl": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.2xl}"}, "lineheight": {"value": "{font.lineheight.110}"}, "family": {"value": "{font.family.latin.heading}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.500.medium}"}}, "lg": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.xl}"}, "lineheight": {"value": "{font.lineheight.110}"}, "family": {"value": "{font.family.latin.heading}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}, "sm": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.lg}"}, "lineheight": {"value": "{font.lineheight.110}"}, "family": {"value": "{font.family.latin.heading}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}}, "body": {"2xl": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.2xl}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.latin.body}"}, "weight": {"value": "{font.weight.400.regular}"}, "weightmedium": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}, "xl": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.xl}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.latin.body}"}, "weight": {"value": "{font.weight.400.regular}"}, "weightmedium": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}, "lg": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.lg}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.latin.body}"}, "weight": {"value": "{font.weight.400.regular}"}, "weightmedium": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}, "base": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.base}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.latin.body}"}, "weight": {"value": "{font.weight.400.regular}"}, "weightmedium": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}, "sm": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.sm}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.latin.body}"}, "weight": {"value": "{font.weight.400.regular}"}, "weightmedium": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}, "xs": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.xs}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.latin.body}"}, "weight": {"value": "{font.weight.400.regular}"}, "weightmedium": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}, "2xs": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.2xs}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.latin.body}"}, "weight": {"value": "{font.weight.400.regular}"}, "weightmedium": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}, "3xs": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.3xs}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.latin.body}"}, "weight": {"value": "{font.weight.400.regular}"}, "weightmedium": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}}}, "cjk": {"display": {"5xl": {"tracking": {"value": "{font.tracking.widest}"}, "size": {"value": "{font.size.6xl}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.cjk.display}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.700.bold}"}}, "4xl": {"tracking": {"value": "{font.tracking.widest}"}, "size": {"value": "{font.size.5xl}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.cjk.display}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.700.bold}"}}, "3xl": {"tracking": {"value": "{font.tracking.widest}"}, "size": {"value": "{font.size.4xl}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.cjk.display}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.700.bold}"}}, "2xl": {"tracking": {"value": "{font.tracking.widest}"}, "size": {"value": "{font.size.3xl}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.cjk.display}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.700.bold}"}}, "xl": {"tracking": {"value": "{font.tracking.widest}"}, "size": {"value": "{font.size.2xl}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.cjk.display}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.700.bold}"}}, "lg": {"tracking": {"value": "{font.tracking.widest}"}, "size": {"value": "{font.size.xl}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.cjk.display}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.700.bold}"}}, "sm": {"tracking": {"value": "{font.tracking.widest}"}, "size": {"value": "{font.size.lg}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.cjk.display}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.700.bold}"}}}, "heading": {"5xl": {"tracking": {"value": "{font.tracking.widest}"}, "size": {"value": "{font.size.6xl}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.cjk.heading}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.700.bold}"}}, "4xl": {"tracking": {"value": "{font.tracking.widest}"}, "size": {"value": "{font.size.5xl}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.cjk.heading}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.700.bold}"}}, "3xl": {"tracking": {"value": "{font.tracking.widest}"}, "size": {"value": "{font.size.4xl}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.cjk.heading}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.700.bold}"}}, "2xl": {"tracking": {"value": "{font.tracking.widest}"}, "size": {"value": "{font.size.3xl}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.cjk.heading}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.700.bold}"}}, "xl": {"tracking": {"value": "{font.tracking.widest}"}, "size": {"value": "{font.size.2xl}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.cjk.heading}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.700.bold}"}}, "lg": {"tracking": {"value": "{font.tracking.widest}"}, "size": {"value": "{font.size.xl}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.cjk.heading}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.700.bold}"}}, "sm": {"tracking": {"value": "{font.tracking.widest}"}, "size": {"value": "{font.size.lg}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.cjk.heading}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.700.bold}"}}}, "body": {"2xl": {"tracking": {"value": "{font.tracking.widest}"}, "size": {"value": "{font.size.2xl}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.cjk.body}"}, "weight": {"value": "{font.weight.400.regular}"}, "weightmedium": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.700.bold}"}}, "xl": {"tracking": {"value": "{font.tracking.widest}"}, "size": {"value": "{font.size.xl}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.cjk.body}"}, "weight": {"value": "{font.weight.400.regular}"}, "weightmedium": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.700.bold}"}}, "lg": {"tracking": {"value": "{font.tracking.widest}"}, "size": {"value": "{font.size.lg}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.cjk.body}"}, "weight": {"value": "{font.weight.400.regular}"}, "weightmedium": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.700.bold}"}}, "base": {"tracking": {"value": "{font.tracking.widest}"}, "size": {"value": "{font.size.base}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.cjk.body}"}, "weight": {"value": "{font.weight.400.regular}"}, "weightmedium": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.700.bold}"}}, "sm": {"tracking": {"value": "{font.tracking.widest}"}, "size": {"value": "{font.size.sm}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.cjk.body}"}, "weight": {"value": "{font.weight.400.regular}"}, "weightmedium": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.700.bold}"}}, "xs": {"tracking": {"value": "{font.tracking.widest}"}, "size": {"value": "{font.size.xs}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.cjk.body}"}, "weight": {"value": "{font.weight.400.regular}"}, "weightmedium": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.700.bold}"}}, "2xs": {"tracking": {"value": "{font.tracking.widest}"}, "size": {"value": "{font.size.2xs}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.cjk.body}"}, "weight": {"value": "{font.weight.400.regular}"}, "weightmedium": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.700.bold}"}}, "3xs": {"tracking": {"value": "{font.tracking.widest}"}, "size": {"value": "{font.size.3xs}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.cjk.body}"}, "weight": {"value": "{font.weight.400.regular}"}, "weightmedium": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.700.bold}"}}}}, "tall": {"display": {"5xl": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.7xl}"}, "lineheight": {"value": "{font.lineheight.110}"}, "family": {"value": "{font.family.tall.display}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}, "4xl": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.6xl}"}, "lineheight": {"value": "{font.lineheight.110}"}, "family": {"value": "{font.family.tall.display}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}, "3xl": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.5xl}"}, "lineheight": {"value": "{font.lineheight.110}"}, "family": {"value": "{font.family.tall.display}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}, "2xl": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.4xl}"}, "lineheight": {"value": "{font.lineheight.110}"}, "family": {"value": "{font.family.tall.display}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}, "xl": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.3xl}"}, "lineheight": {"value": "{font.lineheight.110}"}, "family": {"value": "{font.family.tall.display}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}, "lg": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.2xl}"}, "lineheight": {"value": "{font.lineheight.110}"}, "family": {"value": "{font.family.tall.display}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}, "sm": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.xl}"}, "lineheight": {"value": "{font.lineheight.110}"}, "family": {"value": "{font.family.tall.display}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}}, "heading": {"5xl": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.6xl}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.tall.heading}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.700.bold}"}}, "4xl": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.5xl}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.tall.heading}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.700.bold}"}}, "3xl": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.4xl}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.tall.heading}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.700.bold}"}}, "2xl": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.3xl}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.tall.heading}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.700.bold}"}}, "xl": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.2xl}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.tall.heading}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.700.bold}"}}, "lg": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.xl}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.tall.heading}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.700.bold}"}}, "sm": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.lg}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.tall.heading}"}, "weight": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.700.bold}"}}}, "body": {"2xl": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.2xl}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.tall.body}"}, "weight": {"value": "{font.weight.400.regular}"}, "weightmedium": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}, "xl": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.xl}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.tall.body}"}, "weight": {"value": "{font.weight.400.regular}"}, "weightmedium": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}, "lg": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.lg}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.tall.body}"}, "weight": {"value": "{font.weight.400.regular}"}, "weightmedium": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}, "base": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.base}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.tall.body}"}, "weight": {"value": "{font.weight.400.regular}"}, "weightmedium": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}, "sm": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.sm}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.tall.body}"}, "weight": {"value": "{font.weight.400.regular}"}, "weightmedium": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}, "xs": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.xs}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.tall.body}"}, "weight": {"value": "{font.weight.400.regular}"}, "weightmedium": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}, "2xs": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.2xs}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.tall.body}"}, "weight": {"value": "{font.weight.400.regular}"}, "weightmedium": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}, "3xs": {"tracking": {"value": "{font.tracking.base}"}, "size": {"value": "{font.size.3xs}"}, "lineheight": {"value": "{font.lineheight.125}"}, "family": {"value": "{font.family.tall.body}"}, "weight": {"value": "{font.weight.400.regular}"}, "weightmedium": {"value": "{font.weight.500.medium}"}, "weightstrong": {"value": "{font.weight.600.semibold}"}}}}}}