# Enki Design System
<span>
  <img src="https://img.shields.io/badge/enki%20tokens%20v2.1-6d47e1" />
  <img src="https://img.shields.io/badge/enki%20token%20parser%20v2.1-001ba5" />
  <img src="https://img.shields.io/badge/enki%20css%20parser%20v2.0-f876a5" />
  <img src="https://img.shields.io/badge/enki%20tailwind%20parser%20v2.0-f876a5" />
  <img src="https://img.shields.io/badge/enki%20scss%20parser%20v2.0-f876a5" />
  <img src="https://img.shields.io/badge/enki%20less%20parser%20v2.0-f876a5" />
  <img src="https://img.shields.io/badge/enki%20iOS%20parser%20v1.0-f876a5" />
  <img src="https://img.shields.io/badge/enki%20android%20parser%20v1.0-f876a5" />
  <img src="https://img.shields.io/badge/node%20v17.0.0-07101a" />
  <img src="https://img.shields.io/badge/npm%20v8.1.0-07101a" />
  <img src="https://img.shields.io/badge/style%20dictionary%20v3.7.1-07101a" />
</span>

## ⭐️ Start here: Use Style Dictionary to build styles from tokens
1. Install the project with `npm i` or `yarn i`.
2. Build styles with Style Dictionary with `npm run build` or `yarn build`.
3. Everything will be built into `./dist/{{library_name}}/{{filetype}}`. Read more about {{library_name}} below.
4. Find the filetype you're working in and get started.

[Learn more about Style Dictionary here.](https://amzn.github.io/style-dictionary/#/) 

## ⭐️ Running Storybook
Navigate to the `/examples/next-storybook-weee` or `/examples/next-storybook-masgusto` folder inside the repository for more information and tools.

## ⭐️ More information on the wiki
[Read the wiki for essential information.](https://github.com/sayweee/weee-ui/wiki)

***

# 💈 New: Generate multiple token libraries
As of Q1 2025, we are generating two libraries to support our different consumer facing frontend projects: `masgusto` and `weee`. The build script is set up to generate both libraries, but you can change the argument variables in the `bash` script `build.sh` to generate one or the other.

The `bash` script with the argument examples in `build.sh` is below.

```bash
node ./src/formatter.js --OUTPUT_DIR=dist/weee/ --TOKEN_SOURCE_DIR=tokens/weee/ --TOKEN_LIB=weee
node ./src/init.js --OUTPUT_DIR=dist/weee/ --TOKEN_SOURCE_DIR=tokens/weee/ --TOKEN_LIB=weee
node ./src/cleanup.js --OUTPUT_DIR=dist/weee/ --TOKEN_SOURCE_DIR=tokens/weee/ --TOKEN_LIB=weee

node ./src/formatter.js --OUTPUT_DIR=dist/masgusto/ --TOKEN_SOURCE_DIR=tokens/masgusto/ --TOKEN_LIB=masgusto
node ./src/init.js --OUTPUT_DIR=dist/masgusto/ --TOKEN_SOURCE_DIR=tokens/masgusto/ --TOKEN_LIB=masgusto
node ./src/cleanup.js --OUTPUT_DIR=dist/masgusto/ --TOKEN_SOURCE_DIR=tokens/masgusto/ --TOKEN_LIB=masgusto
```

To put it simply, we run the same build script twice with different arguments to define different token libraries and output directories. All tokens come from the same Figma source, so all tokens, regardless of their library, will have the same keys, but with different values.

# Generated styles

## CSS styles
We generate a few files depending on your use case here. Your `./dist/{{library_name}}/css` directory should look something like the below.

```bash
.
└── .dist/{{library_name}}/
    └── css/
        ├── _lib/ # Source CSS files of CSS bundles
        │   ├── color.css # Default colors file
        │   ├── buttons.css # Button CSS classes
        │   ├── elevation.css # Elevation CSS classes
        │   ├── size.css # Sizing & measurement variables
        │   ├── fonts.{lang}.css # Font variables specific to languages
        │   ├── styles.{lang}.css # Root style variables specific to languages
        │   └── typography.{{ lang }}.css # These are the language typography files
        ├── _lib-noref/ # Source CSS files with all variable references resolved
        ├── min/ # Minified CSS bundles
        │   ├── color.dark.css # Dark mode color minified CSS
        │   └── main.{{ lang }}.css # Final, bundled minified CSS file
        ├── min-hash/ # Minified & hashed CSS bundles
        │   ├── color.dark.{{ hash }}.css # Dark mode color minified & hashed CSS
        │   └── main.{{ lang }}.{{ hash }}.css # Final, bundled minified & hashed CSS file
        ├── color.dark.css # Dark mode color CSS
        └── main.{{ lang }}.css # Final, bundled CSS file
```


### Loading CSS
Because we are using variables, order matters and there are notes in the file header for order. A simple example below if you aren't bundling and minifying your css into one file already.

**HTML  ↴**
```html
  <!-- Root styles -->
  <link rel="stylesheet" href="css/min/main.{{ lang }}.min.css"> 

  <!-- Optional: Dark mode colors -->
  <link rel="stylesheet" media="(prefers-color-scheme: dark)" href="css/min/colors.dark.min.css"> 
```


## Tailwind styles
This repo generates a custom Tailwind configuration file with extentions to use Enki variables. It exports a simple `withEnkiConfig` function to wrap your any of your additional configuration in.

We generate two types of Tailwind token styles -- one that is strongly based on Tailwind configuration and based on embedded variables for a lighter weight build, and another that is based on CSS variables. 

[Read more about adding Enki to Next.js projects on the wiki](https://github.com/sayweee/weee-ui/wiki/Adding-to-Next.js-Tailwind-Projects)

```bash
.
└── .dist/{{library_name}}/
    │   
    │   # This is the Tailwind setup if you want to use an embedded config -- you'll have a smaller CSS size here.
    ├── tailwind/embedded
    │   ├── css/ # This is required CSS for variable references to work
    │   │   ├── _lib/ # Source CSS files of CSS bundles
    │   │   │   ├── styles.{lang}.css # All variables for each language
    │   │   │   └── fonts.{lang}.root.css # Font variables
    │   │   ├── min/ # Minified CSS bundles
    │   │   │   └── main.{{ lang }}.css # Final, bundled minified CSS file
    │   │   ├── min-hash/ # Minified & hashed CSS bundles
    │   │   │   └── main.{{ lang }}.{{ hash }}.css # Final, bundled minified & hashed CSS file
    │   │   └── main.{lang}.css # Final, bundled CSS file
    │   ├── lib/ # This is generated for our Tailwind plugins for font, button, & elevation styles
    │   │   ├── elevation-classes.tailwind.js # Elevation classes.
    │   │   ├── button-classes.tailwind.js # Button classes.
    │   │   └── font-classes.tailwind.js # Font & typography classes.
    │   ├── styles/ # These are font specific styles and classes/
    │   │   ├── color.tailwind.js # Color styles. This all references CSS variables
    │   │   ├── font.tailwind.js # Font styles. Only primitive styles exist here
    │   │   └── size.tailwind.js # Sizing & measurement styles.
    │   └── tailwind.enki.config.js # This is the Tailwind config generated by the build process
    │   
    │   # This is the Tailwind setup if you want to use CSS variables.
    └── tailwind/with-vars 
        ├── css/ # This is required CSS for variable references to work
        │   ├── _lib/ # Source CSS files of CSS bundles
        │   │   ├── styles.{lang}.css # All variables for each language
        │   │   └── fonts.{lang}.root.css # Font variables
        │   ├── min/ # Minified CSS bundles
        │   │   ├── color.dark.css # Dark mode color minified CSS
        │   │   └── main.{{ lang }}.css # Final, bundled minified CSS file
        │   ├── min-hash/ # Minified & hashed CSS bundles
        │   │   ├── color.dark.{{ hash }}.css # Dark mode color minified & hashed CSS
        │   │   └── main.{{ lang }}.{{ hash }}.css # Final, bundled minified & hashed CSS file
        │   ├── color.dark.css # Dark mode color CSS
        │   └── main.{lang}.css # Final, bundled CSS file
        ├── lib/ # This is generated for our Tailwind plugins for font, button, & elevation styles
        │   ├── elevation-classes.tailwind.js # Elevation classes.
        │   ├── button-classes.tailwind.js # Button classes.
        │   └── font-classes.tailwind.js # Font & typography classes.
        ├── styles/ # These are font specific styles and classes/
        │   ├── color.tailwind.js # Color styles. This all references CSS variables
        │   ├── font.tailwind.js # Font styles. Only primitive styles exist here
        │   └── size.tailwind.js # Sizing & measurement styles.
        └── tailwind.enki.config.js # This is the Tailwind config generated by the build process
```

### Note:
- **Do not edit `./dist/{{library_name}}/tailwind.enki.config.js`.** We handpick variables that are imported into the Tailwind theme configuration here. Do not edit this file directly. Edit this file in `./src/tailwind/source/tailwind.enki.config.js`.
- Tailwind leverages CSS variables, so be sure to load your CSS styles from `./dist/{{library_name}}/tailwind/{{ build-type }}/css`.

## SCSS styles
Your `./dist/{{library_name}}/scss` directory should look something like the below. Please note the SCSS variables do not support typography and language styles, but do export `font-family` variables for each language.

```bash
.
└── .dist/{{library_name}}/
    └── scss/
        ├── _lib # Separated variable files
        ├── color.dark.scss # Optional: Dark mode color styles
        └── enki.scss # All variables
```

## Swift styles
Your `./dist/{{library_name}}/swift` directory should look something like the below.

```bash
.
└── .dist/{{library_name}}/
    └── swift/
        ├── color.swift # Color styles
        ├── color.dark.swift # Dark mode color styles
        ├── size.swift # Sizing & measurement variables
        └── typography-{lang}.swift # Typography styles
```

## Android styles
Your `./dist/{{library_name}}/android` directory should look something like the below.

```bash
.
└── .dist/{{library_name}}/
    └── android/
        ├── color.xml # Color styles
        ├── color.dark.xml # Dark mode color styles
        ├── dimens.xml # Sizing & measurement variables
        ├── font-base.xml # [Optional] Core typography properties
        ├── font-face-{lang}.xml # Language specific font faces
        └── font-prop-{lang}.xml # Language specific properties
```


## Importing Enki CSS to Next.js/Tailwind projects
[Moved to wiki.](https://github.com/sayweee/weee-ui/wiki/Adding-to-Next.js-Tailwind-Projects#instructions-for-integrating-within-nextjstailwind-projects)


## A note on token formatting
[Moved to wiki.](https://github.com/sayweee/weee-ui/wiki/A-note-on-token-formatting)