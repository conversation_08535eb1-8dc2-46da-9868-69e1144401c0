const fs = require('fs');
const path = require('path');

function removeFromCss(patterns, data) {
  const combinedPattern = patterns.map(pattern => `.*${pattern}.*`).join('|');
  const regex = new RegExp(combinedPattern, 'i');
  const lines = data.split('\n');
  const filteredLines = lines.filter(line => !regex.test(line));
  return filteredLines.join('\n');
}

function containsPatterns(patterns, data) {
  const combinedPattern = patterns.map(pattern => `.*${pattern}.*`).join('|');
  const regex = new RegExp(combinedPattern, 'i');
  return regex.test(data);
}

// Function to strip CSS files based on patterns
function stripCssFiles(files, patterns) {
  files.forEach(file => {
    const filePath = path.resolve(file);
    
    fs.readFile(filePath, 'utf8', (err, data) => {
      if (err) {
        console.error(`⛔️ Error reading file ${file}:`, err);
        return;
      }

      let updatedData = removeFromCss(patterns, data);

      if (containsPatterns(patterns, updatedData)) {
        updatedData = removeFromCss(patterns, updatedData);
      }

      fs.writeFile(filePath, updatedData, 'utf8', (err) => {
        if (err) {
          console.error(`⛔️ Error writing to file ${file}:`, err);
          return;
        }

        console.log(`✅ Successfully processed and updated file: ${file}`);
      });
    });
  });
}

module.exports = {
  stripCssFiles,
  removeFromCss
}