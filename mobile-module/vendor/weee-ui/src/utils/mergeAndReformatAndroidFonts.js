const fs = require("fs");
const xml2js = require("xml2js");
const util = require("util");

const parseXml = util.promisify(xml2js.parseString);

async function mergeAndReformatAndroidFontStyles(
  propFile,
  stylesFile,
  fontFamily,
  id
) {
  console.log("📱 Starting Android font styles reformatting...");

  // Read files
  console.log(`📂 Reading ${propFile} and ${stylesFile}...`);
  const propContent = fs.readFileSync(propFile, "utf8");
  const stylesContent = fs.readFileSync(stylesFile, "utf8");

  // Parse XML
  console.log("🔍 Parsing XML files...");
  const parser = new xml2js.Parser({ explicitArray: false });
  let propXml, stylesXml;

  try {
    propXml = await parseXml(propContent);
    stylesXml = await parseXml(stylesContent);
  } catch (error) {
    console.error("❌ Error parsing XML:", error);
    return;
  }

  // Create a map of style items from stylesFile
  console.log("🗺️ Creating map of style items...");
  const styleItems = {};
  if (stylesXml && stylesXml.resources && stylesXml.resources.item) {
    const items = Array.isArray(stylesXml.resources.item)
      ? stylesXml.resources.item
      : [stylesXml.resources.item];
    items.forEach((item) => {
      if (item && item.$) {
        styleItems[item.$.name] = item.$;
      }
    });
  } else {
    console.warn("⚠️ Unexpected structure in styles XML");
  }

  // Group styles
  console.log(`👥 Grouping styles with ID: ${id}...`);
  const groupedStyles = {};

  if (propXml && propXml.resources && propXml.resources.dimen) {
    const dimens = Array.isArray(propXml.resources.dimen)
      ? propXml.resources.dimen
      : [propXml.resources.dimen];
    dimens.forEach((dimen) => {
      if (dimen.$.name.includes(id)) {
        const nameParts = dimen.$.name.split("_");
        const property = nameParts.pop();
        const styleName = nameParts.join("_");

        if (!groupedStyles[styleName]) {
          groupedStyles[styleName] = {};
        }
        groupedStyles[styleName][property] = dimen._;
      }
    });
  } else {
    console.warn("⚠️ Unexpected structure in prop XML");
  }

  // Helper function to get the correct reference
  function getCorrectReference(value) {
    if (value.startsWith("@dimen/")) {
      const referenceName = value.replace("@dimen/", "");
      if (styleItems[referenceName]) {
        const type = styleItems[referenceName].type || "dimen";
        return `@${type}/${referenceName}`;
      }
    }
    return value;
  }

  // Create new styles
  console.log("🎨 Creating new styles...");
  const newStyles = [];
  for (const [styleName, properties] of Object.entries(groupedStyles)) {
    console.log(`  ✨ Processing style: ${styleName}`);
    const style = {
      $: { name: styleName },
      item: [],
    };

    if (properties.size) {
      style.item.push({
        _: getCorrectReference(properties.size),
        $: { name: "android:textSize" },
      });
    }

    if (properties.weight) {
      style.item.push({
        _: getCorrectReference(properties.weight),
        $: { name: "android:textFontWeight" },
      });
    }

    if (properties.tracking) {
      style.item.push({
        _: getCorrectReference(properties.tracking),
        $: { name: "android:letterSpacing" },
      });
    }

    if (properties.lineheight) {
      style.item.push({
        _: getCorrectReference(properties.lineheight),
        $: { name: "android:lineSpacingMultiplier" },
      });
    }

    if (fontFamily) {
      style.item.push({
        _: fontFamily,
        $: { name: "android:fontFamily" },
      });
    }

    newStyles.push(style);

    // Create _strong version if weightstrong exists
    if (properties.weightstrong) {
      console.log(`  💪 Creating strong version for: ${styleName}`);
      const strongStyle = JSON.parse(JSON.stringify(style));
      strongStyle.$.name += "_strong";
      const weightIndex = strongStyle.item.findIndex(
        (item) => item.$.name === "android:textFontWeight"
      );
      if (weightIndex !== -1) {
        strongStyle.item[weightIndex]._ = getCorrectReference(
          properties.weightstrong
        );
      }
      newStyles.push(strongStyle);
    }

    // Create _medium version if weightmedium exists
    if (properties.weightmedium) {
      console.log(`  🔶 Creating medium version for: ${styleName}`);
      const mediumStyle = JSON.parse(JSON.stringify(style));
      mediumStyle.$.name += "_medium";
      const weightIndex = mediumStyle.item.findIndex(
        (item) => item.$.name === "android:textFontWeight"
      );
      if (weightIndex !== -1) {
        mediumStyle.item[weightIndex]._ = getCorrectReference(
          properties.weightmedium
        );
      }
      newStyles.push(mediumStyle);
    }
  }

  // Create new XML structure
  console.log("🏗️ Building new XML structure...");
  const newXml = {
    resources: {
      style: newStyles,
    },
  };

  // Convert to XML string
  console.log("📝 Converting to XML string...");
  const builder = new xml2js.Builder({
    renderOpts: { pretty: true, indent: "  " },
    xmldec: { version: "1.0", encoding: "UTF-8" },
  });
  let xmlString = builder.buildObject(newXml);

  // Add comment after the XML declaration
  const currentDate = new Date().toUTCString();
  const comment = `
<!--
  Do not edit directly
  Generated on ${currentDate}
-->
`;
  const xmlDeclarationEndIndex = xmlString.indexOf("?>") + 2;
  xmlString =
    xmlString.slice(0, xmlDeclarationEndIndex) +
    comment +
    xmlString.slice(xmlDeclarationEndIndex);

  // Write to file
  console.log(`💾 Writing updated content to ${propFile}...`);
  fs.writeFileSync(propFile, xmlString);

  console.log("✅ Android font styles reformatting completed successfully!");
}

// Usage example
// mergeAndReformatAndroidFontStyles(
//   "dist/android/font_prop_tall.xml",
//   "dist/android/font_base_styles.xml",
//   "@font/be_vietnam_pro",
//   "tall"
// )
//   .then(() => console.log("🎉 Process completed!"))
//   .catch((error) => console.error("❌ An error occurred:", error));

module.exports = {
  mergeAndReformatAndroidFontStyles
}