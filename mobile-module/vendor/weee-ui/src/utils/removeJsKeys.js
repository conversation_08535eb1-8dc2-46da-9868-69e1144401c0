const fs = require("fs");
const path = require("path");

function removeJsKeys(files, keysToRemove) {
  files.forEach(file => {
    try {
      const filePath = path.resolve(file);
      const content = fs.readFileSync(filePath, 'utf8');
      const obj = eval(`(${content})`); // Evaluate the content to get the exported object

      // Function to recursively remove keys
      function removeKeys(obj, keysToRemove) {
        if (typeof obj !== 'object' || obj === null) {
          return obj; // Base case: return non-object values as-is
        }

        // Filter keys based on keysToRemove
        const filteredObj = {};
        for (const key in obj) {
          if (!keysToRemove.includes(key)) {
            // Recursively remove keys from nested objects
            filteredObj[key] = removeKeys(obj[key], keysToRemove);
          }
        }
        return filteredObj;
      }

      const modifiedObj = removeKeys(obj, keysToRemove);

      // Convert modified object back to a string representation
      const modifiedContent = `module.exports = ${JSON.stringify(modifiedObj, null, 2)};\n`;

      // Write the modified content back to the file
      fs.writeFileSync(filePath, modifiedContent, 'utf8');

      console.log(`🔥 Successfully removed keys from '${file}'`);
    } catch (err) {
      console.error(`⛔️ Error processing file '${file}':`, err);
    }
  });

  console.log('✅ All files processed successfully.');
}

module.exports = { removeJsKeys }