const fs = require("fs");
const path = require("path");

function readCssVariables(cssFilePath) {
  const cssContent = fs.readFileSync(cssFilePath, 'utf8');
  const variables = {};
  const regex = /--(.+?):\s*([^;]+);/g; // Regular expression to match CSS variables (--variable-name: value;)

  let match;
  while ((match = regex.exec(cssContent)) !== null) {
      const variableName = match[1].trim();
      const variableValue = match[2].trim();
      variables[variableName] = variableValue;
  }

  return variables;
}

function replaceCssVariablesInFiles(filePaths, cssVariablesFile) {
  // Read CSS variables from the provided CSS file
  const cssVariables = readCssVariables(cssVariablesFile);

  // Iterate over each file path in the array
  filePaths.forEach(filePath => {
      // Read file content
      let fileContent = fs.readFileSync(filePath, 'utf8');

      // Replace all occurrences of var(--variable-name) with corresponding value
      Object.entries(cssVariables).forEach(([variableName, variableValue]) => {
          const regex = new RegExp(`var\\(--${variableName}\\)`, 'g');
          fileContent = fileContent.replace(regex, variableValue);
      });

      // Write updated content back to the file
      fs.writeFileSync(filePath, fileContent, 'utf8');
      console.log(`🪢 Variables replaced in '${filePath}'`);
  });

  console.log('✅ Replacement process completed.');
}

module.exports = {
  replaceCssVariablesInFiles,
};
