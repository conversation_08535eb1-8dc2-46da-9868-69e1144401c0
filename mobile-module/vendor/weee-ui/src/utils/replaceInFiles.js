const fs = require("fs");
const path = require("path");

function replaceInFiles(filePaths, replacements) {
  filePaths.forEach((filePath) => {
    // Read the file content
    let fileContent = fs.readFileSync(filePath, "utf8");

    // Perform the replacements in order
    replacements.forEach(([from, to]) => {
      // Replace all instances of the strings
      fileContent = fileContent.split(from).join(to);
    });

    // Save the modified content back to the file
    fs.writeFileSync(filePath, fileContent, "utf8");
  });
}

module.exports = {
  replaceInFiles,
};
