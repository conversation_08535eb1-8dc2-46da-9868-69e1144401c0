/* Convert CSS to JS object notation */
/* By <PERSON> 2024 */

const cssToJs = (css) => {
  // Remove :root { ... } and :root [data-...] { ... } declarations
  css = css.replace(/:root\s*(\[[^\]]+\])?\s*{[^}]*}\s*/g, '');

  const lines = css.split('\n');
  const jsObject = {};
  let currentSelector = null;
  let mediaQuery = null;

  const processLine = (line, targetObject) => {
      const [property, value] = line.split(':').map(part => part.trim().replace(';', ''));
      const camelCasedProperty = property.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase());
      targetObject[camelCasedProperty] = value;
  };

  lines.forEach(line => {
      // Remove leading and trailing whitespace
      line = line.trim();

      // Check if the line is a CSS selector
      if (line.endsWith('{')) {
          const selector = line.slice(0, -1).trim();
          if (selector.startsWith('@media')) {
              mediaQuery = selector;
          } else {
              currentSelector = selector;
              if (!jsObject[currentSelector]) {
                  jsObject[currentSelector] = {};
              }
          }
      } 
      // Check if the line is the closing brace of a selector block
      else if (line === '}') {
          mediaQuery = null;
          currentSelector = null;
      } 
      // Check if the line is a CSS property
      else if (currentSelector) {
          if (currentSelector == "body, html" || currentSelector.includes("behavior")) {
            return;
          }
          if (mediaQuery && currentSelector.includes(':hover')) {
              const baseSelector = currentSelector.replace(':hover', '').trim();
              if (!jsObject[baseSelector]['@media(hover:hover)']) {
                  jsObject[baseSelector]['@media(hover:hover)'] = { '&:hover': {} };
              }
              processLine(line, jsObject[baseSelector]['@media(hover:hover)']['&:hover']);
          } else if (currentSelector.includes(':active')) {
              const baseSelector = currentSelector.replace(':active', '').trim();
              if (!jsObject[baseSelector]['&:active']) {
                  jsObject[baseSelector]['&:active'] = {};
              }
              processLine(line, jsObject[baseSelector]['&:active']);
          } else {
              processLine(line, jsObject[currentSelector]);
          }
      }
  });

  // Remove empty objects
  Object.keys(jsObject).forEach(key => {
      if (Object.keys(jsObject[key]).length === 0) {
          delete jsObject[key];
      }
  });

  return jsObject;
};

module.exports = {
  cssToJs
}