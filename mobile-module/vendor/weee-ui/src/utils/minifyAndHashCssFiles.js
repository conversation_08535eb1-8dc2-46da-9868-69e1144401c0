const Constants = require("../config/constants");
const fs = require("fs");
const path = require("path");
const cleanCSS = require("clean-css");
const crypto = require("crypto");

function cleanOutputDir(dir) {
  const dirPath = dir;

  // Check if the directory exists
  if (fs.existsSync(dirPath)) {
    console.log(
      `⚠️ Directory '${dirPath}' exists. It will be removed before creating a new one.`
    );

    // Remove the directory and its contents
    fs.rmSync(dirPath, { recursive: true });
    console.log(`🔥 Directory '${dirPath}' removed.`);
  }

  // Create the new directory
  fs.mkdirSync(dirPath);
  console.log(`🐣 Empty directory '${dirPath}' created.`);
}

function minifyAndHashCssFiles(directories, minify = true, hash = true) {
  if (!directories) return;

  // _____________________
  // Read directories
  directories.forEach((outputDir) => {
    fs.readdir(outputDir, function (err, files) {
      if (err) {
        onError(err);
        return;
      }

      let minifiedDir = `${outputDir}${Constants.V2_MINIFIED_DIR}`;
      const hashedDir = `${outputDir}${Constants.V2_HASHED_DIR}`;

      // _____________________
      // Clean directories

      if (minify) {
        cleanOutputDir(minifiedDir);
      }

      if (hash) {
        cleanOutputDir(hashedDir);
      }

      // _____________________
      // Get directory files
      files.forEach((file) => {
        if (path.extname(file) == ".css") {
          // _____________________
          // Read files
          fs.readFile(`${outputDir}${file}`, "utf8", function (err, data) {
            if (err) return err;
            if (!data) return;

            const fileName = file.replace(".css", "");
            const fileData = `${data}`;

            // _____________________
            // Minify files
            if ((minify = true)) {
              const minifiedCss = new cleanCSS().minify(fileData).styles;

              fs.writeFile(
                `${minifiedDir}${fileName}.min.css`,
                minifiedCss,
                (err) => {
                  if (err) {
                    console.error(`⛔️ Error writing to file '${path}'`, err);
                  } else {
                    console.log(
                      `💽 Minified CSS fonts file '${minifiedDir}${file}' has been written`
                    );
                  }
                }
              );
            }

            // _____________________
            // Hash files
            if (hash) {
              const hash = crypto.createHash("sha256");
              const minifiedCss = new cleanCSS().minify(fileData).styles;

              hash.update(minifiedCss);
              const hashedDigest = hash.digest("hex").slice(0, 8);

              fs.writeFile(
                `${hashedDir}${fileName}.${hashedDigest}.min.css`,
                minifiedCss,
                (err) => {
                  if (err) {
                    console.error(`⛔️ Error writing to file '${path}'`, err);
                  } else {
                    console.log(
                      `🔐 Minified & hashed CSS fonts file '${hashedDir}${file}' has been written`
                    );
                  }
                }
              );
            }
          });
        }
      });
    });
  });
}

module.exports = {
  minifyAndHashCssFiles
}
