/* Append or prepend CSS files to one file */
/* By <PERSON> 2024 */

const fs = require('fs');
const path = require('path');

function appendOrPrependCssFiles(sourceFile, filesToMerge, mergedOutputFile, prependMerge = true, preserveHeader = true) {
    // Read the source file
    const sourceContent = fs.readFileSync(sourceFile, 'utf8');
    let header = '';
    let content = sourceContent;

    // If preserving header, extract it
    if (preserveHeader) {
        const headerMatch = sourceContent.match(/\/\*[\s\S]*?\*\//);
        if (headerMatch) {
            header = headerMatch[0];
            content = sourceContent.replace(header, '').trim();
        }
    }

    // Read and merge the files to merge
    let mergeContent = filesToMerge.map(file => fs.readFileSync(file, 'utf8')).join('\n');

    // Decide where to place the merge content
    let finalContent;
    if (prependMerge) {
        finalContent = `${header}\n${mergeContent}\n${content}`;
    } else {
        finalContent = `${header}\n${content}\n${mergeContent}`;
    }

    // Ensure the output directory exists
    const outputDir = path.dirname(mergedOutputFile);
    if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
    }

    // Write the final content to the output file (overwrite if it exists)
    fs.writeFileSync(mergedOutputFile, finalContent.trim(), 'utf8');
}

module.exports = {
  appendOrPrependCssFiles
}