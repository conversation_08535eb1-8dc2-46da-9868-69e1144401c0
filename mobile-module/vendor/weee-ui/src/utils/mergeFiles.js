/* Merge multiple files to one */
/* By <PERSON> 2024 */

const fs = require("fs");
const path = require("path");
const { promisify } = require('util');

const renameAsync = promisify(fs.rename);
const mkdirAsync = promisify(fs.mkdir);
const existsAsync = promisify(fs.exists);

async function mergeFiles(filesToBeMerged, outputDirectory, outputFileName, archiveDirectory) {
  // Ensure output directory exists, create if not
  if (!await existsAsync(outputDirectory)) {
    await mkdirAsync(outputDirectory, { recursive: true });
  }

  // Ensure archive directory exists, create if not
  if (!await existsAsync(archiveDirectory)) {
    await mkdirAsync(archiveDirectory, { recursive: true });
  }

  // Move files to the archive directory
  const archivedFiles = [];
  for (const filePath of filesToBeMerged) {
    const archivePath = path.join(archiveDirectory, path.basename(filePath));
    try {
      await renameAsync(filePath, archivePath);
      archivedFiles.push(archivePath);
    } catch (err) {
      console.error(`⛔️ Error moving file ${filePath} to ${archivePath}: ${err.message}`);
    }
  }

  // Merge the archived files into the output file
  const outputPath = path.join(outputDirectory, outputFileName);
  const writeStream = fs.createWriteStream(outputPath);

  for (const [index, filePath] of archivedFiles.entries()) {
    await new Promise((resolve, reject) => {
      const readStream = fs.createReadStream(filePath);
      
      readStream.pipe(writeStream, { end: false });

      readStream.on('end', () => {
        // Add a newline or any separator between files if desired
        if (index < archivedFiles.length - 1) {
          writeStream.write('\n'); // You can change the separator as needed
        }
        resolve();
      });

      readStream.on('error', (err) => {
        console.error(`⛔️ Error reading file ${filePath}: ${err.message}`);
        reject(err);
      });
    });
  }

  writeStream.end();

  writeStream.on('finish', () => {
    console.log(`✅ All files have been merged into ${outputPath}`);
  });

  writeStream.on('error', (err) => {
    console.error(`⛔️ Error writing to file ${outputPath}: ${err.message}`);
  });
}

module.exports = {
  mergeFiles,
};
