const fs = require('fs');
const path = require('path');

function formatAndroidDimens(arrayOfXmlFiles) {
  console.log('🚀 Starting Android dimens syntax update...');

  arrayOfXmlFiles.forEach(filePath => {
    const fullPath = path.resolve(filePath);
    console.log(`📂 Opening file: ${fullPath}`);
    let content = fs.readFileSync(fullPath, 'utf8');

    console.log('🔍 Searching for <dimen> tags...');
    const dimenRegex = /<dimen name="([^"]+)">([^<]+)<\/dimen>/g;

    let updateCount = 0;
    content = content.replace(dimenRegex, (match, name, value) => {
      updateCount++;
      let type, format;

      if (value.endsWith('sp') || value.endsWith('dp')) {
        type = 'dimen';
        format = 'dimension';
        console.log(`📏 Converting sp unit: ${name}`);
      } else if (value.match(/^-?\d+(\.\d+)?$/)) {
        type = 'integer';
        if (value.includes('.')) {
          format = 'float';
          console.log(`🔢 Converting float value: ${name}`);
        } else {
          format = 'integer';
          console.log(`🔢 Converting integer value: ${name}`);
        }
      } else {
        type = 'integer';
        format = 'integer';
        const weightMap = {
          'Regular': '400',
          'Medium': '500',
          'Semibold': '600',
          'Bold': '700',
          'Extra Bold': '800'
        };
        if (weightMap[value]) {
          console.log(`🔤 Converting font weight: ${name} (${value} to ${weightMap[value]})`);
          value = weightMap[value];
        } else {
          console.log(`⚠️ Unknown value type: ${name} (${value})`);
        }
      }

      return `<item name="${name}" type="${type}" format="${format}">${value}</item>`;
    });

    fs.writeFileSync(fullPath, content, 'utf8');
    console.log(`✅ Processed: ${fullPath} (${updateCount} dimens updated)`);
  });

  console.log('🎉 Android dimens syntax update completed!');
}

module.exports = { formatAndroidDimens }