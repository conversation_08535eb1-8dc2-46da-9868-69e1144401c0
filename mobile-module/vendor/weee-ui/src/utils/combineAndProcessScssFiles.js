const fs = require("fs");
const path = require("path");

function combineAndProcessScssFiles(inputFiles, outputFile) {
  let combinedContent = '';

  // Combine all SCSS files into one string
  inputFiles.forEach(file => {
      const fileContent = fs.readFileSync(file, 'utf-8');
      combinedContent += fileContent + '\n';
  });

  // Split the combined content into lines
  let lines = combinedContent.split('\n');

  // Remove duplicate lines and filter out comments and empty lines
  lines = [...new Set(lines)]
      .filter(line => line.trim() !== '' && !line.trim().startsWith('//'));

  // Separate font-family lines and other lines
  const fontFamilyLines = [];
  const otherLines = [];
  
  lines.forEach(line => {
      if (line.startsWith('$font-family-')) {
          fontFamilyLines.push(line);
      } else {
          otherLines.push(line);
      }
  });

  // Construct the final content
  const timestampComment = `// Do not edit directly\n// Generated on ${new Date()}\n\n`;
  const finalContent = timestampComment + fontFamilyLines.join('\n') + '\n' + otherLines.join('\n');

  // Write the final content to the output file
  fs.writeFileSync(outputFile, finalContent, 'utf-8');

  // Remove input files
  inputFiles.forEach(file => {
      fs.unlinkSync(file);
  });
}


module.exports = {
  combineAndProcessScssFiles,
};
