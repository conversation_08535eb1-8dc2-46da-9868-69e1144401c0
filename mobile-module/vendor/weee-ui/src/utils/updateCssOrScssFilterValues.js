const fs = require('fs');
const path = require('path');

function updateCssOrScssFilterValues(styleFilePath, jsonFilePath, variablePattern) {
  try {
    // Read the style (CSS or SCSS) and JSON files
    const styleContent = fs.readFileSync(styleFilePath, 'utf8');
    const jsonContent = fs.readFileSync(jsonFilePath, 'utf8');

    const jsonData = JSON.parse(jsonContent);
    const fileExtension = path.extname(styleFilePath).toLowerCase();
    const isScss = fileExtension === '.scss';

    // Adjust the regex based on whether it's a CSS or SCSS file
    const variablePrefix = isScss ? '\\$' : '--';
    const variableRegex = new RegExp(`${variablePrefix}${variablePattern.replace('*', '[\\w-]+')}: .*?;`, 'g');

    // Find all matching variables
    const matches = [...styleContent.matchAll(variableRegex)];

    let updatedStyleContent = styleContent;

    for (const match of matches) {
      const variableName = match[0].split(':')[0].slice(isScss ? 1 : 2); // Remove '$' or '--' prefix
      const jsonKey = variableName.replace(/-/g, '.').replace('.behavior', '');
      
      const behaviorValue = jsonData.color?.btn?.[jsonKey.split('.')[2]]?.behavior?.value;

      if (behaviorValue && behaviorValue.startsWith('{') && behaviorValue.endsWith('}')) {
        const referenceKey = behaviorValue.slice(1, -1);
        const referenceObject = getNestedValue(jsonData, referenceKey);

        if (referenceObject) {
          const newVariables = Object.keys(referenceObject)
            .map(key => {
              const newVarName = `${variableName}-${key}`;
              const referencedVarName = `${referenceKey.replace(/\./g, '-')}-${key}`;
              return isScss 
                ? `$${newVarName}: $${referencedVarName};`
                : `--${newVarName}: var(--${referencedVarName});`;
            })
            .join('\n');

          updatedStyleContent = updatedStyleContent.replace(match[0], newVariables);
          console.log(`Updated ${variableName} with new values`);
        }
      }
    }

    // Write the updated style content back to the file
    fs.writeFileSync(styleFilePath, updatedStyleContent, 'utf8');
    console.log('Style file updated successfully');

  } catch (error) {
    console.error('Error updating style file:', error);
  }
}

function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => current && current[key], obj);
}

module.exports = {
  updateCssOrScssFilterValues
}