const Constants = require("../config/constants");

/* Convert design token typography JSON to CSS notation while maintaining variable references */
/* By <PERSON> 2024 */

function typographyJsonToCss(json, rootVariablesOnly = false) {
  const cssClasses = {};
  const rootVars = {};

  function addRootVar(family, subKey, sizeKey, prop, value) {
    const classVarName = `--${subKey}-${sizeKey}-${prop}`;
    if (!rootVars[family]) {
      rootVars[family] = [];
    }
    if (value.startsWith("{") && value.endsWith("}")) {
      const varName = value.slice(1, -1).replace(/\./g, "-");
      rootVars[family].push(`${classVarName}: var(--${varName})`);
    } else {
      rootVars[family].push(`${classVarName}: ${value}`);
    }
    return classVarName;
  }

  function parseJson(family, obj) {
    Object.keys(obj).forEach((subKey) => {
      const subObj = obj[subKey];
      Object.keys(subObj).forEach((sizeKey) => {
        const sizeObj = subObj[sizeKey];
        const className = `.enki-${subKey}-${sizeKey}`;
        const strongClassName = `.enki-${subKey}-${sizeKey}-strong`;
        const mediumClassName = `.enki-${subKey}-${sizeKey}-medium`;

        const classDeclarations = [];
        let strongDeclarations = [];
        let mediumDeclarations = [];

        const props = {
          tracking: "letter-spacing",
          size: "font-size",
          family: "font-family",
          weight: "font-weight",
          lineheight: "line-height",
        };

        const requiredProps = [
          "tracking",
          "size",
          "family",
          "weight",
          "lineheight",
        ];

        requiredProps.forEach((propKey) => {
          if (sizeObj[propKey]) {
            const propObj = sizeObj[propKey];
            const classVarName = addRootVar(
              family,
              subKey,
              sizeKey,
              propKey,
              propObj.value
            );
            if (propKey === "weightstrong") {
              strongDeclarations.push(`font-weight: var(${classVarName})`);
            } else if (propKey === "weightmedium") {
              mediumDeclarations.push(`font-weight: var(${classVarName})`);
            } else {
              classDeclarations.push(`${props[propKey]}: var(${classVarName})`);
              strongDeclarations.push(
                `${props[propKey]}: var(${classVarName})`
              );
              mediumDeclarations.push(
                `${props[propKey]}: var(${classVarName})`
              );
            }
          }
        });

        if (classDeclarations.length > 0) {
          cssClasses[className] = classDeclarations;
        }

        if (sizeObj.weightstrong) {
          const strongVarName = addRootVar(
            family,
            subKey,
            sizeKey,
            "weightstrong",
            sizeObj.weightstrong.value
          );
          strongDeclarations = strongDeclarations.filter(
            (decl) => !decl.startsWith("font-weight:")
          );
          strongDeclarations.push(`font-weight: var(${strongVarName})`);
          cssClasses[strongClassName] = strongDeclarations;
        }

        if (sizeObj.weightmedium) {
          const mediumVarName = addRootVar(
            family,
            subKey,
            sizeKey,
            "weightmedium",
            sizeObj.weightmedium.value
          );
          mediumDeclarations = mediumDeclarations.filter(
            (decl) => !decl.startsWith("font-weight:")
          );
          mediumDeclarations.push(`font-weight: var(${mediumVarName})`);
          cssClasses[mediumClassName] = mediumDeclarations;
        }
      });
    });
  }

  Object.keys(json).forEach((family) => {
    parseJson(family, json[family]);
  });

  const rootCss = Object.keys(rootVars)
    .map((family) => {
      const vars = rootVars[family]
        .map((varDeclaration) => `   ${varDeclaration};`)
        .join("\n");

      let bodyClassDeclaration = `body, html { font-family: var(--font-family-${family}-main); -webkit-text-size-adjust: 100%; -moz-osx-font-smoothing: grayscale; }\n`

      return `:root {\n${vars}\n}\n${bodyClassDeclaration}`;
    })
    .join("\n\n");

  let classCss = Object.keys(cssClasses)
    .map((className) => {
      const declarations = cssClasses[className].join(";\n   ");
      return `${className} {\n   ${declarations};\n}`;
    })
    .join("\n\n");

  const dateTime = new Date();
  const fileHeader = `
/**
 * Do not edit directly
 * Generated on ${dateTime}
 * IMPORTANT: Make sure you import 'styles.{lang}.css' before loading this file if you are loading individually
 * FOR EXAMPLE: If this is 'fonts.cjk.css', import 'styles.cjk.css' before loading this file
 */
  `;

  

  if (rootVariablesOnly) {
    classCss = "";
  }

  return `${fileHeader}\n\n${rootCss}\n\n${classCss}`;
}

module.exports = {
  typographyJsonToCss,
};
