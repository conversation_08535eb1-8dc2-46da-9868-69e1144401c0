const includeTokenName = (token, str = false) => {
  if (!token || !str || !token.name) return;

  // console.log(token);

  return token.name.includes(str);
};

const includeTokenPathMulti = (token, str = []) => {
  if (!token || !str || !token.name) return;

  let hasMatch = false;

  str.forEach((matcher) => {
    if (token.path.includes(matcher)) {
      hasMatch = true;
    }
  })

  return hasMatch;
};

const includeTokenPathAllValues = (token, values = []) => {
  if (!token || !values || !token.path) return;

  return values.every(value => token.path.includes(value));
};

const excludeTokenName = (token, str = false) => {
  if (!token || !str || !token.name) return;

  return !token.name.includes(str);
};

const includeTokenPath = (token, str = "") => {
  if (!token || !str || !token.name) return;

  return token.path.includes(str);
};

const excludeTokenPath = (token, str = "") => {
  if (!token || !str || !token.name) return;

  return !token.path.includes(str);
};

const excludeTokenPathAllValues = (token, values = []) => {
  if (!token || !values || !token.path) return;

  return !values.every(value => token.path.includes(value));
};

module.exports = {
  includeTokenName,
  excludeTokenName,
  includeTokenPath,
  includeTokenPathMulti,
  includeTokenPathAllValues,
  excludeTokenPath,
  excludeTokenPathAllValues
};
