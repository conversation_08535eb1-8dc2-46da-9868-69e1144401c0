/* Convert JSON design token object notation for buttons to CSS classes */
/* By <PERSON> 2024 */

function buttonJsonToCss(json) {
  if (!json && !json.color && !json.color.btn) {
    console.log(
      "⛔️⛔️⛔️⛔️⛔️⛔️⛔️⛔️ buttonJsonToCss(): But<PERSON> json not found, bailing"
    );

    return;
  }

  console.log("✅ buttonJsonToCss(): Button json found, let's go");

  const prefix = "enki-button-";
  const btns = json.color.btn;
  let cssClasses = "";

  for (const btn in btns) {
    if (btns.hasOwnProperty(btn)) {
      const bg = btns[btn].bg.value;
      const fg = btns[btn].fg.value;
      const behavior = btns[btn].behavior?.value;

      const className = `${prefix}${btn}`;
      cssClasses += `.${className} {\n`;
      cssClasses += `  appearance: none;\n`;
      cssClasses += `  outline: none;\n`;

      if (className.includes("disabled")) {
        cssClasses += `  cursor: not-allowed;\n\n`;
      } else {
        cssClasses += `  cursor: pointer;\n\n`;
      }
      cssClasses += `  background-color: var(--color-btn-${btn}-bg);\n`;
      cssClasses += `  color: var(--color-btn-${btn}-fg-default);\n`;
      cssClasses += `}\n\n`;

      if (behavior) {
        const behaviorVar = behavior.replace(/[{}]/g, "").replace(/\./g, "-");
        cssClasses += `@media (hover: hover) {\n`;
        cssClasses += `  .${className}:hover {\n`;
        cssClasses += `    filter: var(--${behaviorVar}-hover);\n`;
        cssClasses += `  }\n`;
        cssClasses += `}\n\n`;
        cssClasses += `.${className}:active {\n`;
        cssClasses += `  filter: var(--${behaviorVar}-pressed);\n`;
        cssClasses += `}\n\n`;
      }
    }
  }

  return cssClasses;
}

module.exports = {
  buttonJsonToCss,
};
