const Constants = require("../config/constants");
const fs = require("fs");
const cleanCSS = require("clean-css");
const crypto = require("crypto");

function outputCssToFile(
  data,
  filename,
  outputDir = Constants.V2_CSS_OUTPUT_DIR
) {
  const path = `${outputDir}${filename}.css`;

  console.log(`📥 Outputting CSS to '${path}'`);

  // _____________________
  // Check if the file exists
  if (fs.existsSync(path)) {
    console.log(`💥 File '${path}' exists. Replacing file.`);
  } else {
    console.log(`🐣 File '${path}' does not exist. Creating new file.`);
  }

  // _____________________
  // Write pretty file
  fs.writeFile(path, data, (err) => {
    if (err) {
      console.error(`⛔️ Error writing to file '${path}'`, err);
    } else {
      console.log(`💅 Pretty CSS file '${path}' has been written`);
    }
  });
}

module.exports = {
  outputCssToFile,
};
