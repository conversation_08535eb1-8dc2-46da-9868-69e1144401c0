/* Convert elevation JSON tokens into CSS classes*/
/* By <PERSON> 2024 */

const elevationJsonToCss = (json) => {
  if (!json) return;

  let cssClasses = "";

  const styleKeys = json.style;

  if (styleKeys) {
    for (const category in styleKeys) {
      const items = styleKeys[category];
      for (const key in items) {
        cssClasses += `.enki-${category}-${key} {\n`;
        cssClasses += `  box-shadow: var(--style-${category}-${key});\n`;
        cssClasses += `}\n\n`;
      }
    }

    return cssClasses.trim();
  } else {
    return "";
  }
};

module.exports = {
  elevationJsonToCss,
};
