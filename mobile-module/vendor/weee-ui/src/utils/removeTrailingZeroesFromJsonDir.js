const fs = require('fs');
const path = require('path');

function removeTrailingZeroesFromJsonDir(directoryPath) {
  console.log(`🚀 Starting to remove trailing zeroes from JSON files in '${directoryPath}'`);

  const files = fs.readdirSync(directoryPath);
  let totalFilesUpdated = 0;
  let totalValuesChanged = 0;

  files.forEach(file => {
    if (path.extname(file) === '.json') {
      const filePath = path.join(directoryPath, file);
      console.log(`📄 Processing file: ${file}`);

      let fileContent = fs.readFileSync(filePath, 'utf8');
      let jsonData = JSON.parse(fileContent);

      const processValue = (obj) => {
        let changedValues = 0;

        for (let key in obj) {
          if (typeof obj[key] === 'object' && obj[key] !== null) {
            changedValues += processValue(obj[key]);
          } else if (key === 'value' && typeof obj[key] === 'string') {
            const originalValue = obj[key];
            // Handle rgba values
            if (originalValue.startsWith('rgba(')) {
              obj[key] = originalValue.replace(/(\d+(?:\.\d*)?|\.\d+)(?=[,)])/g, match => {
                return parseFloat(match).toString();
              });
            } else {
              // Handle numeric strings
              const numValue = parseFloat(originalValue);
              if (!isNaN(numValue) && numValue.toString() === originalValue.replace(/\.0+$/, '')) {
                obj[key] = numValue.toString();
              }
            }
            if (obj[key] !== originalValue) {
              changedValues++;
            }
          }
        }

        return changedValues;
      };

      const changedValues = processValue(jsonData);

      if (changedValues > 0) {
        fs.writeFileSync(filePath, JSON.stringify(jsonData, null, 2));
        console.log(`✅ Updated ${file} - Removed trailing zeroes from ${changedValues} value(s)`);
        totalFilesUpdated++;
        totalValuesChanged += changedValues;
      } else {
        console.log(`ℹ️ No trailing zeroes found in ${file}`);
      }
    }
  });

  console.log(`\n🎉 Finished removing trailing zeroes from JSON files in '${directoryPath}'`);
  console.log(`📊 Total files updated: ${totalFilesUpdated}`);
  console.log(`📊 Total values changed: ${totalValuesChanged}`);
}

module.exports = { removeTrailingZeroesFromJsonDir }