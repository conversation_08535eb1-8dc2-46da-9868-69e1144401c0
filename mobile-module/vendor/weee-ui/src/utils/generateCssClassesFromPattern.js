const fs = require("fs");
const path = require("path");

// Function to process CSS file and generate classes
function generateCssClassesFromPattern(
  inputFile,
  variablePattern,
  cssProperty,
  classPrefix,
  removePattern,
  callback
) {
  // Read the CSS file
  fs.readFile(inputFile, 'utf8', (err, data) => {
    if (err) {
      console.error('Error reading the file:', err);
      callback(err, null);
      return;
    }

    // Extract the :root { ... } block content
    const rootMatch = data.match(/:root\s*{([^}]*)}/);
    if (!rootMatch) {
      const errorMsg = 'No :root block found in the CSS file.';
      console.error(errorMsg);
      callback(new Error(errorMsg), null);
      return;
    }

    // Extract CSS variables from the :root block
    const rootContent = rootMatch[1];
    const variableLines = rootContent
      .split(';')
      .map(line => line.trim())
      .filter(line => line.startsWith('--'));

    // Process each variable
    const classes = variableLines
      .map(line => {
        const [variable, value] = line.split(':').map(part => part.trim());
        if (variable.includes(variablePattern)) {
          const className = variable
            .replace('--', '')
            .replace(removePattern, '')
            .replace(/-/g, '-');
          return `.${classPrefix}-${className} {\n${cssProperty}: ${value};\n}`;
        }
        return null;
      })
      .filter(Boolean);

    // Join classes into a single string
    const result = classes.join('\n');
    callback(null, result);
  });
}

// Example usage with a callback
// generateCssClassesFromPattern(
//   './dist/css/_lib/color.css',
//   'style-filter',
//   'filter',
//   'enki',
//   'style-',
//   (err, result) => {
//     if (err) {
//       console.error('Error:', err);
//     } else {
//       console.log(result);
//     }
//   }
// );

module.exports = { generateCssClassesFromPattern };
