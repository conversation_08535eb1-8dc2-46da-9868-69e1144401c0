const fs = require("fs");
const path = require("path");
const Constants = require("../config/constants");

// Files: Array of filepaths to tailwind js files (colors.tailwind.js)
// Tokens: Filepath to a tokens json file (colors.json)
// asVariables: Boolean to populate as css variables or evaulate color values
function populateTailwindConfigFromJson(files, tokens, asVariables = false, variablePrefix = '') {
  if (!Array.isArray(files) || files.length === 0) {
    console.log('[populateTailwindConfigFromJson] No files provided.');
    return;
  }

  // Read and parse the tokens JSON file
  let tokensJson;
  try {
    if (!fs.existsSync(tokens)) {
      console.log(`[populateTailwindConfigFromJson] Tokens file not found: ${tokens}`);
      return;
    }
    const tokensContent = fs.readFileSync(tokens, 'utf8');
    tokensJson = JSON.parse(tokensContent);
  } catch (err) {
    console.log(`[populateTailwindConfigFromJson] Error reading tokens file: ${err.message}`);
    return;
  }

  // Helper: Get value at a path array (e.g., ['color','root','energy','blue','dark','4'])
  function getValueByPath(obj, pathArr) {
    return pathArr.reduce((acc, key) => (acc && acc[key] !== undefined ? acc[key] : undefined), obj);
  }

  // Helper: Recursively resolve references, with circular reference protection
  function resolveValue(obj, pathArr, visitedPaths = new Set()) {
    const valueObj = getValueByPath(obj, pathArr);
    if (!valueObj || typeof valueObj !== 'object' || !('value' in valueObj)) return undefined;
    let val = valueObj.value;
    if (typeof val === 'string' && val.startsWith('{') && val.endsWith('}')) {
      // Reference: {color.root.energy.blue.dark.4}
      const refPathStr = val.slice(1, -1);
      const refPathArr = refPathStr.split('.');
      const refKey = refPathArr.join('.');
      if (visitedPaths.has(refKey)) {
        console.log(`[populateTailwindConfigFromJson] Circular reference detected: ${refKey}`);
        return undefined;
      }
      visitedPaths.add(refKey);
      return resolveValue(obj, refPathArr, visitedPaths);
    }
    return val;
  }

  // Helper: Recursively build config object from tokens
  function buildConfig(obj, pathArr = []) {
    if (typeof obj !== 'object' || obj === null) return obj;
    if (Object.prototype.hasOwnProperty.call(obj, 'value')) {
      let val = obj.value;
      if (asVariables) {
        // Always use the current key path for the CSS variable, regardless of reference
        const varName = '--' + (variablePrefix ? variablePrefix + '-' : '') + pathArr.join('-');
        return `var(${varName})`;
      } else {
        if (typeof val === 'string' && val.startsWith('{') && val.endsWith('}')) {
          // Reference: resolve to actual value
          const refPathStr = val.slice(1, -1);
          const refPathArr = refPathStr.split('.');
          const resolved = resolveValue(tokensJson, refPathArr);
          return resolved !== undefined ? resolved : val;
        } else {
          return val;
        }
      }
    }
    const result = {};
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        result[key] = buildConfig(obj[key], pathArr.concat(key));
      }
    }
    return result;
  }

  // Find the root key (e.g., 'color') and use its children for config
  const rootKey = Object.keys(tokensJson)[0];
  const configObj = buildConfig(tokensJson[rootKey]);

  // Write config to each file (support [input, output] or [input])
  files.forEach((fileArr) => {
    let inputPath, outputPath;
    if (Array.isArray(fileArr)) {
      inputPath = fileArr[0];
      outputPath = fileArr[1] || fileArr[0]; // If no output, overwrite input
    } else {
      // fallback for legacy usage
      inputPath = fileArr;
      outputPath = fileArr;
    }
    try {
      const resolvedPath = path.resolve(outputPath);
      // Ensure directory exists
      const dir = path.dirname(resolvedPath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      // Write the config as a CommonJS module
      const fileContent =
        'module.exports = ' + JSON.stringify(configObj, null, 2) + ';\n';
      fs.writeFileSync(resolvedPath, fileContent, 'utf8');
      console.log(`[populateTailwindConfigFromJson] Wrote config to ${resolvedPath}`);
    } catch (err) {
      console.log(`[populateTailwindConfigFromJson] Error writing file ${outputPath}: ${err.message}`);
    }
  });
}

// ______________________
// Examples

function test() {
  // Overwrite file in place, evaluated values
  populateTailwindConfigFromJson(
    [
      [
        `${Constants.V2_TAILWIND_EMBEDDED_STYLES_OUTPUT_DIR}color.tailwind.js`,
        `test/evaluated/color.tailwind.js`
      ]
    ],
    `${Constants.V2_TOKEN_FORMATTED_OUTPUT_DIR}colors.json`,
    false
  );

  // Write to a new output file, css variables
  populateTailwindConfigFromJson(
    [
      [
        `${Constants.V2_TAILWIND_EMBEDDED_STYLES_OUTPUT_DIR}color.tailwind.js`,
        `test/css-variables/color.tailwind.js`
      ]
    ],
    `${Constants.V2_TOKEN_FORMATTED_OUTPUT_DIR}colors.json`,
    true,
    'color'
  );
}

test();

module.exports = {
  populateTailwindConfigFromJson,
};

// _____________________________
// Prompt
// 1. Loop through files, return a console.log if no files are found or they don't exist
  // 2a. For each file, loop through the all the keys in the tokens json file recursively until you reach a `value` key
  //     and overwrite the js file in the tailwind js file in `module.exports = {}`
  //     Example supplied tokens json file (colors.json): 
  //   {
  //    "color": {
  //      "root": {
  //        "mandarin": {
  //          "orange": {
  //            "base": { 
  //              "1": { "value": "rgba(229,61,20,1)" },
  //              "2": { "value": "rgba(250,112,80,1)" },
  //              "3": { "value": "rgba(89,32,7,1)" }
  //            }
  //          }
  //        }
  //      }
  //    }
  // 2b. You will replace all the content in the tailwind file with our new config generated below
  //
  //     Expected `colors.tailwind.js` config file write (with asVariables = false):
  //     module.exports = {
  //        "root": {
  //          "mandarin": {
  //            "orange": {
  //              "base": {
  //                 "1": "rgba(229,61,20,1)",
  //                 "2": "rgba(250,112,80,1)",
  //                 "3": "rgba(89,32,7,1)",
  //                 ...
  //              },
  //              "light": { ... },
  //              "dark": { ... }
  //            }
  //          },
  //          ...
  //        }
  //     }
  //
  //     Expected `colors.tailwind.js` config file write (with asVariables = true):
  //     module.exports = {
  //        "root": {
  //          "mandarin": {
  //            "orange": {
  //              "base": {
  //                 "1": "var(--color-root-mandarin-orange-base-1)",
  //                 "2": "var(--color-root-mandarin-orange-base-2)",
  //                 "3": "var(--color-root-mandarin-orange-base-3)",
  //                 ...
  //              },
  //              "light": { ... },
  //              "dark": { ... }
  //
  // NOTE:
  // - Use `fs.readFileSync` to read the files
  // - Use `fs.writeFileSync` to write the file updates
  // - If the supplied `files` array filepath doesn't exist, make the file
  // - The `files` array is an array of filepaths to tailwind js files (`colors.tailwind.js`)
  // - The `tokens` filepath is the filepath to a tokens json file (`colors.json`)
  // - The `asVariables` boolean is a boolean to populate as css variables or evaulate color values