const fs = require("fs");
const path = require("path");

/**
 * Reads CSS variables from a CSS file and returns them as an object
 * @param {string} cssFilePath - Path to the CSS file containing variables
 * @returns {Object} Object with variable names as keys and values as values
 */
function readCssVariables(cssFilePath) {
  const cssContent = fs.readFileSync(cssFilePath, 'utf8');
  const variables = {};
  
  // More robust regex to match CSS variables, handling various formats
  const regex = /--([a-zA-Z0-9_-]+)\s*:\s*([^;]+);/g;

  let match;
  while ((match = regex.exec(cssContent)) !== null) {
    const variableName = match[1].trim();
    const variableValue = match[2].trim();
    variables[variableName] = variableValue;
  }

  return variables;
}

/**
 * Safely escapes special regex characters in a string
 * @param {string} string - String to escape
 * @returns {string} Escaped string safe for use in regex
 */
function escapeRegExp(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

/**
 * Safely escapes replacement string for use in String.replace()
 * @param {string} string - String to escape
 * @returns {string} Escaped string safe for use as replacement
 */
function escapeReplacement(string) {
  return string.replace(/\$/g, '$$$$');
}

/**
 * Replaces CSS variables in files with their actual values
 * This is a more robust version that handles edge cases and complex CSS content
 * @param {Array<string>} filePaths - Array of file paths to process
 * @param {string} cssVariablesFile - Path to CSS file containing variable definitions
 */
function replaceCssVariablesInFilesRobust(filePaths, cssVariablesFile) {
  try {
    // Read CSS variables from the provided CSS file
    const cssVariables = readCssVariables(cssVariablesFile);
    
    if (Object.keys(cssVariables).length === 0) {
      console.warn(`⚠️ No CSS variables found in '${cssVariablesFile}'`);
      return;
    }

    console.log(`📖 Found ${Object.keys(cssVariables).length} CSS variables to replace`);

    // Iterate over each file path in the array
    filePaths.forEach(filePath => {
      try {
        // Check if file exists
        if (!fs.existsSync(filePath)) {
          console.warn(`⚠️ File not found: '${filePath}' - skipping`);
          return;
        }

        // Read file content
        let fileContent = fs.readFileSync(filePath, 'utf8');
        let replacementCount = 0;

        // Replace all occurrences of var(--variable-name) with corresponding value
        Object.entries(cssVariables).forEach(([variableName, variableValue]) => {
          // Escape special characters in variable name for regex
          const escapedVariableName = escapeRegExp(variableName);
          
          // Escape replacement value for String.replace()
          const escapedVariableValue = escapeReplacement(variableValue);
          
          // Create regex pattern that matches var(--variable-name) with optional whitespace
          // This pattern is more robust and handles various CSS contexts
          const pattern = `var\\s*\\(\\s*--${escapedVariableName}\\s*\\)`;
          const regex = new RegExp(pattern, 'g');
          
          // Count matches before replacement for logging
          const matches = fileContent.match(regex);
          if (matches) {
            replacementCount += matches.length;
            fileContent = fileContent.replace(regex, escapedVariableValue);
          }
        });

        // Only write file if changes were made
        if (replacementCount > 0) {
          fs.writeFileSync(filePath, fileContent, 'utf8');
          console.log(`🪢 ${replacementCount} variable(s) replaced in '${filePath}'`);
        } else {
          console.log(`ℹ️ No variables found to replace in '${filePath}'`);
        }

      } catch (error) {
        console.error(`⛔️ Error processing file '${filePath}':`, error.message);
      }
    });

    console.log('✅ CSS variable replacement process completed.');

  } catch (error) {
    console.error(`⛔️ Error in CSS variable replacement:`, error.message);
    throw error;
  }
}

/**
 * Alternative implementation that handles complex CSS content more safely
 * Uses a different approach that's less prone to regex errors
 * @param {Array<string>} filePaths - Array of file paths to process  
 * @param {string} cssVariablesFile - Path to CSS file containing variable definitions
 */
function replaceCssVariablesInFilesSafe(filePaths, cssVariablesFile) {
  try {
    // Read CSS variables from the provided CSS file
    const cssVariables = readCssVariables(cssVariablesFile);
    
    if (Object.keys(cssVariables).length === 0) {
      console.warn(`⚠️ No CSS variables found in '${cssVariablesFile}'`);
      return;
    }

    console.log(`📖 Found ${Object.keys(cssVariables).length} CSS variables to replace`);

    // Iterate over each file path in the array
    filePaths.forEach(filePath => {
      try {
        // Check if file exists
        if (!fs.existsSync(filePath)) {
          console.warn(`⚠️ File not found: '${filePath}' - skipping`);
          return;
        }

        // Read file content
        let fileContent = fs.readFileSync(filePath, 'utf8');
        let replacementCount = 0;
        let originalContent = fileContent;

        // Use a safer string-based replacement approach
        Object.entries(cssVariables).forEach(([variableName, variableValue]) => {
          // Create search patterns with different possible whitespace variations
          const patterns = [
            `var(--${variableName})`,
            `var( --${variableName})`,
            `var(--${variableName} )`,
            `var( --${variableName} )`
          ];

          patterns.forEach(pattern => {
            if (fileContent.includes(pattern)) {
              const beforeLength = fileContent.length;
              fileContent = fileContent.split(pattern).join(variableValue);
              const afterLength = fileContent.length;
              
              // Calculate approximate number of replacements
              if (beforeLength !== afterLength) {
                replacementCount++;
              }
            }
          });
        });

        // Only write file if changes were made
        if (fileContent !== originalContent) {
          fs.writeFileSync(filePath, fileContent, 'utf8');
          console.log(`🪢 Variables replaced in '${filePath}' (${replacementCount} patterns processed)`);
        } else {
          console.log(`ℹ️ No variables found to replace in '${filePath}'`);
        }

      } catch (error) {
        console.error(`⛔️ Error processing file '${filePath}':`, error.message);
      }
    });

    console.log('✅ CSS variable replacement process completed.');

  } catch (error) {
    console.error(`⛔️ Error in CSS variable replacement:`, error.message);
    throw error;
  }
}

module.exports = {
  replaceCssVariablesInFilesRobust,
  replaceCssVariablesInFilesSafe,
  readCssVariables,
  escapeRegExp,
  escapeReplacement
};
