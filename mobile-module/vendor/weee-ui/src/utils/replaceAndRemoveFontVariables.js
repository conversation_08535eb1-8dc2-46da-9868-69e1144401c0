const fs = require("fs");
const path = require("path");

function replaceAndRemoveFontVariables(cssFilePaths, removeZeroPxVals = true) {
  cssFilePaths.forEach((filePath) => {
    // Read the CSS file content
    fs.readFile(filePath, "utf8", (err, data) => {
      if (err) {
        console.error(`⛔️ Error reading file ${filePath}:`, err);
        return;
      }

      // Regular expression to match :root block and its variables
      const rootRegex = /:root\s*{([^}]*)}/s;
      const match = data.match(rootRegex);

      if (!match) {
        console.warn(`⛔️ No :root block found in ${filePath}`);
        return;
      }

      // Extract variables from :root block
      let rootContent = match[1];
      const variableRegex = /--([\w-]+)\s*:\s*([^;]+)/g;
      let variables = {};
      let referencedVariables = new Set();
      let fontVariables = new Set();

      // Match all variables and store them in 'variables' object
      let variableMatch;
      while ((variableMatch = variableRegex.exec(rootContent)) !== null) {
        let variableName = variableMatch[1].trim();
        let variableValue = variableMatch[2].trim();
        variables[variableName] = variableValue;

        // Identify font-related variables
        if (
          variableName.startsWith("font-") &&
          !variableName.startsWith("font-family")
        ) {
          fontVariables.add(variableName);
        }

        // Find references to other variables in this variable's value
        const referenceRegex = /var\s*\(\s*--([\w-]+)\s*\)/g;
        let referenceMatch;
        while ((referenceMatch = referenceRegex.exec(variableValue)) !== null) {
          let referencedVarName = referenceMatch[1];
          referencedVariables.add(referencedVarName);

          // Replace references with actual values
          if (variables.hasOwnProperty(referencedVarName)) {
            let referencedVarValue = variables[referencedVarName];
            variableValue = variableValue.replace(
              `var(--${referencedVarName})`,
              referencedVarValue
            );
          }
        }

        // Update the variable value in 'variables' object
        variables[variableName] = variableValue;
      }

      // Remove font-related variables that have been referenced
      fontVariables.forEach((fontVar) => {
        if (referencedVariables.has(fontVar)) {
          delete variables[fontVar];
        }
      });

      // Reconstruct the :root block with updated variables
      let updatedContent = "";
      Object.keys(variables).forEach((varName) => {
        let varValue = variables[varName];
        if (
          varName !== "" &&
          !(varName.startsWith("font-") && !varName.startsWith("font-family"))
        ) {
          updatedContent += `--${varName}: ${varValue};\n`;
        }
      });

      // Write the final updated CSS back to the file
      const newData = data.replace(rootRegex, `:root {\n${updatedContent}}`);

      // Convert to array of lines, filter and write to file
      const lines = newData.split("\n").filter((line) => {
        return !line.includes("--font-") || line.includes("--font-family");
      });

      // Join the lines back into a single string
      let finalData = lines.join("\n");

      // Filter out lines with variables having values 0px, 0, 0.00, 0.00px
      if (removeZeroPxVals) {
        finalData = finalData
          .split("\n")
          .filter((line) => {
            const zeroValues = ["0px", "0", "0.00", "0.00px"];
            return !zeroValues.some((zeroValue) =>
              line.includes(`: ${zeroValue};`)
            );
          })
          .join("\n");
      }

      // Write the final updated CSS back to the file
      fs.writeFile(filePath, finalData, "utf8", (err) => {
        if (err) {
          console.error(`⛔️ Error writing final updated file ${filePath}:`, err);
        } else {
          console.log(`✨ Successfully populated and removed unused variables from '${filePath}`);
        }
      });
    });
  });
}

module.exports = {
  replaceAndRemoveFontVariables,
};
