const tokenFilter = require("./tokenFilters");
const ColorUtils = require("tinycolor2");

const fontWeightTransform = (group, definitionOnly = false) => {
  const definition = `enki_v2/${group}/fontWeight`;

  if (!definitionOnly) {
    return {
      type: `value`,
      name: definition,
      matcher: (t) => {
        return tokenFilter.includeTokenPathAllValues(t, ["font", "weight"]);
      },
      transformer: (t) => {
        if (tokenFilter.includeTokenPathAllValues(t, ["font", "800"])) {
          return "800";
        } else if (tokenFilter.includeTokenPathAllValues(t, ["font", "700"])) {
          return "700";
        } else if (tokenFilter.includeTokenPathAllValues(t, ["font", "600"])) {
          return "600";
        } else if (tokenFilter.includeTokenPathAllValues(t, ["font", "500"])) {
          return "500";
        } else if (tokenFilter.includeTokenPathAllValues(t, ["font", "400"])) {
          return "400";
        } else {
          return t.original.value ? t.original.value : token.value;
        }
      },
    };
  } else {
    return definition;
  }
};

const fontFamilyTransform = (
  group,
  definitionOnly = false,
  doublequotes = false
) => {
  const definition = `enki_v2/${group}/fontFamily`;

  if (!definitionOnly) {
    return {
      type: `value`,
      name: definition,
      matcher: (t) => {
        return tokenFilter.includeTokenPathAllValues(t, ["font", "family"]);
      },
      transformer: (t) => {
        if (!doublequotes) {
          return `'${t.value}'`;
        } else {
          return `"${t.value}"`;
        }
      },
    };
  } else {
    return definition;
  }
};

const webSystemFontFamilyTransform = (
  group,
  definitionOnly = false,
  doublequotes = false,
  wrapInDoubleQuotes = false
) => {
  const definition = `enki_v2/${group}/webSystemFontFamily`;

  const systemFontStack = `SF Pro Text, SF Pro, Microsoft YaHei, PingFang SC, Roboto, Helvetica Neue, Helvetica, Arial, Apple SD Gothic Neo, Malgun Gothic, BlinkMacSystemFont, -apple-system, Segoe UI, Ubuntu, sans-serif`;
  const systemMonoFontStack = `monospace`;
  const quote = doublequotes ? `"` : `'`;

  function wrapQuotes(str, wrap) {
    if (wrap) {
      return `"${str}"`;
    } else {
      return str;
    }
  }

  if (!definitionOnly) {
    return {
      type: `value`,
      name: definition,
      matcher: (t) => {
        return tokenFilter.includeTokenPathAllValues(t, ["font", "family"]);
      },
      transformer: (t) => {
        if (
          t.value.includes("Noto") ||
          t.value.includes("SF UI") ||
          t.value.includes("Inter")
        ) {
          return wrapQuotes(systemFontStack, wrapInDoubleQuotes);
        }
        if (t.value.includes("Roboto Mono")) {
          return wrapQuotes(`${systemMonoFontStack}`, wrapInDoubleQuotes);
        } else {
          return wrapQuotes(
            `${quote}${t.value}${quote}, ${systemFontStack}`,
            wrapInDoubleQuotes
          );
        }
      },
    };
  } else {
    return definition;
  }
};

const fontTrackingTransform = (group, definitionOnly = false) => {
  const definition = `enki_v2/${group}/fontTracking`;

  if (!definitionOnly) {
    return {
      type: `value`,
      name: definition,
      matcher: (t) => {
        return tokenFilter.includeTokenPathAllValues(t, ["font", "tracking"]);
      },
      transformer: (t) => {
        return t.value + "px";
      },
    };
  } else {
    return definition;
  }
};

const fontSizeTransform = (group, definitionOnly = false) => {
  const definition = `enki_v2/${group}/fontSize`;

  if (!definitionOnly) {
    return {
      type: `value`,
      name: definition,
      matcher: (t) => {
        return tokenFilter.includeTokenPathAllValues(t, ["font", "size"]);
      },
      transformer: (t) => {
        return parseInt(t.value, 10) + "px";
      },
    };
  } else {
    return definition;
  }
};

const behaviorStringTransform = (group, definitionOnly = false) => {
  const definition = `enki_v2/${group}/behaviorString`;

  if (!definitionOnly) {
    return {
      type: `value`,
      name: definition,
      matcher: (t) => {
        return tokenFilter.includeTokenPathAllValues(t, ["behavior"]);
      },
      transformer: (t) => {
        return "";
      },
    };
  } else {
    return definition;
  }
};

const sizePxTransform = (group, definitionOnly = false) => {
  const definition = `enki_v2/${group}/sizePx`;

  if (!definitionOnly) {
    return {
      type: `value`,
      transitive: true,
      name: definition,
      matcher: function (token) {
        return tokenFilter.includeTokenPath(token, "size");
      },
      transformer: function (token) {
        const val = token.value.replace("px", "");
        return `${val}px`;
      },
    };
  } else {
    return definition;
  }
};

const sizeDpTransform = (group, definitionOnly = false) => {
  const definition = `enki_v2/${group}/sizeDp`;

  if (!definitionOnly) {
    return {
      type: `value`,
      transitive: true,
      name: definition,
      matcher: function (token) {
        return (
          tokenFilter.includeTokenPath(token, "size") &&
          tokenFilter.excludeTokenPath(token, "font")
        );
      },
      transformer: function ({ value }) {
        const val = value.replace("dp", "");
        return `${val}dp`;
      },
    };
  } else {
    return definition;
  }
};

const fontLineHeightFloatTransform = (group, definitionOnly = false) => {
  const definition = `enki_v2/${group}/fontLineHeightFloatTransform`;

  if (!definitionOnly) {
    return {
      type: `value`,
      transitive: true,
      name: definition,
      matcher: function (token) {
        return (
          tokenFilter.includeTokenPath(token, "lineheight") 
        );
      },
      transformer: function ({ value }) {
        if (value.includes(".")) {
          return `${value}`
        } else {
          return `${value}.00`
        }
      },
    };
  } else {
    return definition;
  }
};

const fontSizeSpTransform = (group, definitionOnly = false) => {
  const definition = `enki_v2/${group}/fontSizeSp`;

  if (!definitionOnly) {
    return {
      type: `value`,
      transitive: true,
      name: definition,
      matcher: function (token) {
        return (
          tokenFilter.includeTokenPath(token, "size") &&
          tokenFilter.includeTokenPath(token, "font")
        );
      },
      transformer: function ({ value }) {
        const val = value.replace("sp", "");
        return `${val}sp`;
      },
    };
  } else {
    return definition;
  }
};

const swiftUiColorTransform = (group, definitionOnly = false) => {
  const definition = `enki_v2/${group}/swiftUiColor`;

  if (!definitionOnly) {
    return {
      type: `value`,
      transitive: true,
      name: definition,
      matcher: (t) => tokenFilter.includeTokenPathAllValues(t, ["color"]),
      transformer: function (token) {
        if (!token.value || typeof token.value !== "string") return;

        if (token.value.startsWith("UIColor")) {
          return token.value;
        }
        const { r, g, b, a } = ColorUtils(token.value).toRgb();
        const rFixed = (r / 255.0).toFixed(3);
        const gFixed = (g / 255.0).toFixed(3);
        const bFixed = (b / 255.0).toFixed(3);
        return `UIColor(red: ${rFixed}, green: ${gFixed}, blue: ${bFixed}, alpha: ${a})`;
      },
    };
  } else {
    return definition;
  }
};

const swiftSystemFontFamilyTransform = (
  group,
  definitionOnly = false,
  doublequotes = false
) => {
  const definition = `enki_v2/${group}/swiftSystemFontFamily`;

  const systemFont = `System`;
  const systemMonoFont = `monospace`;

  if (!definitionOnly) {
    return {
      type: `value`,
      name: definition,
      matcher: (t) => {
        return tokenFilter.includeTokenPathAllValues(t, ["font", "family"]);
      },
      transformer: (t) => {
        if (
          t.value.includes("Noto") ||
          t.value.includes("SF UI") ||
          t.value.includes("Inter")
        ) {
          return `"${systemFont}"`;
        }
        if (t.value.includes("Roboto Mono")) {
          return `"${systemMonoFont}"`;
        } else {
          return `"${t.value}"`;
        }
      },
    };
  } else {
    return definition;
  }
};

module.exports = {
  fontTrackingTransform,
  fontWeightTransform,
  fontFamilyTransform,
  fontSizeTransform,
  sizePxTransform,
  swiftUiColorTransform,
  swiftSystemFontFamilyTransform,
  webSystemFontFamilyTransform,
  sizeDpTransform,
  fontSizeSpTransform,
  behaviorStringTransform,
  fontLineHeightFloatTransform
};
