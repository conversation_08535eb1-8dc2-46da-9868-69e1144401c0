const fs = require("fs");
const path = require("path");

function mergeRootSelectors(filePaths) {
  filePaths.forEach((filePath) => {
    // Read the content of the file
    const content = fs.readFileSync(filePath, "utf-8");

    // Regular expressions to match CSS comments and :root selectors
    const commentRegex = /\/\*[\s\S]*?\*\//g;
    const rootRegex = /:root\s*{[^}]*}/g;

    // Extract comments and :root selectors
    const comments = content.match(commentRegex) || [];
    const rootSelectors = content.match(rootRegex) || [];

    // Extract the first comment group if it exists
    const firstComment = comments.length > 0 ? comments[0] : "";

    // Combine all :root selectors into one, maintaining order
    let mergedRootContent = rootSelectors
      .map((selector) => {
        return selector.replace(/:root\s*{/, "").replace(/}/, "");
      })
      .join("");

    if (mergedRootContent) {
      mergedRootContent = `:root {${mergedRootContent}}`;
    }

    // Remove all existing :root selectors and comments from the original content
    let cleanedContent = content
      .replace(commentRegex, "")
      .replace(rootRegex, "")
      .trim();

    // Reconstruct the file content
    let newContent =
      `${firstComment}\n\n${mergedRootContent}\n\n${cleanedContent}`.trim();

    // Remove any empty newlines at the end
    newContent = newContent.replace(/\n\s*\n/g, "\n");

    // Write the new content back to the file
    fs.writeFileSync(filePath, newContent, "utf-8");
  });
}

module.exports = {
  mergeRootSelectors,
};
