const Constants = require("../../config/constants");
const Files = require("./files");
const StyleDictionary = require("style-dictionary");
const {
  webSystemFontFamilyTransform,
  sizeDpTransform,
  fontSizeSpTransform,
  fontWeightTransform,

  fontTrackingTransform,
  fontSizeTransform,
  fontLineHeightFloatTransform,
} = require("../../utils/transforms");
const fs = require('fs').promises;
const path = require('path');

// _______________________________
// Transformers
const transforms = {
  [webSystemFontFamilyTransform("android", true)]:
    StyleDictionary.registerTransform({
      ...webSystemFontFamilyTransform("android", false, false, true),
    }),
  [fontSizeSpTransform("android", true)]: StyleDictionary.registerTransform({
    ...fontSizeSpTransform("android"),
  }),
  [sizeDpTransform("android", true)]: StyleDictionary.registerTransform({
    ...sizeDpTransform("android"),
  }),
  [fontLineHeightFloatTransform("android", true)]: StyleDictionary.registerTransform({
    ...fontLineHeightFloatTransform("android"),
  })
  // [fontWeightTransform("android", true)]: StyleDictionary.registerTransform({
  //   ...fontWeightTransform("android"),
  // }),
};

// _____________________________
// Generate files

async function copyAndroidFontFiles() {
  const sourceDir = Constants.V2_ANDROID_FONT_FILES_ORIGIN;
  const destDir = `${Constants.V2_ANDROID_OUTPUT_DIR}${Constants.V2_ANDROID_FONT_FILES_DIST}`;

  try {
    // Create destination directory if it doesn't exist
    await fs.mkdir(destDir, { recursive: true });

    // Read the contents of the source directory
    const files = await fs.readdir(sourceDir);

    // Copy each file to the destination directory
    for (const file of files) {
      const srcPath = path.join(sourceDir, file);
      const destPath = path.join(destDir, file);

      // Get file stats to check if it's a directory or file
      const stats = await fs.stat(srcPath);

      if (stats.isDirectory()) {
        // Recursively copy subdirectories
        await copyAndroidFontFiles(srcPath, destPath);
      } else {
        // Copy file
        await fs.copyFile(srcPath, destPath);
      }
    }

    console.log(`📥 Successfully copied files from ${sourceDir} to ${destDir}`);
  } catch (error) {
    console.error(`⛔️ Error copying files: ${error.message}`);
  }
}

function generateFiles() {
  let outputFiles = Files._generateFiles();

  return {
    transforms: [
      ...Object.keys(transforms),
      "attribute/cti",
      "name/cti/snake",
      "color/hex8android",
    ],
    buildPath: `${Constants.OUTPUT_DIR}/android/`,
    files: outputFiles,
  };
}

module.exports = {
  generateFiles,
  copyAndroidFontFiles
};
