const tokenFilter = require("../../utils/tokenFilters");
const Constants = require("../../config/constants");

function _generateFiles() {
  let _filetype = "xml";

  // ____________________________________________
  // Color
  const colors = [
    {
      destination: `${Constants.V2_ANDROID_COLORS_FILENAME}.${_filetype}`,
      format: "android/colors",
      filter: (t) => tokenFilter.includeTokenPath(t, "color"),
    },
  ];

  // ____________________________________________
  // Dimens
  const dimens = [
    {
      destination: `${Constants.V2_ANDROID_DIMENS_FILENAME}.${_filetype}`,
      format: "android/dimens",
      resourceType: "dimen",
      options: {
        outputReferences: true,
      },
      filter: (t) =>
        tokenFilter.includeTokenPath(t, "size") &&
        tokenFilter.excludeTokenPath(t, "font"),
    },
  ];

  //____________________________________________
  // Typography
  // const fontFaces = Constants.V2_LOCALES.map((locale) => {
  //   return {
  //     destination: `font_face_${locale}.${_filetype}`,
  //     format: "android/resources",
  //     options: {
  //       outputReferences: true,
  //     },
  //     filter: (t) =>
  //       tokenFilter.includeTokenPath(t, "family") &&
  //       tokenFilter.includeTokenPath(t, locale),
  //   };
  // });

  const fontProps = Constants.V2_LOCALES.map((locale) => {
    return {
      destination: `${Constants.V2_ANDROID_DIMENS_FONT_PROP_PREFIX}${locale}.${_filetype}`,
      resourceType: "dimen",
      format: "android/resources",
      options: {
        outputReferences: true,
      },
      filter: (t) => {
        let allow;

        let excludedLocales = Constants.V2_LOCALES.filter(
          (currentLocale) => currentLocale !== locale
        );

        if (tokenFilter.includeTokenPath(t, "font")) {
          allow = true;
        }

        excludedLocales.map((excludedLocale) => {
          if (tokenFilter.includeTokenPath(t, excludedLocale)) {
            allow = false;
          }
        });

        if (tokenFilter.includeTokenPath(t, "family")) {
          allow = false;
        }

        return allow;
      },
    };
  });

  const fontBase = [
    {
      destination: `${Constants.V2_ANDROID_DIMENS_FONT_BASE_FILENAME}.${_filetype}`,
      resourceType: "dimen",
      format: "android/resources",
      filter: (t) => {
        function hasLocale(t) {
          let hasLocale = false;

          Constants.V2_LOCALES.forEach((locale) => {
            let localeCheck = tokenFilter.includeTokenName(t, locale);

            if (localeCheck) {
              hasLocale = true;
            }
          });

          return hasLocale;
        }

        return tokenFilter.includeTokenPath(t, "font") && !hasLocale(t);
      },
    },
  ];

  // ____________________________________________
  // ____________________________________________
  // ____________________________________________
  // Exports
  let finalFiles = [
    ...colors,
    ...dimens,
    ...fontProps,
    ...fontBase,
    // ...fontFaces,
    // ...baseFonts
  ];

  return finalFiles;
}

// ____________________________________________
// ____________________________________________
// ____________________________________________
// Exports
module.exports = { _generateFiles };
