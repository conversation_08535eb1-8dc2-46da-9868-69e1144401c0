const Constants = require("../../config/constants")
const { readFile, writeFile, mkdir, copyFile, rm } = require('node:fs/promises');

const SPACING_FILE =  `${Constants.V2_TOKEN_FORMATTED_OUTPUT_DIR}size.json`;
const TO_SPACING_FILE = `${Constants.V2_TAILWIND_TEMP_DIR}size.json`;

const COPY_FILE_NAMES = [
  `${Constants.V2_TOKEN_FORMATTED_OUTPUT_DIR}colors.json`,
  `${Constants.V2_TOKEN_FORMATTED_OUTPUT_DIR}size.json`,
  `${Constants.V2_TOKEN_FORMATTED_OUTPUT_DIR}typography.all.json`,
]
const COPY_TO_FILE_NAMES = [
  `${Constants.V2_TAILWIND_TEMP_DIR}colors.json`,
  `${Constants.V2_TAILWIND_TEMP_DIR}size.json`,
  `${Constants.V2_TAILWIND_TEMP_DIR}typography.all.json`,
]

async function createTemp() {
    try {
        await mkdir(Constants.V2_TAILWIND_TEMP_DIR, { recursive: true });
    
        COPY_FILE_NAMES.forEach(async (filePath, index) => {
            await copyFile(filePath, COPY_TO_FILE_NAMES[index]);
        })
        
        const spacing = await readFile(SPACING_FILE, { encoding: 'utf8' });
        const spacingObj = JSON.parse(spacing);
        const formatedSpacing = renamedSpacingKey(spacingObj);
        await writeFile(TO_SPACING_FILE, JSON.stringify(formatedSpacing), { encoding: 'utf8' });         
    } catch (err) {
        console.error(err.message);
    }
}

function renamedSpacingKey( spacingJson ) {
    const res = {
        size: {
            spacing: {},
        },
    };
    const keys = Object.keys(spacingJson.size.spacing);
    const prefix = 'enki'
    keys.forEach(key => {
      res.size.spacing[`${prefix}${key}`] = spacingJson.size.spacing[key];
    })
    return res;
}

async function removeTemp() {
    return await rm(Constants.V2_TAILWIND_TEMP_DIR, { recursive: true });
}

module.exports = {
    createTemp,
    removeTemp
}

