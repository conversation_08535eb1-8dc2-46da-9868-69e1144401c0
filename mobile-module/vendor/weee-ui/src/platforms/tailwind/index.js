const Constants = require("../../config/constants");

const StyleDictionary = require("style-dictionary");
const { makeSdTailwindConfig } = require("sd-tailwindcss-transformer");

const fs = require("fs");

const {
  fontWeightTransform,
  webSystemFontFamilyTransform,
  fontTrackingTransform,
  sizePxTransform,
  behaviorStringTransform
} = require("../../utils/transforms");

const { copyFile } = require("node:fs/promises");
const { createTemp, removeTemp } = require("./temp");

function generateTailwindTheme(
  buildPath = Constants.V2_TAILWIND_STYLES_OUTPUT_DIR,
  buildOptions = {
    color: {
      outputReferences: true,
      isVariables: true,
    },
    size: {},
    font: { outputReferences: true, isVariables: false },
  }
) {
  // _______________________________
  // Transformers

  const transforms = {
    [fontWeightTransform("tailwind", true)]: StyleDictionary.registerTransform({
      ...fontWeightTransform("tailwind"),
    }),
    [webSystemFontFamilyTransform("tailwind", true)]:
      StyleDictionary.registerTransform({
        ...webSystemFontFamilyTransform("tailwind"),
      }),
    [fontTrackingTransform("tailwind", true)]:
      StyleDictionary.registerTransform({
        ...fontTrackingTransform("tailwind"),
      }),
    [sizePxTransform("tailwind", true)]: StyleDictionary.registerTransform({
      ...sizePxTransform("tailwind"),
    }),
    [behaviorStringTransform("tailwind", true)]: StyleDictionary.registerTransform({
      ...behaviorStringTransform("tailwind"),
    }),
  };

  // _______________________________
  // Generate

  const options = {
    source: [Constants.V2_TOKEN_SOURCE_DIR_ALL_CORE_VARIABLES],
    transforms: ["attribute/cti", "name/cti/kebab", ...Object.keys(transforms)],
    buildPath: buildPath,
  };

  const tailwindColorStyles = StyleDictionary.extend(
    makeSdTailwindConfig({
      type: "color",
      ...options,
      ...buildOptions.color,
    })
  );

  const tailwindSizeStyles = StyleDictionary.extend(
    makeSdTailwindConfig({
      type: "size",
      ...options,
      ...buildOptions.size,
    })
  );

  const tailwindFontStyles = StyleDictionary.extend(
    makeSdTailwindConfig({
      type: "font",
      ...options,
      ...buildOptions.font,
    })
  );

  tailwindColorStyles.buildAllPlatforms();
  tailwindSizeStyles.buildAllPlatforms();
  tailwindFontStyles.buildAllPlatforms();
}

function moveConfigToDist(
  fileName,
  outputDir = Constants.V2_TAILWIND_OUTPUT_DIR
) {
  console.log(`🔀 Moving Tailwind config '${outputDir + fileName}'`);

  fs.readFile(
    Constants.V2_TAILWIND_CONFIG_SOURCE_PATH + fileName,
    "utf8",
    function (err, data) {
      const date = new Date();
      const fileHeader = `
    /**
     * Do not edit directly if you aren't in ~weee-ui/src/tailwind/source/config.js
     * Autogenerated on ${date}, edit in ~weee-ui/src/tailwind/source/config.js
     * IMPORTANT: Be sure to load all CSS style variables required from ~weee-ui/dist/tailwind/css
     */
    `;
      const fileData = `${fileHeader}\n\n${data}`;

      fs.writeFile(outputDir + fileName, fileData, (err) => {
        if (err) {
          console.error(`⛔️ Error writing to file '${path}'`, err);
        } else {
          console.log(`✅ File '${outputDir + fileName}' has been written`);
        }
      });
    }
  );
}

async function buildTailwindConfig(
  buildPath = Constants.V2_TAILWIND_STYLES_OUTPUT_DIR,
  buildOptions = {
    color: {
      outputReferences: true,
      isVariables: true,
    },
    size: {},
    font: { outputReferences: true, isVariables: false },
  },
  outputDir = Constants.V2_TAILWIND_OUTPUT_DIR
) {
  console.log("");
  console.log("🚧 Building temporary theme");
  await createTemp();
  generateTailwindTheme(buildPath, buildOptions);

  console.log("");
  console.log("🔥 Removing temporary theme");
  await removeTemp();

  console.log("");
  console.log(`📥 Moving Tailwind config.js to '${buildPath}'`);
  await moveConfigToDist(Constants.V2_TAILWIND_CONFIG_FILENAME, outputDir);
}

async function copyTailwindCssFiles(
  outputDir = Constants.V2_TAILWIND_CSS_OUTPUT_DIR,
  includeDarkMode = true
) {
  await copyFile(
    `${Constants.V2_CSS_OUTPUT_DIR}${Constants.V2_CJK_BUNDLE_FILENAME}.css`,
    `${outputDir}${Constants.V2_CJK_BUNDLE_FILENAME}.css`
  );
  console.log(
    `📥 Copied CJK style bundle to '${outputDir}'`
  );

  await copyFile(
    `${Constants.V2_CSS_OUTPUT_DIR}${Constants.V2_LATIN_BUNDLE_FILENAME}.css`,
    `${outputDir}${Constants.V2_LATIN_BUNDLE_FILENAME}.css`
  );
  console.log(
    `📥 Copied Latin style bundle to '${outputDir}'`
  );

  await copyFile(
    `${Constants.V2_CSS_OUTPUT_DIR}${Constants.V2_TALL_BUNDLE_FILENAME}.css`,
    `${outputDir}${Constants.V2_TALL_BUNDLE_FILENAME}.css`
  );
  console.log(
    `📥 Copied Tall style bundle to '${outputDir}'`
  );

  if (includeDarkMode) {
    await copyFile(
      `${Constants.V2_CSS_OUTPUT_DIR}${Constants.V2_COLORS_DARK_FILENAME}.css`,
      `${outputDir}${Constants.V2_COLORS_DARK_FILENAME}.css`
    );
    console.log(
      `🌚 Copied dark mode colors bundle to '${outputDir}'`
    );
  }
}

module.exports = {
  buildTailwindConfig,
  copyTailwindCssFiles,
};
