// _________________________________
// Core
const Constants = require("../../config/constants");
const fs = require("fs");
const { typographyJsonToCss } = require("../../utils/typographyJsonToCss");
const { outputCssToFile } = require("../../utils/outputCssFiles");

// _________________________________
// Token inputs
const typography = require(`../../../${Constants.V2_TOKEN_SOURCE_DIR_TYPOGRAPHY_ALL_JSON}`);

// ____________________________
// Use 'fs' to write to css file
function cleanOutputDir(outputDir = Constants.V2_TAILWIND_CSS_OUTPUT_DIR) {
  const directories = [outputDir];

  directories.forEach((outputDir) => {
    // Check if the directory exists
    if (fs.existsSync(outputDir)) {
      console.log(
        `⚠️ Directory '${outputDir}' exists. It will be removed before creating a new one.`
      );

      // Remove the directory and its contents
      fs.rmSync(outputDir, { recursive: true });
      console.log(`🔥 Directory '${outputDir}' removed.`);
    }

    // Create the new directory
    fs.mkdirSync(outputDir);
    console.log(`🐣 Empty directory '${outputDir}' created.`);
  });
}

// ________________________________
// Module export

function buildTailwindFontVariables(
  outputDir = Constants.V2_TAILWIND_CSS_OUTPUT_DIR,
  outputAll = false
) {
  const styles = {
    latin: typography.font.latin,
    cjk: typography.font.cjk,
    tall: typography.font.tall,
  };

  const latinRootCompiledCss = typographyJsonToCss(
    { latin: styles.latin },
    true
  );
  const cjkRootCompiledCss = typographyJsonToCss({ cjk: styles.cjk }, true);
  const latinTallRootCompiledCss = typographyJsonToCss(
    { tall: styles.tall },
    true
  );

  cleanOutputDir(outputDir);

  outputCssToFile(
    latinRootCompiledCss,
    "fonts.latin.root",
    outputDir
  );
  outputCssToFile(
    cjkRootCompiledCss,
    "fonts.cjk.root",
    outputDir
  );
  outputCssToFile(
    latinTallRootCompiledCss,
    "fonts.tall.root",
    outputDir
  );

  if (outputAll) {
    const allCompiledCss = typographyJsonToCss(styles);
    outputCssToFile(allCompiledCss, "fonts.all");
  }
}

module.exports = {
  buildTailwindFontVariables,
};
