const { cssToJs } = require("../../../utils/cssToJs");
const fs = require("fs");
const Constants = require("../../../config/constants");

function cleanOutputDir(dirPath = Constants.V2_TAILWIND_PLUGIN_JS_OUTPUT_DIR) {
  // Check if the directory exists
  if (fs.existsSync(dirPath)) {
    console.log(`⚠️ Directory '${dirPath}' exists.`);

    return;
  }

  // Create the new directory
  fs.mkdirSync(dirPath);
  console.log(`🐣 Empty directory '${dirPath}' created.`);
}

function outputJsToFile(
  data,
  filename,
  outputDir = Constants.V2_TAILWIND_PLUGIN_JS_OUTPUT_DIR
) {
  const path = `${outputDir}${filename}.tailwind.js`;

  console.log(`📥 Outputting tokens to '${path}'`);

  // Check if the file exists
  if (fs.existsSync(path)) {
    console.log(`💥 File '${path}' exists. Replacing file.`);
  } else {
    console.log(`🐣 File '${path}' does not exist. Creating new file.`);
  }

  // Write the nested object to the file
  fs.writeFile(path, data, (err) => {
    if (err) {
      console.error(`⛔️ Error writing to file '${path}'`, err);
    } else {
      console.log(`✅ File '${path}' has been written`);
    }
  });
}

function generateTailwindCssFilterPluginClasses(
  outputDir = Constants.V2_TAILWIND_PLUGIN_JS_OUTPUT_DIR
) {
  const date = new Date();
  const fileHeader = `
  /** 
   * Do not edit directly
   * Generated on ${date}
   */
  `;
  const moduleHeader = `module.exports = `;

  cleanOutputDir(outputDir);

  fs.readFile(
    `${Constants.V2_CSS_OUTPUT_DIR}${Constants.V2_SOURCE_SUBDIRECTORY_NO_LEADING_SLASH}${Constants.V2_FILTERS_FILENAME}.css`,
    "utf8",
    function (err, data) {
      let fileData = `${fileHeader}\n\n${moduleHeader}${JSON.stringify(
        cssToJs(data),
        null,
        2
      )}`;

      console.log(`🪄 Creating css filter style library for Tailwind → '${outputDir}'`);

      outputJsToFile(fileData, "css-filter-classes", outputDir);
    }
  );
}

module.exports = {
  generateTailwindCssFilterPluginClasses,
};
