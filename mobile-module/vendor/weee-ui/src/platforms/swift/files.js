const tokenFilter = require("../../utils/tokenFilters");
const Constants = require("../../config/constants");

function _generateFiles(
  _formatter = "ios-swift/any.swift",
  _filetype = "swift",
  _fileName = "variables",
  _className = "EnkiKit"
) {
  let _import = ["UIKit"];

  // ____________________________________________
  // Color
  const color = [
    {
      destination: `${_fileName}.${_filetype}`,
      format: _formatter,
      import: _import,
      className: _className,
      options: {
        objectType: "public extension",
        accessControl: "",
        outputReferences: true,
      },
      filter: (t) => tokenFilter.includeTokenPath(t, "color"),
    },
  ];

  // ____________________________________________
  // Size
  const size = [
    {
      destination: `${_fileName}.${_filetype}`,
      format: _formatter,
      import: _import,
      className: _className,
      options: {
        objectType: "public extension",
        accessControl: "",
        outputReferences: true,
      },
      filter: (t) =>
        tokenFilter.includeTokenPath(t, "size") &&
        tokenFilter.excludeTokenPath(t, "font"),
    },
  ];

  // ____________________________________________
  // Typography
  const typography = Constants.V2_LOCALES.map((locale) => {
    return {
      destination: `typography-${locale}.${_filetype}`,
      format: _formatter,
      import: _import,
      className: _className,
      options: {
        outputReferences: true,
      },
      filter: (t) => {
        let allow;

        let excludedLocales = Constants.V2_LOCALES.filter(
          (currentLocale) => currentLocale !== locale
        );

        if (tokenFilter.includeTokenPath(t, "font")) {
          allow = true;
        }

        excludedLocales.map((excludedLocale) => {
          if (tokenFilter.includeTokenPath(t, excludedLocale)) {
            allow = false;
          }
        });

        return allow;
      },
    };
  });

  // ____________________________________________
  // ____________________________________________
  // ____________________________________________
  // Exports
  let finalFiles = [...color, ...size, ...typography];

  return finalFiles;
}

// ____________________________________________
// ____________________________________________
// ____________________________________________
// Exports
module.exports = { _generateFiles };
