const Constants = require("../../config/constants");
const Files = require("./files");
const StyleDictionary = require("style-dictionary");
const ColorUtils = require("tinycolor2");
const tokenFilter = require("../../utils/tokenFilters");

const {
  fontWeightTransform,
  swiftSystemFontFamilyTransform,
  swiftUiColorTransform,
} = require("../../utils/transforms");

// _______________________________
// Transformers

const transforms = {
  [fontWeightTransform("swift", true)]: StyleDictionary.registerTransform({
    ...fontWeightTransform("swift"),
  }),
  [swiftUiColorTransform("swift", true)]: StyleDictionary.registerTransform({
    ...swiftUiColorTransform("swift"),
  }),
  [swiftSystemFontFamilyTransform("swift", true)]:
    StyleDictionary.registerTransform({
      ...swiftSystemFontFamilyTransform("swift"),
    }),
};

StyleDictionary.registerTransform({
  type: `value`,
  transitive: true,
  name: `enki/size`,
  matcher: (t) => t.attributes.category === "size",
  transformer: function (token) {
    let value = `${token.original.value}`;
    return parseFloat(value).toFixed(2);
  },
});

StyleDictionary.registerTransformGroup({
  name: "weee-swift",
  transforms: [
    "attribute/cti",
    "size/px",
    "name/cti/camel",
    "color/UIColorSwift",
    "asset/swift/literal",
    "size/swift/remToCGFloat",
    "font/swift/literal",
  ],
});

// _____________________________
// Generate files
function generateFiles(
  formatter = "ios-swift/any.swift",
  filetype = "swift",
  filename = "variables",
  className = "EnkiKit"
) {
  
  let outputFiles = Files._generateFiles(
    formatter,
    filetype,
    filename,
    className
  );

  return {
    transforms: [
      ...Object.keys(transforms),
      "attribute/cti",
      "name/cti/camel",
      "content/swift/literal",
      "asset/swift/literal",
      "enki/size",
      // "size/swift/remToCGFloat",
    ],
    buildPath: `${Constants.OUTPUT_DIR}/${filetype}/`,
    files: outputFiles,
  };
}

module.exports = {
  generateFiles,
};
