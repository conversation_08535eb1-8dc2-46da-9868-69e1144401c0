const tokenFilter = require("../../utils/tokenFilters");


function _generateFiles(_darkmode = false) {
  let _formatter = "ios-swift/any.swift";
  let _filetype = "swift";
  let _className = "EnkiKit";
  let _import = ["UIKit"];

  // ____________________________________________
  // Color
  let color = [{
    destination: "color.json",
    format: "json/flat",
    filter: (t) => tokenFilter.includeTokenName(t, "color-"),
  }]

  // ____________________________________________
  // Size
  let size = [{
    destination: "size.json",
    format: "json/flat",
    filter: (t) =>
      tokenFilter.includeTokenPath(t, "size") &&
      tokenFilter.excludeTokenPath(t, "font"),
  }]

  // ____________________________________________
  // Typography
  let typography = [{
    destination: "typography.json",
    format: "json/flat",
    filter: (t) => tokenFilter.includeTokenPath(t, "font"),
  }]

  // ____________________________________________
  // ____________________________________________
  // ____________________________________________
  // Exports
  let finalFiles = [
    ...color,
    ...size,
    ...typography,
  ];

  return finalFiles;
}

// ____________________________________________
// ____________________________________________
// ____________________________________________
// Exports
module.exports = { _generateFiles };
