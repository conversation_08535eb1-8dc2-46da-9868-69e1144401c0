const Constants = require("../../config/constants");
const StyleDictionary = require("style-dictionary");
const Files = require("./files");
const tokenFilter = require("../../utils/tokenFilters");

StyleDictionary.registerTransform({
  type: `value`,
  name: `web/enki_v2/json/fontFamily`,
  matcher: (t) => {
    return tokenFilter.includeTokenPathAllValues(t, ["font", "family"]);
  },
  transformer: function (token) {
    return `'${token.value}'`;
  },
});


StyleDictionary.registerTransform({
  type: `value`,
  name: `web/enki_v2/json/fontWeight`,
  matcher: (t) => {
    return tokenFilter.includeTokenPathAllValues(t, ["font", "weight"]);
  },
  transformer: (t) => {          
      if(tokenFilter.includeTokenPathAllValues(t, ["font", "700"])) {
        return "700";
      } else if(tokenFilter.includeTokenPathAllValues(t, ["font", "600"])) {
        return "600";
      } else if(tokenFilter.includeTokenPathAllValues(t, ["font", "500"])) {
        return "500";
      } else if(tokenFilter.includeTokenPathAllValues(t, ["font", "400"])) {
        return "400";
      } else {
        return t.original.value ? t.original.value : token.value
      }
  },
});

// _____________________________
// Generate files
function generateFiles(formatter = "json/flat", filetype = "json", darkmode = false) {
  let outputFiles = Files._generateFiles(darkmode);

  return {
    transforms: [
      'attribute/cti',
      'name/cti/kebab',
      'size/px',
      'color/css',
      'web/enki_v2/json/fontFamily',
      'web/enki_v2/json/fontWeight'
    ],
    buildPath: `${Constants.OUTPUT_DIR}/${filetype}/`,
    files: outputFiles,
  };
}

module.exports = {
  generateFiles,
};