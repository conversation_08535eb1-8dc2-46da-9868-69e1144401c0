const tokenFilter = require("../../utils/tokenFilters");

// ____________________________________________
// Tokens
const token = [
  // ____________________________________________
  // Root
  {
    destination: "_root/token.js",
    format: "javascript/module-flat",
    filter: (t) => t.name.includes("root-") && !t.name.includes("-root-"),
  },

  // ____________________________________________
  // UI
  {
    destination: "ui/token.js",
    format: "javascript/module-flat",
    filter: (t) =>
      (t.filePath.includes("ui") && !t.filePath.includes("typography")) ||
      t.name.includes("locale-"),
  },

  // ____________________________________________
  // Brand
  {
    destination: "brand/token.js",
    format: "javascript/module-flat",
    filter: (t) => t.name.includes("brand-") && t.name.includes("color"),
  },
];

// ____________________________________________
// ____________________________________________
// ____________________________________________
// Exports
module.exports = [...token];
