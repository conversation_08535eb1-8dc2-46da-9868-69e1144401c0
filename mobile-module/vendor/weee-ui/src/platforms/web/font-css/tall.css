@font-face {
  font-display: swap;
  font-family: Be Vietnam Pro;
  font-style: normal;
  font-weight: 300;
  src: url(https://static.weeecdn.net/common/enki-fonts/vi/QdVMSTAyLFyeg_IDWvOJmVES_HScJ286Rb0bcw.woff2)
    format("woff2");
  unicode-range: u+0102-0103, u+0110-0111, u+0128-0129, u+0168-0169, u+01a0-01a1,
    u+01af-01b0, u+1ea0-1ef9, u+20ab;
}
@font-face {
  font-display: swap;
  font-family: Be Vietnam Pro;
  font-style: normal;
  font-weight: 300;
  src: url(https://static.weeecdn.net/common/enki-fonts/vi/QdVMSTAyLFyeg_IDWvOJmVES_HScJ287Rb0bcw.woff2)
    format("woff2");
  unicode-range: u+0100-024f, u+0259, u+1e??, u+2020, u+20a0-20ab, u+20ad-20cf,
    u+2113, u+2c60-2c7f, u+a720-a7ff;
}
@font-face {
  font-display: swap;
  font-family: Be Vietnam Pro;
  font-style: normal;
  font-weight: 300;
  src: url(https://static.weeecdn.net/common/enki-fonts/vi/QdVMSTAyLFyeg_IDWvOJmVES_HScJ281Rb0.woff2)
    format("woff2");
  unicode-range: u+00??, u+0131, u+0152-0153, u+02bb-02bc, u+02c6, u+02da,
    u+02dc, u+2000-206f, u+2074, u+20ac, u+2122, u+2191, u+2193, u+2212, u+2215,
    u+feff, u+fffd;
}
@font-face {
  font-display: swap;
  font-family: Be Vietnam Pro;
  font-style: normal;
  font-weight: 400;
  src: url(https://static.weeecdn.net/common/enki-fonts/vi/QdVPSTAyLFyeg_IDWvOJmVES_Hw4BXoKZA.woff2)
    format("woff2");
  unicode-range: u+0102-0103, u+0110-0111, u+0128-0129, u+0168-0169, u+01a0-01a1,
    u+01af-01b0, u+1ea0-1ef9, u+20ab;
}
@font-face {
  font-display: swap;
  font-family: Be Vietnam Pro;
  font-style: normal;
  font-weight: 400;
  src: url(https://static.weeecdn.net/common/enki-fonts/vi/QdVPSTAyLFyeg_IDWvOJmVES_Hw5BXoKZA.woff2)
    format("woff2");
  unicode-range: u+0100-024f, u+0259, u+1e??, u+2020, u+20a0-20ab, u+20ad-20cf,
    u+2113, u+2c60-2c7f, u+a720-a7ff;
}
@font-face {
  font-display: swap;
  font-family: Be Vietnam Pro;
  font-style: normal;
  font-weight: 400;
  src: url(https://static.weeecdn.net/common/enki-fonts/vi/QdVPSTAyLFyeg_IDWvOJmVES_Hw3BXo.woff2)
    format("woff2");
  unicode-range: u+00??, u+0131, u+0152-0153, u+02bb-02bc, u+02c6, u+02da,
    u+02dc, u+2000-206f, u+2074, u+20ac, u+2122, u+2191, u+2193, u+2212, u+2215,
    u+feff, u+fffd;
}
@font-face {
  font-display: swap;
  font-family: Be Vietnam Pro;
  font-style: normal;
  font-weight: 500;
  src: url(https://static.weeecdn.net/common/enki-fonts/vi/QdVMSTAyLFyeg_IDWvOJmVES_HTEJm86Rb0bcw.woff2)
    format("woff2");
  unicode-range: u+0102-0103, u+0110-0111, u+0128-0129, u+0168-0169, u+01a0-01a1,
    u+01af-01b0, u+1ea0-1ef9, u+20ab;
}
@font-face {
  font-display: swap;
  font-family: Be Vietnam Pro;
  font-style: normal;
  font-weight: 500;
  src: url(https://static.weeecdn.net/common/enki-fonts/vi/QdVMSTAyLFyeg_IDWvOJmVES_HTEJm87Rb0bcw.woff2)
    format("woff2");
  unicode-range: u+0100-024f, u+0259, u+1e??, u+2020, u+20a0-20ab, u+20ad-20cf,
    u+2113, u+2c60-2c7f, u+a720-a7ff;
}
@font-face {
  font-display: swap;
  font-family: Be Vietnam Pro;
  font-style: normal;
  font-weight: 500;
  src: url(https://static.weeecdn.net/common/enki-fonts/vi/QdVMSTAyLFyeg_IDWvOJmVES_HTEJm81Rb0.woff2)
    format("woff2");
  unicode-range: u+00??, u+0131, u+0152-0153, u+02bb-02bc, u+02c6, u+02da,
    u+02dc, u+2000-206f, u+2074, u+20ac, u+2122, u+2191, u+2193, u+2212, u+2215,
    u+feff, u+fffd;
}
@font-face {
  font-display: swap;
  font-family: Be Vietnam Pro;
  font-style: normal;
  font-weight: 600;
  src: url(https://static.weeecdn.net/common/enki-fonts/vi/QdVMSTAyLFyeg_IDWvOJmVES_HToIW86Rb0bcw.woff2)
    format("woff2");
  unicode-range: u+0102-0103, u+0110-0111, u+0128-0129, u+0168-0169, u+01a0-01a1,
    u+01af-01b0, u+1ea0-1ef9, u+20ab;
}
@font-face {
  font-display: swap;
  font-family: Be Vietnam Pro;
  font-style: normal;
  font-weight: 600;
  src: url(https://static.weeecdn.net/common/enki-fonts/vi/QdVMSTAyLFyeg_IDWvOJmVES_HToIW87Rb0bcw.woff2)
    format("woff2");
  unicode-range: u+0100-024f, u+0259, u+1e??, u+2020, u+20a0-20ab, u+20ad-20cf,
    u+2113, u+2c60-2c7f, u+a720-a7ff;
}
@font-face {
  font-display: swap;
  font-family: Be Vietnam Pro;
  font-style: normal;
  font-weight: 600;
  src: url(https://static.weeecdn.net/common/enki-fonts/vi/QdVMSTAyLFyeg_IDWvOJmVES_HToIW81Rb0.woff2)
    format("woff2");
  unicode-range: u+00??, u+0131, u+0152-0153, u+02bb-02bc, u+02c6, u+02da,
    u+02dc, u+2000-206f, u+2074, u+20ac, u+2122, u+2191, u+2193, u+2212, u+2215,
    u+feff, u+fffd;
}
@font-face {
  font-display: swap;
  font-family: Be Vietnam Pro;
  font-style: normal;
  font-weight: 700;
  src: url(https://static.weeecdn.net/common/enki-fonts/vi/QdVMSTAyLFyeg_IDWvOJmVES_HSMIG86Rb0bcw.woff2)
    format("woff2");
  unicode-range: u+0102-0103, u+0110-0111, u+0128-0129, u+0168-0169, u+01a0-01a1,
    u+01af-01b0, u+1ea0-1ef9, u+20ab;
}
@font-face {
  font-display: swap;
  font-family: Be Vietnam Pro;
  font-style: normal;
  font-weight: 700;
  src: url(https://static.weeecdn.net/common/enki-fonts/vi/QdVMSTAyLFyeg_IDWvOJmVES_HSMIG87Rb0bcw.woff2)
    format("woff2");
  unicode-range: u+0100-024f, u+0259, u+1e??, u+2020, u+20a0-20ab, u+20ad-20cf,
    u+2113, u+2c60-2c7f, u+a720-a7ff;
}
@font-face {
  font-display: swap;
  font-family: Be Vietnam Pro;
  font-style: normal;
  font-weight: 700;
  src: url(https://static.weeecdn.net/common/enki-fonts/vi/QdVMSTAyLFyeg_IDWvOJmVES_HSMIG81Rb0.woff2)
    format("woff2");
  unicode-range: u+00??, u+0131, u+0152-0153, u+02bb-02bc, u+02c6, u+02da,
    u+02dc, u+2000-206f, u+2074, u+20ac, u+2122, u+2191, u+2193, u+2212, u+2215,
    u+feff, u+fffd;
}