const StyleDictionary = require("style-dictionary");
const Constants = require("../../config/constants");
const Files = require("./files");
const tokenFilter = require("../../utils/tokenFilters");
const {
  fontWeightTransform,
  webSystemFontFamilyTransform,
  fontTrackingTransform,
  fontSizeTransform,
} = require("../../utils/transforms");

// _______________________________
// Transformers
const transforms = {
  [fontWeightTransform("web", true)]: StyleDictionary.registerTransform({
    ...fontWeightTransform("web"),
  }),
  [webSystemFontFamilyTransform("web", true)]:
    StyleDictionary.registerTransform({
      ...webSystemFontFamilyTransform("web"),
    }),
  [fontTrackingTransform("web", true)]: StyleDictionary.registerTransform({
    ...fontTrackingTransform("web"),
  }),
  [fontSizeTransform("web", true)]: StyleDictionary.registerTransform({
    ...fontSizeTransform("web"),
  }),
};

// _______________________________
// Generate files
function generateFiles(
  formatter = "scss/variables",
  filetype = "scss",
  filename = "variables",
  selector = ":root",
  subdirectory = "/",
  generateWithoutReferences = false
) {
  let outputFiles = Files._generateFiles(
    formatter,
    filetype,
    filename,
    selector
  );

  let noRefOutputFiles = Files._generateFiles(
    formatter,
    filetype,
    filename,
    selector,
    false
  );

  let finalOutputFiles = outputFiles;

  if (generateWithoutReferences) {
    finalOutputFiles = noRefOutputFiles;
  }

  let _subdirectory = `${subdirectory ? subdirectory : `/`}`;

  return {
    transforms: [
      ...Object.keys(transforms),
      "attribute/cti",
      "name/cti/kebab",
      "time/seconds",
      "size/px",
      "color/css",
    ],
    buildPath: `${Constants.OUTPUT_DIR}/${filetype}${_subdirectory}`,
    files: finalOutputFiles,
  };
}

module.exports = {
  generateFiles,
};
