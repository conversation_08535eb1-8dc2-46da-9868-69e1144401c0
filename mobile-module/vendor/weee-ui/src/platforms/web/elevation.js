// _________________________________
// Core
const Constants = require("../../config/constants");
const fs = require("fs");
const { elevationJsonToCss } = require("../../utils/elevationJsonToCss");
const { outputCssToFile } = require("../../utils/outputCssFiles");
const { wrapWithFileHeader } = require("../../utils/wrapWithFileHeader");

// _________________________________
// Token inputs
const elevation = require(`../../../${Constants.V2_TOKEN_SOURCE_DIR_ELEVATION_JSON}`);

// ____________________________
// Use 'fs' to write to css file
function cleanOutputDir() {
  const directories = [Constants.V2_CSS_OUTPUT_DIR];

  directories.forEach((outputDir) => {
    // Check if the directory exists
    if (fs.existsSync(outputDir)) {
      console.log(`⚠️ Directory '${outputDir}' exists.`);

      return;
    }

    // Create the new directory
    fs.mkdirSync(outputDir);
    console.log(`🐣 Empty directory '${outputDir}' created.`);
  });
}

// ________________________________
// Module export

function buildCssElevationStyles(outputAll = false) {
  const styles = elevation;

  const compiledCss = elevationJsonToCss(styles);
  const finalData = wrapWithFileHeader(compiledCss);

  cleanOutputDir();

  outputCssToFile(
    finalData,
    Constants.V2_ELEVATION_FILENAME,
    `${Constants.V2_CSS_OUTPUT_DIR}${Constants.V2_SOURCE_SUBDIRECTORY_NO_LEADING_SLASH}`
  );
}

module.exports = {
  buildCssElevationStyles,
};
