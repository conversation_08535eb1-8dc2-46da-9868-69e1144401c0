// _________________________________
// Core
const Constants = require("../../config/constants");
const fs = require("fs");
const { elevationJsonToCss } = require("../../utils/elevationJsonToCss");
const { outputCssToFile } = require("../../utils/outputCssFiles");
const { wrapWithFileHeader } = require("../../utils/wrapWithFileHeader");
const {
  generateCssClassesFromPattern,
} = require("../../utils/generateCssClassesFromPattern");

// ____________________________
// Use 'fs' to write to css file
function cleanOutputDir() {
  const directories = [Constants.V2_CSS_OUTPUT_DIR];

  directories.forEach((outputDir) => {
    // Check if the directory exists
    if (fs.existsSync(outputDir)) {
      console.log(`⚠️ Directory '${outputDir}' exists.`);

      return;
    }

    // Create the new directory
    fs.mkdirSync(outputDir);
    console.log(`🐣 Empty directory '${outputDir}' created.`);
  });
}

// ________________________________
// Module export

function buildCssFilterStyles(outputAll = false) {
  cleanOutputDir();

  generateCssClassesFromPattern(
    `${Constants.V2_CSS_OUTPUT_DIR}${Constants.V2_SOURCE_SUBDIRECTORY_NO_LEADING_SLASH}${Constants.V2_COLORS_FILENAME}.css`,
    "style-filter",
    "filter",
    "enki",
    "style-",
    (err, result) => {
      if (err) {
        console.error("⛔️ Error generating CSS filter classes:", err);
      } else {
        const finalData = wrapWithFileHeader(result);

        outputCssToFile(
          finalData,
          Constants.V2_FILTERS_FILENAME,
          `${Constants.V2_CSS_OUTPUT_DIR}${Constants.V2_SOURCE_SUBDIRECTORY_NO_LEADING_SLASH}`
        );

        console.log(result);
      }
    }
  );
}

module.exports = {
  buildCssFilterStyles,
};
