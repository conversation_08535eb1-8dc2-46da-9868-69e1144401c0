// _________________________________
// Core
const Constants = require("../../config/constants");
const fs = require("fs");
const { buttonJsonToCss } = require("../../utils/buttonJsonToCss");
const { outputCssToFile } = require("../../utils/outputCssFiles");

// _________________________________
// Token inputs
const buttons = require(`../../../${Constants.V2_TOKEN_SOURCE_DIR_COLORS_JSON}`);
const { wrapWithFileHeader } = require("../../utils/wrapWithFileHeader");

// ____________________________
// Use 'fs' to write to css file
function cleanOutputDir() {
  const directories = [Constants.V2_CSS_OUTPUT_DIR];

  directories.forEach((outputDir) => {
    // Check if the directory exists
    if (fs.existsSync(outputDir)) {
      console.log(`⚠️ Directory '${outputDir}' exists.`);

      return;
    }

    // Create the new directory
    fs.mkdirSync(outputDir);
    console.log(`🐣 Empty directory '${outputDir}' created.`);
  });
}

// ________________________________
// Module export

function buildCssButtonStyles(outputAll = false) {
  const styles = buttons;

  const compiledCss = buttonJsonToCss(styles);

  const finalData = wrapWithFileHeader(compiledCss);

  cleanOutputDir();

  outputCssToFile(
    finalData,
    Constants.V2_BUTTONS_FILENAME,
    `${Constants.V2_CSS_OUTPUT_DIR}${Constants.V2_SOURCE_SUBDIRECTORY_NO_LEADING_SLASH}`
  );
}

module.exports = {
  buildCssButtonStyles,
};
