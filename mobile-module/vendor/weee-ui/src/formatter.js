const Constants = require("./config/constants");
const fs = require("fs");
const { filter } = require("./platforms/js-module/files");
const { removeTrailingZeroesFromJsonDir } = require("./utils/removeTrailingZeroesFromJsonDir");

// _______________________________________
// _______________________________________
// _______________________________________  
// Get args
const args = process.argv.slice(2).reduce((acc, arg) => {
  const [key, value] = arg.replace('--', '').split('=');
  acc[key] = value;
  return acc;
}, {});


// _______________________________________
// _______________________________________
// _______________________________________
// Token inputs
const tokenSourceJson = {
  brand: require(`${Constants.V2_TOKEN_ORIGIN}input/brand.json`),
  ui: require(`${Constants.V2_TOKEN_ORIGIN}input/ui.json`),
};

const references = {
  brand: {
    typography: tokenSourceJson.brand[args.TOKEN_LIB]
      ? tokenSourceJson.brand[args.TOKEN_LIB].TYPOGRAPHY
      : tokenSourceJson.brand.TYPOGRAPHY[args.TOKEN_LIB],
    colors: tokenSourceJson.brand[args.TOKEN_LIB]
      ? tokenSourceJson.brand[args.TOKEN_LIB].COLORS
      : tokenSourceJson.brand.COLORS[args.TOKEN_LIB],
  },
  ui: {
    spacing: tokenSourceJson.ui["base"]
      ? tokenSourceJson.ui["base"].SPACING
      : tokenSourceJson.ui.SPACING["base"],
    device: tokenSourceJson.ui["base"]
      ? tokenSourceJson.ui["base"].DEVICE
      : tokenSourceJson.ui.DEVICE["base"],
    borders: tokenSourceJson.ui["base"]
      ? tokenSourceJson.ui["base"].BORDERS
      : tokenSourceJson.ui.BORDERS["base"],
    elevation: tokenSourceJson.ui["base"]
      ? tokenSourceJson.ui["base"].ELEVATION
      : tokenSourceJson.ui.ELEVATION["base"],
    typography: tokenSourceJson.ui[args.TOKEN_LIB + "-base"]
      ? tokenSourceJson.ui[args.TOKEN_LIB + "-base"].TYPOGRAPHY
      : tokenSourceJson.ui.TYPOGRAPHY[args.TOKEN_LIB + "-base"],
    colors: {
      light: tokenSourceJson.ui[args.TOKEN_LIB + "-light"]
        ? tokenSourceJson.ui[args.TOKEN_LIB + "-light"].COLORS
        : tokenSourceJson.ui.COLORS[args.TOKEN_LIB + "-light"],
      dark: tokenSourceJson.ui[args.TOKEN_LIB + "-dark"]
        ? tokenSourceJson.ui[args.TOKEN_LIB + "-dark"].COLORS
        : tokenSourceJson.ui.COLORS[args.TOKEN_LIB + "-dark"],
    },
  },
};

// _______________________________________
// _______________________________________
// _______________________________________
// Flattened inputs with their references
const flattenedJsonInputs = [
  {
    namespace: "typography",
    data: { ...references.brand.typography, ...references.ui.typography },
  },
  {
    namespace: "size",
    data: {
      ...references.ui.spacing,
      ...references.ui.device,
      ...references.ui.borders,
      ...references.ui.elevation,
      ...references.ui.gutters,
    },
  },
  {
    namespace: "colors",
    data: { ...references.brand.colors, ...references.ui.colors.light },
  },
  {
    namespace: "colors.dark",
    data: { ...references.brand.colors, ...references.ui.colors.dark },
  },
];

function convertToNestedObject(json, id) {
  console.log(`💠 Formatted tokens '${id}' to nested object`);

  const result = {};

  for (const key in json) {
    const parts = key.split("-");
    let currentLevel = result;

    parts.forEach((part, index) => {
      if (!currentLevel[part]) {
        if (index === parts.length - 1) {
          let value = json[key].varValue;
          if (json[key].isAlias) {
            value = `{${value.replaceAll("-", ".")}}`;
          } else if (!isNaN(value)) {
            value = parseFloat(value).toFixed(2);
          }
          currentLevel[part] = { value };
        } else {
          currentLevel[part] = {};
        }
      }
      currentLevel = currentLevel[part];
    });
  }

  return result;
}

// _______________________________________
// _______________________________________
// _______________________________________
// Token filtering & modifiying of tokens

const universalTypographyGroups = ["font-number", "font-family-number"];

const cjkTypographyGroups = [
  "font-family-cjk-body",
  "font-family-cjk-heading",
  "font-family-cjk-display",
  "font-family-cjk-main",
  "font-family-cjk-expressive",
  ...universalTypographyGroups,
];

const latinTypographyGroups = [
  "font-family-latin-body",
  "font-family-latin-heading",
  "font-family-latin-display",
  "font-family-latin-main",
  "font-family-latin-expressive",
  ...universalTypographyGroups,
];

const tallTypographyGroups = [
  "font-family-tall-body",
  "font-family-tall-heading",
  "font-family-tall-display",
  "font-family-tall-main",
  "font-family-tall-expressive",
  ...universalTypographyGroups,
];

const allTypographyGroupStyles = [
  "font-family-cjk",
  "font-cjk",
  "font-family-latin",
  "font-latin",
  "font-family-tall",
  "font-tall",
];

const allLanguageGroups = [
  "font-ko",
  "font-zh",
  "font-zht",
  "font-ja",
  "font-family-ko",
  "font-family-zh",
  "font-family-zht",
  "font-family-ja",
  "font-cjk",
  "font-family-cjk",
  "font-latin",
  "font-family-latin",
  "font-tall",
  "font-family-tall",
  ...universalTypographyGroups
];

const elevationGroups = [
  "elevation",
];

const elevationRootExclude = [
  "style",
];

const buttonGroups = [
  "color-btn",
];

const buttonGroupsDisabled = [
  "disabled",  
];

function filterTokensMatching(originalObject, filters) {
  if (
    !filters ||
    filters.length === 0 ||
    (filters.length === 1 && filters[0] === "")
  ) {
    return originalObject;
  }

  const filteredData = Object.keys(originalObject.data).reduce((acc, key) => {
    if (filters.some((filter) => key.includes(filter))) {
      acc[key] = originalObject.data[key];
    }
    return acc;
  }, {});

  return {
    ...originalObject,
    data: filteredData,
  };
}

function filterTokensRemoving(originalObject, filters) {
  if (
    !filters ||
    filters.length === 0 ||
    (filters.length === 1 && filters[0] === "")
  ) {
    return originalObject;
  }

  const filteredData = Object.keys(originalObject.data).reduce((acc, key) => {
    if (!filters.some((filter) => key.includes(filter))) {
      acc[key] = originalObject.data[key];
    }
    return acc;
  }, {});

  return {
    ...originalObject,
    data: filteredData,
  };
}

function filterByTokenNamePatterns(tokens, filters = ["latin"], matching = true) {
  let filteredOutput = [];

  if (matching) {
    tokens.forEach((dictionary) => {
      let finalOutput = {};
      let filtered = filterTokensMatching(dictionary, filters);

      if (Object.keys(filtered.data).length > 0) {
        finalOutput = filtered;

        filteredOutput.push(finalOutput);
      }
    });
  } else {
    tokens.forEach((dictionary) => {
      let filtered = filterTokensRemoving(dictionary, filters);

      filteredOutput.push(filtered);
    });
  }
  return filteredOutput;
}

// _______________________________________
// _______________________________________
// _______________________________________
// Use `fs` to write to JSON file

function cleanOutputDir() {
  const dirPath = Constants.V2_TOKEN_FORMATTED_OUTPUT_DIR;

  // Check if the directory exists
  if (fs.existsSync(dirPath)) {
    console.log(
      `⚠️ Directory '${dirPath}' exists. It will be removed before creating a new one.`
    );

    // Remove the directory and its contents
    fs.rmSync(dirPath, { recursive: true });
    console.log(`🔥 Directory '${dirPath}' removed.`);
  }

  // Create the new directory
  fs.mkdirSync(dirPath);
  console.log(`🐣 Empty directory '${dirPath}' created.`);
}

function outputTokensToFile(data, filename) {
  const path = `${Constants.V2_TOKEN_FORMATTED_OUTPUT_DIR}${filename}.json`;

  console.log(`📥 Outputting tokens to '${path}'`);

  // Check if the file exists
  if (fs.existsSync(path)) {
    console.log(`💥 File '${path}' exists. Replacing file.`);
  } else {
    console.log(`🐣 File '${path}' does not exist. Creating new file.`);
  }

  // Write the nested object to the file
  fs.writeFile(path, JSON.stringify(data, null, 2), (err) => {
    if (err) {
      console.error(`⛔️ Error writing to file '${path}'`, err);
    } else {
      console.log(`✅ File '${path}' has been written`);
    }
  });
}

// _______________________________________
// _______________________________________
// _______________________________________
// Module export
function init() {
  console.log("");
  console.log("┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓");
  console.log("┃                                            ┃");
  console.log("┃     💠 Running Enki token formatter...     ┃");
  console.log("┃                                            ┃");
  console.log("┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛");
  console.log("");

  console.log(
    `${tokenSourceJson.ui ? "🚀" : "⛔️"} tokenSourceJson.ui: ${
      tokenSourceJson.ui ? "Loaded" : "Not found"
    }`
  );
  console.log(
    `${tokenSourceJson.brand ? "🚀" : "⛔️"} tokenSourceJson.brand: ${
      tokenSourceJson.brand ? "Loaded" : "Not found"
    }`
  );

  // ___________________________
  // Clean output directory
  cleanOutputDir();


  // ___________________________________
  // Export CJK typography tokens
  const allTypographyTokens = filterByTokenNamePatterns(
    flattenedJsonInputs,
    allTypographyGroupStyles,
    true
  );

  allTypographyTokens.forEach((input) => {
    const formatted = convertToNestedObject(input.data, input.namespace);
    let modifier = ""

    if (input.namespace == 'typography') {
      modifier = 'all'
    }

    outputTokensToFile(formatted, `${input.namespace}${modifier ? `.${modifier}` : ""}`);
  })
  
  // ___________________________________
  // Export CJK typography tokens
  const cjkTypographyTokens = filterByTokenNamePatterns(
    flattenedJsonInputs,
    cjkTypographyGroups,
    true
  );

  cjkTypographyTokens.forEach((input) => {
    const formatted = convertToNestedObject(input.data, input.namespace);
    let modifier = ""

    if (input.namespace == 'typography') {
      modifier = 'cjk'
    }

    outputTokensToFile(formatted, `${input.namespace}${modifier ? `.${modifier}` : ""}`);
  })

  // ___________________________________
  // Export Latin typography tokens
  const latinTypographyTokens = filterByTokenNamePatterns(
    flattenedJsonInputs,
    latinTypographyGroups,
    true
  );

  latinTypographyTokens.forEach((input) => {
    const formatted = convertToNestedObject(input.data, input.namespace);
    let modifier = ""

    if (input.namespace == 'typography') {
      modifier = 'latin'
    }

    outputTokensToFile(formatted, `${input.namespace}${modifier ? `.${modifier}` : ""}`);
  })

  // ___________________________________
  // Export Tall (Vietnamese) typography tokens
  const tallTypographyTokens = filterByTokenNamePatterns(
    flattenedJsonInputs,
    tallTypographyGroups,
    true
  );

  tallTypographyTokens.forEach((input) => {
    const formatted = convertToNestedObject(input.data, input.namespace);
    let modifier = ""

    if (input.namespace == 'typography') {
      modifier = 'tall'
    }

    outputTokensToFile(formatted, `${input.namespace}${modifier ? `.${modifier}` : ""}`);
  })

  // ___________________________________
  // Export elevation style tokens
  const elevationTokens = filterByTokenNamePatterns(
    flattenedJsonInputs,
    elevationGroups,
    true
  );

  const elevationRootTokens = filterByTokenNamePatterns(elevationTokens, elevationRootExclude, false);

  elevationTokens.forEach((input) => {
    const formatted = convertToNestedObject(input.data, input.namespace);
    let modifier = ""

    outputTokensToFile(formatted, `${Constants.V2_ELEVATION_FILENAME}${modifier ? `.${modifier}` : ""}`);
  })

  elevationRootTokens.forEach((input) => {
    const formatted = convertToNestedObject(input.data, input.namespace);
    let modifier = "root"

    outputTokensToFile(formatted, `${Constants.V2_ELEVATION_FILENAME}${modifier ? `.${modifier}` : ""}`);
  })

  // ___________________________________
  // Export all tokens without language (core tokens)
  const allTokensWithoutLanguage = filterByTokenNamePatterns(
    flattenedJsonInputs,
    [...allLanguageGroups, ...elevationGroups],
    false
  );


  allTokensWithoutLanguage.forEach((input) => {
    const formatted = convertToNestedObject(input.data, input.namespace);
    let modifier = ""

    if (input.namespace == 'typography') {
      modifier = 'core'
    }

    outputTokensToFile(formatted, `${input.namespace}${modifier ? `.${modifier}` : ""}`);
  });

  setTimeout(() => removeTrailingZeroesFromJsonDir(Constants.V2_TOKEN_FORMATTED_OUTPUT_DIR), 500)
}

init();