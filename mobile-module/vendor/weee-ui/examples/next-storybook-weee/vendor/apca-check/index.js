// From StackExchange's team
// https://github.com/StackExchange/apca-check

const apcaW3 = require("apca-w3");
const calcAPCA = apcaW3.calcAPCA;
const APCABronzeConformanceThresholdFn = require("./apca-bronze.js");
const APCASilverPlusConformanceThresholdFn = require("./apca-silver-plus.js");
const axe = require("axe-core");

const generateColorContrastAPCAConformanceCheck = (
  conformanceLevel,
  conformanceThresholdFn
) => {
  return {
    id: `color-contrast-apca-${conformanceLevel}-conformance`,
    metadata: {
      impact: "serious",
      messages: {
        pass:
          "Element has sufficient APCA " +
          conformanceLevel +
          "level lightness contrast (Lc) of ${this.data.apcaContrast}Lc (foreground color: ${this.data.fgColor}, background color: ${this.data.bgColor}, font size: ${this.data.fontSize}, font weight: ${this.data.fontWeight}). Expected minimum APCA contrast of ${this.data.apcaThreshold}}",
        fail: {
          default:
            "Element has insufficient APCA " +
            conformanceLevel +
            "level contrast of ${this.data.apcaContrast}Lc (foreground color: ${this.data.fgColor}, background color: ${this.data.bgColor}, font size: ${this.data.fontSize}, font weight: ${this.data.fontWeight}). Expected minimum APCA lightness contrast of ${this.data.apcaThreshold}Lc",
          increaseFont:
            "Element has insufficient APCA " +
            conformanceLevel +
            " level contrast of ${this.data.apcaContrast}Lc (foreground color: ${this.data.fgColor}, background color: ${this.data.bgColor}, font size: ${this.data.fontSize}, font weight: ${this.data.fontWeight}). Increase font size and/or font weight to meet APCA conformance minimums",
        },
        incomplete: "Unable to determine APCA lightness contrast (Lc)",
      },
    },
    evaluate(node, options, virtualNode) {      
      const nodeStyle = window.getComputedStyle(node);
      const fontSize = nodeStyle.getPropertyValue("font-size");
      const fontWeight = nodeStyle.getPropertyValue("font-weight");
      const bgColor = axe.commons.color.getBackgroundColor(node);
      const fgColor = axe.commons.color.getForegroundColor(
        node,
        false,
        bgColor
      );
      // missing data to determine APCA contrast for this node
      if (!bgColor || !fgColor || !fontSize || !fontWeight) {
        return undefined;
      }
      const toRGBA = (color) => {
        return `rgba(${color.red}, ${color.green}, ${color.blue}, ${color.alpha})`;
      };
      const apcaContrast = Math.abs(calcAPCA(toRGBA(fgColor), toRGBA(bgColor)));
      const apcaThreshold = conformanceThresholdFn(fontSize, fontWeight);
      this.data = {
        fgColor: fgColor.toHexString(),
        bgColor: bgColor.toHexString(),
        fontSize: `${((parseFloat(fontSize) * 72) / 96).toFixed(1)}pt (${parseFloat(fontSize)}px)`,
        fontWeight: fontWeight,
        apcaContrast: Math.round(apcaContrast * 100) / 100,
        apcaThreshold: apcaThreshold,
        messageKey: apcaThreshold === null ? "increaseFont" : "default",
      };

      return apcaThreshold ? apcaContrast >= apcaThreshold : void 0;
    },
  };
};

const generateColorContrastAPCARule = (conformanceLevel) => ({
  id: `color-contrast-apca-${conformanceLevel}`,
  impact: "serious",
  matches: "color-contrast-matches",
  metadata: {
    help: "Elements must meet APCA conformance minimums thresholds",
    description: `Ensures the contrast between foreground and background colors meets APCA ${conformanceLevel} level conformance minimums thresholds`,
    helpUrl:
      "https://readtech.org/ARC/tests/visual-readability-contrast/?tn=criterion",
  },
  all: [`color-contrast-apca-${conformanceLevel}-conformance`],
  tags: ["apca", "wcag3", `apca-${conformanceLevel}`],
});

const registerAPCACheck = (conformanceLevel, customConformanceThresholdFn) => {
  if (
    conformanceLevel === "custom" &&
    typeof customConformanceThresholdFn !== "function"
  ) {
    throw new Error(
      "A custom conformance level requires a custom conformance threshold function"
    );
  }
  const conformanceThresholdFnMap = {
    bronze: APCABronzeConformanceThresholdFn,
    silver: APCASilverPlusConformanceThresholdFn,
    custom: customConformanceThresholdFn,
  };
  return {
    rule: generateColorContrastAPCARule(conformanceLevel),
    check: generateColorContrastAPCAConformanceCheck(
      conformanceLevel,
      conformanceThresholdFnMap[conformanceLevel]
    ),
  };
};

module.exports = { registerAPCACheck };
