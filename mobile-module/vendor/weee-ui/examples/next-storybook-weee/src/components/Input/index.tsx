import React from "react";
import { twMerge } from "tailwind-merge";

interface InputProps {
  /**
   * Input placeholder
   */
  placeholder: string;
  /**
   * Input state
   */
  state?: "idle" | "disabled" | "error";
  /**
   * Input value
   */
  value?: string;
  /**
   * Input size
   */
  size?: "default" | "large";
  /**
   * Input style
   */
  style?: "flat" | "outlined";
  /**
   * Show required indicator
   */
  required?: boolean;
}

export function Input({
  placeholder = "Enter placeholder",
  state = "idle",
  size = "default",
  style = "flat",
  value,
  required,
  ...props
}: InputProps) {
  let inputBaseStyles = `px-200 h-[40px] bg-input-100-bg-default text-input-100-fg-default border border-input-100-hairline-default rounded-300`;
  let inputStateStyles = {
    idle: ``,
    disabled: ``,
    error: `bg-input-100-bg-critical text-input-100-fg-critical border border-input-100-hairline-critical rounded-300`,
  };

  return (
    <input
      value={value ? value : ""}
      placeholder={placeholder}
      className={twMerge(inputBaseStyles, inputStateStyles[state])}
      required={required}
      {...props}
    />
  );
}
