import type { <PERSON>a, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import { Button } from './index';

const meta = {
  title: 'Button',
  component: Button,
  parameters: {
    layout: 'centered',
  },
  // tags: ['autodocs'],
  args: { onClick: fn() },
} satisfies Meta<typeof Button>;

export default meta;
type Story = StoryObj<typeof meta>;

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Primary: Story = {
  args: {
    style: 'primary',
    size: 'medium',
    label: 'Shop now',
  },
};

export const Secondary: Story = {
  args: {
    style: 'secondary',
    size: 'medium',
    label: 'Shop now',
  },
};

export const Tertiary: Story = {
  args: {
    style: 'tertiary',
    size: 'medium',
    label: 'Shop now',
  },
};

export const Confirmation: Story = {
  args: {
    style: 'confirmation',
    size: 'medium',
    label: 'Shop now',
  },
};

export const Surface: Story = {
  args: {
    style: 'surface',
    size: 'medium',
    label: 'Shop now',
  },
};

export const Interactive: Story = {
  args: {
    style: 'interactive',
    size: 'medium',
    label: 'Shop now',
  },
};

export const Disabled: Story = {
  args: {
    style: 'disabled',
    size: 'medium',
    label: 'Shop now',
  },
};

export const OverlayWhite: Story = {
  args: {
    style: 'overlayWhite',
    size: 'medium',
    label: 'Shop now',
  },
  parameters: {
    backgrounds: { default: 'dark' },
  },
};

export const Overlay: Story = {
  args: {
    style: 'overlay',
    size: 'medium',
    label: 'Shop now',
  },
};