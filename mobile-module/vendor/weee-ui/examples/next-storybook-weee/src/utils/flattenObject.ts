interface NestedObject {
  [key: string]: any;
}

export function flattenObject(
  obj: NestedObject,
  parentKey: string = '',
  result: NestedObject = {}
): NestedObject {
  for (let key in obj) {
    if (obj.hasOwnProperty(key)) {
      let newKey = parentKey ? `${parentKey}-${key}` : key;

      if (typeof obj[key] === 'object' && obj[key] !== null) {
        flattenObject(obj[key], newKey, result);
      } else {
        result[newKey] = obj[key];
      }
    }
  }
  return result;
}