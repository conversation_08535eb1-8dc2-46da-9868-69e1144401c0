export interface FlatObject {
  [key: string]: string;
}

export function sortFlatObject(flatObject: FlatObject, sortObj: string[]): FlatObject {
  const sorted: FlatObject = {};
  const remaining: FlatObject = {};

  const keys = Object.keys(flatObject);

  // Collect matched keys in sorted order
  sortObj.forEach((pattern) => {
    keys.forEach((key) => {
      if (key.includes(pattern)) {
        sorted[key] = flatObject[key];
      }
    });
  });

  // Collect remaining keys
  keys.forEach((key) => {
    if (!Object.hasOwnProperty.call(sorted, key)) {
      remaining[key] = flatObject[key];
    }
  });

  // Merge sorted and remaining keys
  return { ...sorted, ...remaining };
}