import { flattenObject } from "@/utils/flattenObject";
import { sortFlatObject } from "@/utils/sortFlatObject";
import { filterKeys } from "@/utils/filterKeys";
import { useEffect, useState } from "react";
import { filterOnlyMatchingKeys } from "@/utils/filterOnlyMatchingKeys";
import CodeSnippet from "./CodeSnippet";
import { removeMatchingKeys } from "@/utils/removeMatchingKeys";

interface NestedObject {
  [key: string]: any;
}

export default function FontTrackingTable({ twConfig }: NestedObject) {
  const [listFilter, setListFilter] = useState("");

  const sizingConfigFlat = flattenObject(twConfig.theme.letterSpacing);
  const cleanedTrackingConfig = removeMatchingKeys(sizingConfigFlat, [
    "normal",
  ]);
  const sortedTrackingConfig = sortFlatObject(cleanedTrackingConfig, [
    "tightest",
    "tighter",
    "tight",
    "base",
    "wide",
    "wider",
    "widest",
  ]);

  const filteredKeys = filterKeys(sortedTrackingConfig, listFilter);

  return (
    <div className={`z-1`}>
      <div
        className={`flex items-center gap-400 border-b border-surface-100-hairline mb-200 pb-200`}
      >
        <div className={`flex-[.75]`}>
          <span className={`enki-body-sm-strong`}>Tailwind class</span>
        </div>
        <div className={`flex-[.75]`}>
          <span className={`enki-body-sm-strong`}>SCSS variable</span>
        </div>
        <div className={`flex-[.75]`}>
          <span className={`enki-body-sm-strong`}>Properties</span>
        </div>
        <div className={`flex-1 flex justify-center`}>
          <span className={`enki-body-sm-strong`}>Sample</span>
        </div>
      </div>

      {Object.keys(filteredKeys).map((trackingSize: string, idx: number) => {
        let value = filteredKeys[trackingSize];

        return (
          <div
            className={`flex items-center gap-400 border-b border-surface-100-hairline mb-200 pb-200 text-sm`}
            key={idx}
          >
            <div className={`flex-[.75] flex gap-400 items-center`}>
              <CodeSnippet value={`.tracking-${trackingSize}`} style={1} />
            </div>
            <div className={`flex-[.75] flex gap-400 items-center`}>
              <CodeSnippet value={`$font-tracking-${trackingSize}`} style={3} />
            </div>
            <div className={`flex-[.75] flex justify-between`}>
              <CodeSnippet value={value} style={2} />
            </div>
            <div className={`flex-1 flex justify-center`}>
              <FontWeightSwatch value={value} />
            </div>
          </div>
        );
      })}
    </div>
  );
}

function FontWeightSwatch({ value }: { value: string }) {
  return (
    <div className={`flex flex-1 justify-center items-center overflow-hidden`}>
      <div
        style={{
          letterSpacing: value,
        }}
        className={`text-surface-100-fg-minor hover:text-surface-100-fg-default text-base`}
      >
        Lorem ipsum solor sit dit imet.
      </div>
    </div>
  );
}
