import { flattenObject } from "@/utils/flattenObject";
import { sortFlatObject } from "@/utils/sortFlatObject";
import { filterKeys } from "@/utils/filterKeys";
import { useEffect, useState } from "react";
import { filterOnlyMatchingKeys } from "@/utils/filterOnlyMatchingKeys";
import { twMerge } from 'tailwind-merge'
import CodeSnippet from "./CodeSnippet";

interface NestedObject {
  [key: string]: any;
}

export default function FiltersTable({ cssFiltersTwConfig }: NestedObject) {
  const [listFilter, setListFilter] = useState("");

  return (
    <div className={`z-1`}>
      <div
        className={`flex items-center gap-400 border-b border-surface-100-hairline mb-200 pb-200`}
      >
        <div className={`flex-[.75]`}>
          <span className={`enki-body-sm-strong`}>Tailwind class</span>
        </div>
        <div className={`flex-[.75]`}>
          <span className={`enki-body-sm-strong`}>SCSS variable</span>
        </div>
        <div className={`flex-[.75]`}>
          <span className={`enki-body-sm-strong`}>Properties</span>
        </div>
        <div className={`flex-[1] flex justify-center`}>
          <span className={`enki-body-sm-strong`}>Sample</span>
        </div>
      </div>

      {Object.keys(cssFiltersTwConfig).map(
        (filterClass: string, idx: number) => {
          return (
            <div
              className={`flex items-center gap-400 border-b border-surface-100-hairline mb-200 pb-200 text-sm`}
              key={idx}
            >
              <div className={`flex-[.75] flex gap-400 items-center`}>
                <CodeSnippet value={`${filterClass}`} style={1} />
              </div>
              <div className={`flex-[.75] flex gap-400 items-center`}>
                <CodeSnippet
                  value={`${filterClass.replace(".enki", "$style")}`}
                  style={3}
                />
              </div>
              <div className={`flex-[.75] flex`}>
                <CodeSnippet
                  value={cssFiltersTwConfig[filterClass].filter}
                  style={2}
                />
              </div>
              <div className={`flex-[1] flex justify-center`}>
                <FilterSwatch value={cssFiltersTwConfig[filterClass].filter} />
              </div>
            </div>
          );
        }
      )}
    </div>
  );
}

function FilterSwatchItem({ value, swatchStyle }: { value: string, swatchStyle: string }) {
  const defaultFilter = ``;
  const [currentStyle, setCurrentStyle] = useState(defaultFilter);

  return (
    <div
        style={{
          filter: currentStyle,
        }}
        onMouseEnter={() => {
          setCurrentStyle(value);
        }}
        onMouseLeave={() => {
          setCurrentStyle(defaultFilter);
        }}
        className={twMerge(swatchStyle, `w-1000 h-1000 rounded-200 my-600 cursor-pointer flex justify-center items-center enki-body-base-medium`)}>
          Aa
      </div>  
  )
}

function FilterSwatch({ value }: { value: string }) {

  return (
    <div
      className={`flex gap-200 flex-1 justify-center items-center overflow-visible`}
    >
      <FilterSwatchItem value={value} swatchStyle={'enki-button-secondary'} />
      <FilterSwatchItem value={value} swatchStyle={'enki-button-tertiary'} />
      <FilterSwatchItem value={value} swatchStyle={'enki-button-confirmation'} />
      <FilterSwatchItem value={value} swatchStyle={'bg-root-tomato-red-base-4 text-reserved-true-white'} />
      <FilterSwatchItem value={value} swatchStyle={'border-2 border-surface-100-hairline text-surface-100-fg-default'} />
    </div>
  );
}
