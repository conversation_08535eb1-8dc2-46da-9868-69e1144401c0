import { flattenObject } from "@/utils/flattenObject";
import { sortFlatObject } from "@/utils/sortFlatObject";
import { filterKeys } from "@/utils/filterKeys";
import { useEffect, useState } from "react";
import CodeSnippet from "./CodeSnippet";

interface NestedObject {
  [key: string]: any;
}

export default function ColorTable({ twConfig }: NestedObject) {
  const [listFilter, setListFilter] = useState("");

  const colorConfigFlat = flattenObject(twConfig.theme.colors);
  const sortedColorConfig = sortFlatObject(colorConfigFlat, [
    "link",
    "surface",
    "btn",
    "pricing",
    "highlight",
    "warning",
    "critical",
    "success",
    "backdrop",
    "input",
    "atc",
    "navbar",
    "sidebar",
    "bookmark",
    "notification",
    "tint",
    "",
  ]);

  const filteredKeys = filterKeys(sortedColorConfig, listFilter);

  return (
    <div className={`z-1`}>
      <div className={`my-400`}>
        <input
          placeholder="Search for a color token"
          className={`bg-input-200-bg-default w-[50%] p-200 rounded-200 enki-body-sm-medium border-none outline-none outline-offset-0 hover:outline-input-200-hairline-active hover:bg-input-200-bg-active focus:outline-offset-0 focus:outline-input-200-hairline-active focus-visible:outline-offset-0 focus-visible:outline-none focus-visible:outline-input-200-hairline-active focus-visible:bg-input-200-bg-active`}
          value={listFilter}
          onChange={(e) => setListFilter(e.target.value)}
        ></input>
      </div>
      <div
        className={`flex items-center gap-400 border-b border-surface-100-hairline mb-200 pb-200`}
      >
        <div className={`flex-[.75]`}>
          <span className={`enki-body-sm-strong`}>Tailwind class</span>
        </div>
        <div className={`flex-[.75]`}>
          <span className={`enki-body-sm-strong`}>SCSS variable</span>
        </div>
        <div className={`flex-[.75]`}>
          <span className={`enki-body-sm-strong`}>Properties</span>
        </div>
        <div className={`flex-1 flex justify-center`}>
          <span className={`enki-body-sm-strong`}>Sample</span>
        </div>
      </div>

      {Object.keys(filteredKeys).map((color: string, idx: number) => {
        let value = filteredKeys[color];

        return (
          <div
            className={`flex items-center gap-400 border-b border-surface-100-hairline mb-200 pb-200 text-sm`}
            key={idx}
          >
            <div className={`flex-[.75] flex gap-400 items-center`}>
              <CodeSnippet value={`.*-${color}`} style={1} />
            </div>
            <div className={`flex-[.75] flex gap-400 items-center`}>
              <CodeSnippet value={`$color-${color}`} style={3} />
            </div>
            <div className={`flex-[.75] flex justify-between`}>
              <CodeSnippet value={value} style={2} />
            </div>
            <div className={`flex-1 flex justify-center`}>
              <ColorSwatch value={value} />
            </div>
          </div>
        );
      })}
    </div>
  );
}

function ColorSwatch({ value }: { value: string }) {
  return (
    <div
      className={`relative overflow-hidden border border-surface-100-hairline rounded-300`}
    >
      <div
        style={{
          backgroundColor: value,
        }}
        className={`absolute left-0 top-0 w-full h-full`}
      />
      <div
        style={{
          background: `repeating-conic-gradient(var(--color-surface-300-bg) 0% 25%, transparent 0% 50%) 50% / 20px 20px`,
        }}
        className={`w-2000 h-700`}
      />
    </div>
  );
}
