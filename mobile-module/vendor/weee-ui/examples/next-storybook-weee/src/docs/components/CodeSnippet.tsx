import { flattenObject } from "@/utils/flattenObject";
import { sortFlatObject } from "@/utils/sortFlatObject";
import { filterKeys } from "@/utils/filterKeys";
import { useEffect, useState } from "react";

interface CodeSnippet {
  value: string;
  style?: number;
  modifiers?: string;
}

export default function CodeSnippet({
  value,
  style = 1,
  modifiers = "",
}: CodeSnippet) {

  const snippetStylesBase = `cursor-pointer appearance-none font-mono text-2xs py-100 px-200 rounded-200`;

  let snippetStyleDefault = `bg-surface-200-bg text-highlight-txt font-bold`;
  let snippetStyle = snippetStyleDefault;

  function copyValue(value: string) {
    navigator.clipboard.writeText(value);

    const notifier = document.createElement("div");
    notifier.innerHTML = "Copied to clipboard"
    notifier.setAttribute("style", "z-index: 9999; position: fixed; left: 50%; bottom: 40px; transform: translateX(-50%); padding: 10px; border-radius: 6px; background-color: #111; box-shadow: 0px 0px 0px 1px #000; color: #fff; font-size: 14px; font-weight: 600;")
    
    document.body.appendChild(notifier);
    setTimeout(() => { document.body.removeChild(notifier)}, 2000);
  }

  switch (style) {
    case 1:
      snippetStyle = `bg-surface-200-bg text-highlight-txt font-bold`;
      break;

    case 2:
      snippetStyle = `bg-surface-100-bg text-extended-dragonfruit-pink-base-6 border-2 border-surface-100-hairline`;
      break;

    case 3:
      snippetStyle = `bg-root-dragonfruit-pink-light-3 text-root-dragonfruit-pink-base-7 font-bold`;
      break;

    case 4:
      snippetStyle = `bg-root-jade-green-light-2 text-root-jade-green-dark-2 font-bold`;
      break;

    default:
      snippetStyle = snippetStyleDefault;
  }

  if (style)
    return (
      <button
        onClick={() => copyValue(value)}
        type="button"
        className={`${snippetStylesBase} ${snippetStyle} ${modifiers}`}
      >
        {value}
      </button>
    );
}
