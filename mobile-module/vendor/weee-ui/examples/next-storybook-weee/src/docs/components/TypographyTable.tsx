import { flattenObject } from "@/utils/flattenObject";
import { sortFlatObject } from "@/utils/sortFlatObject";
import { filterKeys } from "@/utils/filterKeys";
import { useEffect, useState } from "react";
import { filterOnlyMatchingKeys } from "@/utils/filterOnlyMatchingKeys";
import CodeSnippet from "./CodeSnippet";

interface NestedObject {
  [key: string]: any;
}

export default function TypographyTable({ fontTwConfig }: NestedObject) {
  const [listFilter, setListFilter] = useState("");

  const filteredKeys = filterKeys(fontTwConfig, listFilter);

  return (
    <div className={`z-1`}>
      <div className={`my-400`}>
        <input
          placeholder="Search for a typography token"
          className={`bg-input-200-bg-default w-[50%] p-200 rounded-200 enki-body-sm-medium border-none outline-none outline-offset-0 hover:outline-input-200-hairline-active hover:bg-input-200-bg-active focus:outline-offset-0 focus:outline-input-200-hairline-active focus-visible:outline-offset-0 focus-visible:outline-none focus-visible:outline-input-200-hairline-active focus-visible:bg-input-200-bg-active`}
          value={listFilter}
          onChange={(e) => setListFilter(e.target.value)}
        ></input>
      </div>
      <div
        className={`flex items-center gap-400 border-b border-surface-100-hairline mb-200 pb-200`}
      >
        <div className={`flex-[.5]`}>
          <span className={`enki-body-sm-strong`}>Tailwind class</span>
        </div>
        <div className={`flex-1`}>
          <span className={`enki-body-sm-strong`}>Properties</span>
        </div>
        <div className={`flex-[.75] flex justify-start`}>
          <span className={`enki-body-sm-strong`}>Sample</span>
        </div>
      </div>

      {Object.keys(filteredKeys).map((font: string, idx: number) => {
        return (
          <div
            className={`flex items-center gap-400 border-b border-surface-100-hairline mb-200 pb-200 text-sm`}
            key={idx}
          >
            <div className={`flex-[.5] flex gap-400 items-center`}>
              <CodeSnippet value={`${font}`} style={1} />
            </div>
            <div className={`flex-1`}>
              <PropertiesList values={filteredKeys[font]} />
            </div>
            <div className={`flex-[.75] flex justify-center`}>
              <TypographySwatch value={font} />
            </div>
          </div>
        );
      })}
    </div>
  );
}

function getPropertyValue(property: string) {
  let cleanedValue = property.replace("var(", "").replace(")", "");

  let style = getComputedStyle(document.body)

  return style.getPropertyValue(cleanedValue)
}


function ComputedCodeSnippet({ value }: { value: string }) {
  const [computedValue, setComputedValue] = useState("0px")

  useEffect(() => {
    let res = getPropertyValue(value);

    if (res && res.length > 0) {
      setComputedValue(res);
    }

  }, [value])

  return (
    <CodeSnippet value={computedValue} style={4} />
  )

}

function PropertiesList({ values }: { values: any }) {
  return (
    <div className={`flex flex-col gap-100`}>
      {Object.keys(values).map((property, idx) => {
        return (
          <div key={idx}>
            <div className={`flex gap-200 items-center justify-start`}>
              <span
                className={`font-mono text-xs w-[150px] text-link-base-1`}
              >
                {property}:
              </span>
              <CodeSnippet value={values[property]} style={2} />
              <ComputedCodeSnippet value={values[property]} />
            </div>
          </div>
        );
      })}
    </div>
  );
}

function TypographySwatch({ value }: { value: string }) {
  return (
    <div className={`flex flex-1 justify-start items-center overflow-hidden`}>
      <div className={`${value.replace(".", "")} text-surface-100-fg-minor hover:text-surface-100-fg-default`}>Taste life to the fullest</div>
    </div>
  );
}
