import { flattenObject } from "@/utils/flattenObject";
import { sortFlatObject } from "@/utils/sortFlatObject";
import { filterKeys } from "@/utils/filterKeys";
import { useEffect, useState } from "react";
import { filterOnlyMatchingKeys } from "@/utils/filterOnlyMatchingKeys";
import CodeSnippet from "./CodeSnippet";
import { removeMatchingKeys } from "@/utils/removeMatchingKeys";
import { reverseObjectKeys } from "@/utils/reverseObjectKeys";

interface NestedObject {
  [key: string]: any;
}

export default function FontLineHeightTable({ twConfig }: NestedObject) {
  const [listFilter, setListFilter] = useState("");

  const config = flattenObject(twConfig.theme.lineHeight);
  const cleanedConfig = removeMatchingKeys(config, [
    "10",
    "9",
    "8",
    "7",
    "6",
    "5",
    "4",
    "3",
    "2",
    "none",
    "tight",
    "snug",
    "normal",
    "relaxed",
    "loose"
  ]);
  const sortedConfig = sortFlatObject(cleanedConfig, [""]);
  const filteredKeys = filterKeys(sortedConfig, listFilter);

  return (
    <div className={`z-1`}>
      <div
        className={`flex items-center gap-400 border-b border-surface-100-hairline mb-200 pb-200`}
      >
        <div className={`flex-[.75]`}>
          <span className={`enki-body-sm-strong`}>Tailwind class</span>
        </div>
        <div className={`flex-[.75]`}>
          <span className={`enki-body-sm-strong`}>SCSS variable</span>
        </div>
        <div className={`flex-[.75]`}>
          <span className={`enki-body-sm-strong`}>Properties</span>
        </div>
        <div className={`flex-1 flex justify-start`}>
          <span className={`enki-body-sm-strong`}>Sample</span>
        </div>
      </div>

      {Object.keys(filteredKeys).reverse().map((size: string, idx: number) => {
        let value = filteredKeys[size];

        return (
          <div
            className={`flex items-center gap-400 border-b border-surface-100-hairline mb-200 pb-200 text-sm`}
            key={idx}
          >
            <div className={`flex-[.75] flex gap-400 items-center`}>
              <CodeSnippet value={`.leading-${size}`} style={1} />
            </div>
            <div className={`flex-[.75] flex gap-400 items-center`}>
              <CodeSnippet value={`$font-lineheight-${size}`} style={3} />
            </div>
            <div className={`flex-[.75] flex items-center`}>
              <CodeSnippet value={value} style={2} />
            </div>
            <div className={`flex-1 flex justify-start`}>
              <FontWeightSwatch value={value} />
            </div>
          </div>
        );
      })}
    </div>
  );
}

function FontWeightSwatch({ value }: { value: string }) {
  return (
    <div className={`flex flex-1 justify-center items-center overflow-visible my-400`}>
      <div
        style={{
          lineHeight: value,
        }}
        className={`text-surface-100-fg-minor hover:text-surface-100-fg-default text-sm`}
      >
        Lorem ipsum dolor sit amet, mazim reprimique est no, veri partiendo ex eos. Eum quodsi audiam repudiare et. Et magna simul impetus usu, duo ea aeterno postulant temporibus. Te mea recteque euripidis intellegam, an diam possit efficiantur sit. Quo quis fabellas disputationi id, nam at saepe tollit possim. Te augue aliquam quo.
      </div>
    </div>
  );
}
