import { flattenObject } from "@/utils/flattenObject";
import { sortFlatObject } from "@/utils/sortFlatObject";
import { filterKeys } from "@/utils/filterKeys";
import { useEffect, useState } from "react";
import { filterOnlyMatchingKeys } from "@/utils/filterOnlyMatchingKeys";
import CodeSnippet from "./CodeSnippet";

interface NestedObject {
  [key: string]: any;
}

export default function ButtonsTable({ buttonsTwConfig }: NestedObject) {
  const [listFilter, setListFilter] = useState("");

  const filteredKeys = filterKeys(buttonsTwConfig, listFilter);

  return (
    <div className={`z-1`}>
      <div className={`my-400`}>
        <div
          className={`bg-highlight-bg text-highlight-fg p-500 rounded-300 max-w-[850px] enki-body-sm enki-elevation-3`}
        >
          <div className={`enki-body-sm-strong`}>
            Enki Tailwind button classes only apply background, color, and
            interactive states.
          </div>
          <div>
            Additional button treatment should be handled within button
            components to leverage Tailwind bundling classes.
          </div>
        </div>
      </div>
      <div
        className={`flex items-center gap-400 border-b border-surface-100-hairline mb-200 pb-200`}
      >
        <div className={`flex-[.5]`}>
          <span className={`enki-body-sm-strong`}>Tailwind class</span>
        </div>
        <div className={`flex-1`}>
          <span className={`enki-body-sm-strong`}>Properties</span>
        </div>
        <div className={`flex-[.75] flex justify-center`}>
          <span className={`enki-body-sm-strong`}>Sample</span>
        </div>
      </div>

      {Object.keys(filteredKeys).map((font: string, idx: number) => {
        return (
          <div
            className={`flex items-center gap-400 border-b border-surface-100-hairline mb-200 pb-200 text-sm`}
            key={idx}
          >
            <div className={`flex-[.5] flex gap-400 items-center`}>
              <CodeSnippet value={`${font}`} style={1} />
            </div>
            <div className={`flex-1`}>
              <PropertiesList values={filteredKeys[font]} />
            </div>
            <div className={`flex-[.75] flex justify-center`}>
              <ButtonSwatch value={font} />
            </div>
          </div>
        );
      })}
    </div>
  );
}

function PropertiesList({ values }: { values: any }) {
  return (
    <div className={`flex flex-col gap-100`}>
      {Object.keys(values).map((property, idx) => {
        if (property.includes("&:active")) {
          if (!values[property]["filter"]) return;

          let filterProp = values[property]["filter"];

          return (
            <div key={idx}>
              <div
                className={`font-mono text-xs text-surface-100-fg-minor pt-100`}
              >
                {`&:active {`}
              </div>

              <div className={`flex items-center justify-start pl-600`}>
                <span
                  className={`font-mono text-xs w-[125px] text-link-base-1`}
                >
                  filter:
                </span>
                <CodeSnippet value={filterProp} style={2} />
              </div>

              <div
                className={`font-mono text-xs text-surface-100-fg-minor pt-100`}
              >
                {`}`}
              </div>
            </div>
          );
        } else if (property.includes("@media")) {
          if (!values[property]["&:hover"]) return;
          if (!values[property]["&:hover"]["filter"]) return;

          let filterProp = values[property]["&:hover"]["filter"];

          return (
            <div key={idx}>
              <div
                className={`font-mono text-xs text-surface-100-fg-minor pt-100`}
              >
                {`@media(hover:hover) {`}
              </div>

              <div
                className={`font-mono text-xs text-surface-100-fg-minor pt-100 pl-400`}
              >
                {`&:hover {`}
              </div>

              <div className={`flex items-center justify-start pl-600`}>
                <span
                  className={`font-mono text-xs w-[125px] text-link-base-1`}
                >
                  filter:
                </span>
                <CodeSnippet value={filterProp} style={2} />
              </div>

              <div
                className={`font-mono text-xs text-surface-100-fg-minor pt-100 pl-400`}
              >
                {`}`}
              </div>

              <div
                className={`font-mono text-xs text-surface-100-fg-minor pt-100`}
              >{`}`}</div>
            </div>
          );
        } else {
          return (
            <div key={idx}>
              <div className={`flex items-center justify-start`}>
                <span
                  className={`font-mono text-xs w-[150px] text-link-base-1`}
                >
                  {property}:
                </span>
                <CodeSnippet value={values[property]} style={2} />
              </div>
            </div>
          );
        }
      })}
    </div>
  );
}

function ButtonSwatch({ value }: { value: string }) {
  let buttonBaseStyles = `rounded-full py-300 px-600 enki-body-lg-medium`;

  return (
    <div className={`flex flex-1 justify-center items-center overflow-hidden`}>
      <div className={`${buttonBaseStyles} ${value.replace(".", "")}`}>
        Shop now
      </div>
    </div>
  );
}
