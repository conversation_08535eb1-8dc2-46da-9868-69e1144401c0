import { create } from '@storybook/theming/create';
import enkiLogo from "../public/enki-logo.svg"
 
export default create({
  base: 'light',
  // Typography
  fontBase: '"Poppins", sans-serif',
  fontCode: 'monospace',
 
  brandTitle: 'Enki Design System',
  brandUrl: '/',
  brandImage: (process.env.NODE_ENV === 'production') ? enkiLogo : '/enki-logo.svg',
  brandTarget: '_self',
 
  //
  colorPrimary: 'rgba(0,178,234,1)',
  colorSecondary: 'rgba(0,27,165,1)',
 
  // UI
  appBg: 'rgba(255,255,255,1)',
  appContentBg: 'rgba(255,255,255,1)',
  appPreviewBg: 'rgba(238,242,251,1)',
  appBorderColor: 'rgba(228,234,245,1)',
  appBorderRadius: 4,
 
  // Text colors
  textColor: 'rgba(7,16,26,1)',
  textInverseColor: 'rgba(255,255,255,1)',
 
  // Toolbar default and active colors
  barTextColor: 'rgba(7,16,26,1)',
  barSelectedColor: 'rgba(0,27,165,1)',
  barHoverColor: 'rgba(7,16,26,1)',
  barBg: 'rgba(255,255,255,1)',
 
  // Form colors
  inputBg: 'rgba(255,255,255,1)',
  inputBorder: 'rgba(228,234,245,1)',
  inputTextColor: 'rgba(7,16,26,1)',
  inputBorderRadius: 8,
});