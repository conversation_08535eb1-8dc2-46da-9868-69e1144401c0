{"name": "storybook", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "storybook": "storybook dev -p 6006 --ci", "build-storybook": "storybook build"}, "dependencies": {"@storybook/addon-backgrounds": "^8.1.9", "@storybook/addon-console": "^3.0.0", "apca-w3": "^0.1.9", "next": "14.2.4", "react": "^18", "react-dom": "^18", "tailwind-merge": "^2.3.0"}, "devDependencies": {"@chromatic-com/storybook": "^1.5.0", "@storybook/addon-a11y": "^8.1.10", "@storybook/addon-docs": "^8.2.6", "@storybook/addon-essentials": "^8.1.9", "@storybook/addon-interactions": "^8.1.9", "@storybook/addon-links": "^8.1.9", "@storybook/addon-onboarding": "^8.1.9", "@storybook/addon-styling-webpack": "^1.0.0", "@storybook/blocks": "^8.1.9", "@storybook/manager-api": "^8.2.6", "@storybook/nextjs": "^8.1.9", "@storybook/react": "^8.1.9", "@storybook/test": "^8.1.9", "@storybook/theming": "^8.2.6", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "apca-check": "^0.1.0", "autoprefixer": "^10.4.19", "eslint": "^8", "eslint-config-next": "14.2.3", "eslint-plugin-storybook": "^0.8.0", "postcss": "^8.4.38", "sass": "^1.77.5", "storybook": "^8.1.9", "tailwindcss": "^3.4.4", "typescript": "^5"}}