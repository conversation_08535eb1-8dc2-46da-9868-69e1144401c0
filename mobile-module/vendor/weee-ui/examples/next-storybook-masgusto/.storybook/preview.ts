import type { Preview } from "@storybook/react";
import '../src/assets/styles/globals.scss'
import '../public/main.latin.min.css'
import { registerAPCACheck } from '../vendor/apca-check/index.js';

const preview: Preview = {
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    backgrounds: {
      default: 'light',
      values: [
        { name: 'light', value: 'rgba(255,255,255,1.00)' },
        { name: 'dark', value: 'rgba(33,47,61,1.00)' },
      ],
    },
    a11y: {
      config: {
        checks: [
          { ... registerAPCACheck('silver-plus').check },
        ],
        rules: [
          { ...registerAPCACheck('silver-plus').rule },
          { id: "color-contrast", enabled: false },
        ],
      },
    },
  },
};

export default preview;
