# Enki Storybook

This is a [Next.js](https://nextjs.org/) project with [Storybook](https://storybook.js.org/) and [Tailwind](https://tailwindcss.com/) integrated into it.

## Local development

First, run the development server:

```bash
npm run storybook
```

Open [http://localhost:6006](http://localhost:6006) with your browser to open Storybook.

## Learn more about testing Enki components on your project

[Read more on the wiki](https://github.com/sayweee/weee-ui/wiki/Submodule-Workflow)