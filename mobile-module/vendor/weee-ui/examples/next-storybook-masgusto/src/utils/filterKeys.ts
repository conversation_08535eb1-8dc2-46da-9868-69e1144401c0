import { FlatObject } from "./sortFlatObject";

type Obj = FlatObject;

export function filterKeys(obj: Obj, substring: string): Obj {
  if (substring === "") {
    return obj;
  }

  const words = substring.split(" ").filter(word => word !== "");

  const filteredObj: Obj = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key) && words.every(word => key.includes(word))) {
      filteredObj[key] = obj[key];
    }
  }

  return filteredObj;
}