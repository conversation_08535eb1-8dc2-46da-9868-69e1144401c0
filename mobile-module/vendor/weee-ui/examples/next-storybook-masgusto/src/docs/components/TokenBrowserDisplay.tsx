import React, { useEffect, useState } from "react";

import resolveConfig from "tailwindcss/resolveConfig.js";
import tailwindConfig from "../../../tailwind.config.js";

import fontTwConfig from "../../_imported_data/tailwind-classes/font-classes.tailwind.js";
import elevationTwConfig from "../../_imported_data/tailwind-classes/elevation-classes.tailwind.js";
import buttonsTwConfig from "../../_imported_data/tailwind-classes/button-classes.tailwind.js";
import cssFiltersTwConfig from "../../_imported_data/tailwind-classes/css-filter-classes.tailwind.js";

import ColorTable from "./ColorTable";
import SpacingTable from "./SpacingTable";
import TypographyTable from "./TypographyTable";
import FontSizeTable from "./FontSizeTable";
import ElevationTable from "./ElevationTable";
import ButtonsTable from "./ButtonsTable";
import RadiusTable from "./RadiusTable";
import FontWeightTable from "./FontWeightTable";
import FontTrackingTable from "./FontTrackingTable";
import FontLineHeightTable from "./FontLineheightTable";
import ScreensTable from "./ScreensTable";
import FiltersTable from "./FiltersTable";

export default function TokenBrowserDisplay() {
  const fullConfig = resolveConfig(tailwindConfig);

  const tab = {
    colors: "colors",
    spacing: "spacing",
    elevation: "elevation",
    buttons: "buttons",
    fontClasses: "font-classes",
    fontSize: "font-size",
    fontWeight: "font-weight",
    radius: "radius",
    letterSpacing: "letter-spacing",
    lineHeight: "line-height",
    screens: "screens",
    filters: "filters",
  };

  const [currentTab, setTab] = useState(tab.colors);

  // console.log(fullConfig);

  const activeTabStyles = `text-link-base-1 after:content-[""] after:bg-link-base-1 after:absolute after:bottom-[-1px] after:left-0 after:w-full after:h-[3px] after:z-2 after:rounded-tl after:rounded-tr`;
  const tabStyles = `enki-body-base-medium p-300 relative before:content-[""] hover:before:bg-surface-200-bg active:before:bg-surface-300-bg before:z-1 before:rounded-300 before:absolute before:left-0 before:right-0 before:top-200 before:bottom-200 whitespace-nowrap`;
  const inactiveTabStyles = `text-surface-100-fg-minor hover:text-surface-100-fg-default`;

  return (
    <div id="tokenBrowser">
      <main className="flex min-h-screen flex-col items-center justify-start">
        <div className="w-full items-center justify-center lg:flex pt-500">
          <div
            className={`w-full max-w-[1920px] bg-surface-100-bg text-surface-100-fg-default border border-surface-100-hairline enki-elevation-9 p-1000 rounded-800`}
          >
            <div>
              <div className={`enki-body-base-medium text-highlight-txt`}>
                Enki for Tailwind / Token browser / Embedded config
              </div>
              <div className={`flex gap-200 mt-300 mb-1000`}>
                <span
                  className={`py-100 px-200 enki-body-xs-strong bg-success-bg text-success-fg rounded-300`}
                >
                  Tiny CSS bundle — 1.4kb
                </span>
                <span
                  className={`py-100 px-200 enki-body-xs-strong bg-success-bg text-success-fg rounded-300`}
                >
                  SCSS variables
                </span>
                <span
                  className={`py-100 px-200 enki-body-xs-strong bg-critical-bg text-critical-fg rounded-300`}
                >
                  Dark mode not supported
                </span>
              </div>
            </div>

            {fullConfig.theme ? (
              <div
                className={`flex gap-100 sticky overflow-x-auto overflow-y-hidden top-[0px] bg-surface-100-bg z-10 justify-start border-b border-surface-100-hairline my-200`}
              >
                {fullConfig.theme.colors ? (
                  <button
                    className={`${tabStyles} ${
                      currentTab == tab.colors
                        ? activeTabStyles
                        : inactiveTabStyles
                    }`}
                    onClick={() => setTab(tab.colors)}
                  >
                    <span className={`relative z-3`}>Colors</span>
                  </button>
                ) : (
                  ""
                )}

                {fullConfig.theme.spacing ? (
                  <button
                    className={`${tabStyles} ${
                      currentTab == tab.spacing
                        ? activeTabStyles
                        : inactiveTabStyles
                    }`}
                    onClick={() => setTab(tab.spacing)}
                  >
                    <span className={`relative z-3`}>Size</span>
                  </button>
                ) : (
                  ""
                )}

                {fullConfig.theme.borderRadius ? (
                  <button
                    className={`${tabStyles} ${
                      currentTab == tab.radius
                        ? activeTabStyles
                        : inactiveTabStyles
                    }`}
                    onClick={() => setTab(tab.radius)}
                  >
                    <span className={`relative z-3`}>Radius</span>
                  </button>
                ) : (
                  ""
                )}

                {buttonsTwConfig ? (
                  <button
                    className={`${tabStyles} ${
                      currentTab == tab.buttons
                        ? activeTabStyles
                        : inactiveTabStyles
                    }`}
                    onClick={() => setTab(tab.buttons)}
                  >
                    <span className={`relative z-3`}>Buttons</span>
                  </button>
                ) : (
                  ""
                )}

                {elevationTwConfig ? (
                  <button
                    className={`${tabStyles} ${
                      currentTab == tab.elevation
                        ? activeTabStyles
                        : inactiveTabStyles
                    }`}
                    onClick={() => setTab(tab.elevation)}
                  >
                    <span className={`relative z-3`}>Elevation</span>
                  </button>
                ) : (
                  ""
                )}

                {fontTwConfig ? (
                  <button
                    className={`${tabStyles} ${
                      currentTab == tab.fontClasses
                        ? activeTabStyles
                        : inactiveTabStyles
                    }`}
                    onClick={() => setTab(tab.fontClasses)}
                  >
                    <span className={`relative z-3`}>Typography</span>
                  </button>
                ) : (
                  ""
                )}

                {fullConfig.theme.fontSize ? (
                  <button
                    className={`${tabStyles} ${
                      currentTab == tab.fontSize
                        ? activeTabStyles
                        : inactiveTabStyles
                    }`}
                    onClick={() => setTab(tab.fontSize)}
                  >
                    <span className={`relative z-3`}>Font size</span>
                  </button>
                ) : (
                  ""
                )}

                {fullConfig.theme.fontWeight ? (
                  <button
                    className={`${tabStyles} ${
                      currentTab == tab.fontWeight
                        ? activeTabStyles
                        : inactiveTabStyles
                    }`}
                    onClick={() => setTab(tab.fontWeight)}
                  >
                    <span className={`relative z-3`}>Font weight</span>
                  </button>
                ) : (
                  ""
                )}

                {fullConfig.theme.letterSpacing ? (
                  <button
                    className={`${tabStyles} ${
                      currentTab == tab.letterSpacing
                        ? activeTabStyles
                        : inactiveTabStyles
                    }`}
                    onClick={() => setTab(tab.letterSpacing)}
                  >
                    <span className={`relative z-3`}>Letter spacing</span>
                  </button>
                ) : (
                  ""
                )}

                {fullConfig.theme.lineHeight ? (
                  <button
                    className={`${tabStyles} ${
                      currentTab == tab.lineHeight
                        ? activeTabStyles
                        : inactiveTabStyles
                    }`}
                    onClick={() => setTab(tab.lineHeight)}
                  >
                    <span className={`relative z-3`}>Line height</span>
                  </button>
                ) : (
                  ""
                )}

                {/* {fullConfig.theme.screens ? (
                  <button
                    className={`${tabStyles} ${
                      currentTab == tab.screens
                        ? activeTabStyles
                        : inactiveTabStyles
                    }`}
                    onClick={() => setTab(tab.screens)}
                  >
                    <span className={`relative z-3`}>Breakpoints</span>
                  </button>
                ) : (
                  ""
                )} */}

                {cssFiltersTwConfig ? (
                  <button
                    className={`${tabStyles} ${
                      currentTab == tab.filters
                        ? activeTabStyles
                        : inactiveTabStyles
                    }`}
                    onClick={() => setTab(tab.filters)}
                  >
                    <span className={`relative z-3`}>Filters</span>
                  </button>
                ) : (
                  ""
                )}
              </div>
            ) : (
              ""
            )}

            {fullConfig.theme.colors && currentTab === tab.colors ? (
              <ColorTable twConfig={fullConfig} />
            ) : (
              ""
            )}

            {fullConfig.theme.spacing && currentTab === tab.spacing ? (
              <SpacingTable twConfig={fullConfig} />
            ) : (
              ""
            )}

            {fontTwConfig && currentTab === tab.fontClasses ? (
              <TypographyTable fontTwConfig={fontTwConfig} />
            ) : (
              ""
            )}

            {fullConfig.theme.fontSize && currentTab === tab.fontSize ? (
              <FontSizeTable twConfig={fullConfig} />
            ) : (
              ""
            )}

            {elevationTwConfig && currentTab === tab.elevation ? (
              <ElevationTable elevationTwConfig={elevationTwConfig} />
            ) : (
              ""
            )}

            {buttonsTwConfig && currentTab === tab.buttons ? (
              <ButtonsTable buttonsTwConfig={buttonsTwConfig} />
            ) : (
              ""
            )}

            {fullConfig.theme.borderRadius && currentTab === tab.radius ? (
              <RadiusTable twConfig={fullConfig} />
            ) : (
              ""
            )}

            {fullConfig.theme.fontWeight && currentTab === tab.fontWeight ? (
              <FontWeightTable twConfig={fullConfig} />
            ) : (
              ""
            )}

            {fullConfig.theme.letterSpacing &&
            currentTab === tab.letterSpacing ? (
              <FontTrackingTable twConfig={fullConfig} />
            ) : (
              ""
            )}

            {fullConfig.theme.lineHeight && currentTab === tab.lineHeight ? (
              <FontLineHeightTable twConfig={fullConfig} />
            ) : (
              ""
            )}

            {fullConfig.theme.screens && currentTab === tab.screens ? (
              <ScreensTable twConfig={fullConfig} />
            ) : (
              ""
            )}

            {cssFiltersTwConfig && currentTab === tab.filters ? (
              <FiltersTable cssFiltersTwConfig={cssFiltersTwConfig} />
            ) : (
              ""
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
