import { flattenObject } from "@/utils/flattenObject";
import { sortFlatObject } from "@/utils/sortFlatObject";
import { filterKeys } from "@/utils/filterKeys";
import { useEffect, useState } from "react";
import { filterOnlyMatchingKeys } from "@/utils/filterOnlyMatchingKeys";
import CodeSnippet from "./CodeSnippet";

interface NestedObject {
  [key: string]: any;
}

export default function ElevationTable({ elevationTwConfig }: NestedObject) {
  const [listFilter, setListFilter] = useState("");

  // const filteredKeys = filterKeys(elevationTwConfig, listFilter);

  return (
    <div className={`z-1`}>
      <div
        className={`flex items-center gap-400 border-b border-surface-100-hairline mb-200 pb-200`}
      >
        <div className={`flex-[.75]`}>
          <span className={`enki-body-sm-strong`}>Tailwind class</span>
        </div>
        <div className={`flex-[.75]`}>
          <span className={`enki-body-sm-strong`}>SCSS variable</span>
        </div>
        <div className={`flex-[.75]`}>
          <span className={`enki-body-sm-strong`}>Properties</span>
        </div>
        <div className={`flex-[1] flex justify-center`}>
          <span className={`enki-body-sm-strong`}>Sample</span>
        </div>
      </div>

      {Object.keys(elevationTwConfig).map((elevation: string, idx: number) => {
        return (
          <div
            className={`flex items-center gap-400 border-b border-surface-100-hairline mb-200 pb-200 text-sm`}
            key={idx}
          >
            <div className={`flex-[.75] flex gap-400 items-center`}>
              <CodeSnippet value={`${elevation}`} style={1} />
            </div>
            <div className={`flex-[.75] flex gap-400 items-center`}>
              <CodeSnippet value={`${elevation.replace(".enki", "$style")}`} style={3} />
            </div>
            <div className={`flex-[.75] flex`}>
              <CodeSnippet
                value={elevationTwConfig[elevation].boxShadow}
                style={2}
              />
            </div>
            <div className={`flex-[1] flex justify-center`}>
              <ElevationSwatch value={elevationTwConfig[elevation].boxShadow} />
            </div>
          </div>
        );
      })}
    </div>
  );
}

function ElevationSwatch({ value }: { value: string }) {
  return (
    <div className={`flex flex-1 justify-center items-center overflow-visible`}>
      <div
        style={{ boxShadow: value }}
        className={`w-2000 h-2000 bg-surface-100-bg-default rounded-400 my-600`}
      />
    </div>
  );
}
