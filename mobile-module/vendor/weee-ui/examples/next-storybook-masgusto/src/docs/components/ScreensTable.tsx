import { flattenObject } from "@/utils/flattenObject";
import { sortFlatObject } from "@/utils/sortFlatObject";
import { filterKeys } from "@/utils/filterKeys";
import { useEffect, useState } from "react";
import { filterOnlyMatchingKeys } from "@/utils/filterOnlyMatchingKeys";
import CodeSnippet from "./CodeSnippet";

interface NestedObject {
  [key: string]: any;
}

export default function ScreensTable({ twConfig }: NestedObject) {
  const [listFilter, setListFilter] = useState("");

  const configFlat = flattenObject(twConfig.theme.screens);
  const sortedConfig = filterOnlyMatchingKeys(configFlat, [
    "desktop",
    "tablet",
    "mobile",
  ]);

  const filteredKeys = filterKeys(sortedConfig, listFilter);

  return (
    <div className={`z-1`}>
      <div
        className={`flex items-center gap-400 border-b border-surface-100-hairline mb-200 pb-200`}
      >
        <div className={`flex-1`}>
          <span className={`enki-body-sm-strong`}>Tailwind class</span>
        </div>
        <div className={`flex-1`}>
          <span className={`enki-body-sm-strong`}>SCSS variable</span>
        </div>
        <div className={`flex-1`}>
          <span className={`enki-body-sm-strong`}>Properties</span>
        </div>
      </div>

      {Object.keys(filteredKeys).map((size: string, idx: number) => {
        let value = filteredKeys[size];

        return (
          <div
            className={`flex items-center gap-400 border-b border-surface-100-hairline mb-200 pb-200 text-sm`}
            key={idx}
          >
            <div className={`flex-1 flex gap-400 items-center`}>
              <CodeSnippet value={`.${size}:*`} style={1} />
            </div>
            <div className={`flex-1 flex gap-400 items-center`}>
              <CodeSnippet value={`$size-device-${size}`} style={3} />
            </div>
            <div className={`flex-1 flex justify-between`}>
              <CodeSnippet value={value} style={2} />
            </div>
          </div>
        );
      })}
    </div>
  );
}
