import { flattenObject } from "@/utils/flattenObject";
import { sortFlatObject } from "@/utils/sortFlatObject";
import { filterKeys } from "@/utils/filterKeys";
import { useEffect, useState } from "react";
import { filterOnlyMatchingKeys } from "@/utils/filterOnlyMatchingKeys";
import CodeSnippet from "./CodeSnippet";

interface NestedObject {
  [key: string]: any;
}

export default function FontWeightTable({ twConfig }: NestedObject) {
  const [listFilter, setListFilter] = useState("");

  const sizingConfigFlat = flattenObject(twConfig.theme.fontWeight);
  const sortedSpacingConfig = filterOnlyMatchingKeys(sizingConfigFlat, 
    [
      "400-regular",
      "500-medium",
      "600-semibold",
      "700-bold",
      "800-extrabold",
    ]
  );

  const filteredKeys = filterKeys(sortedSpacingConfig, listFilter);

  return (
    <div className={`z-1`}>
      <div
        className={`flex items-center gap-400 border-b border-surface-100-hairline mb-200 pb-200`}
      >
        <div className={`flex-[.75]`}>
          <span className={`enki-body-sm-strong`}>Tailwind class</span>
        </div>
        <div className={`flex-[.75]`}>
          <span className={`enki-body-sm-strong`}>SCSS variable</span>
        </div>
        <div className={`flex-[.75]`}>
          <span className={`enki-body-sm-strong`}>Properties</span>
        </div>
        <div className={`flex-1 flex justify-center`}>
          <span className={`enki-body-sm-strong`}>Sample</span>
        </div>
      </div>

      {Object.keys(filteredKeys).map((spacingSize: string, idx: number) => {
        let value = filteredKeys[spacingSize];

        return (
          <div
            className={`flex items-center gap-400 border-b border-surface-100-hairline mb-200 pb-200 text-sm`}
            key={idx}
          >
            <div className={`flex-[.75] flex gap-400 items-center`}>
              <CodeSnippet value={`.font-${spacingSize}`} style={1} />
            </div>
            <div className={`flex-[.75] flex gap-400 items-center`}>
              <CodeSnippet value={`$font-weight-${spacingSize}`} style={3} />
            </div>
            <div className={`flex-[.75] flex justify-between`}>
              <CodeSnippet value={value} style={2} />
            </div>
            <div className={`flex-1 flex justify-center`}>
              <FontWeightSwatch value={value} />
            </div>
          </div>
        );
      })}
    </div>
  );
}

function FontWeightSwatch({ value }: { value: string }) {
  return (
    <div className={`flex flex-1 justify-center items-center overflow-hidden`}>
      <div
        style={{
          fontWeight: value,
        }}
        className={`text-surface-100-fg-minor hover:text-surface-100-fg-default text-5xl`}
      >
        Howdy
      </div>
    </div>
  );
}
