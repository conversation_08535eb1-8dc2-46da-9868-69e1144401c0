import { flattenObject } from "@/utils/flattenObject";
import { sortFlatObject } from "@/utils/sortFlatObject";
import { filterKeys } from "@/utils/filterKeys";
import { useEffect, useState } from "react";
import { filterOnlyMatchingKeys } from "@/utils/filterOnlyMatchingKeys";
import CodeSnippet from "./CodeSnippet";

interface NestedObject {
  [key: string]: any;
}

export default function FontSizeTable({ twConfig }: NestedObject) {
  const [listFilter, setListFilter] = useState("");

  const sizingConfigFlat = flattenObject(twConfig.theme.fontSize);
  const sortedSpacingConfig = sortFlatObject(sizingConfigFlat, 
    [
      "3xs",
      "2xs",
      "xs",
      "sm",
      "base",
      "lg",
      "xl",
      "2xl",
      "3xl",
      "4xl",
      "5xl",
      "6xl",
      "7xl",
      "8xl",
      "9xl",
    ]
  );

  const filteredKeys = filterKeys(sortedSpacingConfig, listFilter);

  return (
    <div className={`z-1`}>
      <div
        className={`flex items-center gap-400 border-b border-surface-100-hairline mb-200 pb-200`}
      >
        <div className={`flex-[.75]`}>
          <span className={`enki-body-sm-strong`}>Tailwind class</span>
        </div>
        <div className={`flex-[.75]`}>
          <span className={`enki-body-sm-strong`}>SCSS variable</span>
        </div>
        <div className={`flex-[.75]`}>
          <span className={`enki-body-sm-strong`}>Properties</span>
        </div>
        <div className={`flex-1 flex justify-center`}>
          <span className={`enki-body-sm-strong`}>Sample</span>
        </div>
      </div>

      {Object.keys(filteredKeys).map((spacingSize: string, idx: number) => {
        let value = filteredKeys[spacingSize];

        return (
          <div
            className={`flex items-center gap-400 border-b border-surface-100-hairline mb-200 pb-200 text-sm`}
            key={idx}
          >
            <div className={`flex-[.75] flex gap-400 items-center`}>
              <CodeSnippet value={`.text-${spacingSize}`} style={1} />
            </div>
            <div className={`flex-[.75] flex gap-400 items-center`}>
              <CodeSnippet value={`$font-size-${spacingSize}`} style={3} />
            </div>
            <div className={`flex-[.75] flex justify-between`}>
              <CodeSnippet value={value} style={2} />
            </div>
            <div className={`flex-1 flex justify-center`}>
              <FontSizeSwatch value={value} />
            </div>
          </div>
        );
      })}
    </div>
  );
}

function FontSizeSwatch({ value }: { value: string }) {
  return (
    <div className={`flex flex-1 justify-center items-center overflow-hidden`}>
      <div
        style={{
          fontSize: value,
        }}
        className={`text-surface-100-fg-minor hover:text-surface-100-fg-default`}
      >
        Hello
      </div>
    </div>
  );
}
