import { Meta } from "@storybook/blocks";
import Image from "next/image";

import Github from "../assets/images/github.svg";
import Discord from "../assets/images/discord.svg";
import Youtube from "../assets/images/youtube.svg";
import Tutorials from "../assets/images/tutorials.svg";
import Styling from "../assets/images/styling.png";
import Context from "../assets/images/context.png";
import Assets from "../assets/images/assets.png";
import Docs from "../assets/images/docs.png";
import Share from "../assets/images/share.png";
import FigmaPlugin from "../assets/images/figma-plugin.png";
import Testing from "../assets/images/testing.png";
import Accessibility from "../assets/images/accessibility.png";
import Theming from "../assets/images/theming.png";
import AddonLibrary from "../assets/images/addon-library.png";

<Meta title="Enki Design System" />

export function ResourceCard(props) {
  return (
    <div className="sb-unstyled relative p-500 pb-[200px] flex-1 text-primary-1 bg-surface-100-bg border border-surface-100-hairline rounded-600 enki-elevation-5 hover:enki-filter-darken-1-hover active:enki-filter-darken-1-pressed">
      <a className="sb-unstyled absolute left-0 top-0 w-full h-full" href={props.linksTo} target="_blank" />
      <span className="block enki-body-base mb-200">{props.sublabel}</span>
      <span className="block enki-body-2xl-medium">{props.label}</span>
    </div>
  )
}

<div className="sb-unstyled text-surface-100-fg-default">
  <div>
    <h1 className="enki-body-sm-medium !tracking-widest text-highlight-txt">BUILD • CONTRIBUTE • EVOLVE</h1>
    <h1 className="enki-heading-5xl mb-200 text-primary-1">Enki Design System</h1>
    <span className={`py-100 px-200 enki-body-xs-strong bg-highlight-bg text-highlight-fg rounded-300`}>Version 2</span>
  </div>

  <div>
    <h2 className="enki-heading-3xl mt-800">Get started</h2>
  </div>
  <div className="flex flex-wrap gap-300 mt-500">
    <ResourceCard linksTo="?path=/docs/token-browser--docs" label="Token browser" sublabel="Find tokens" />
    <ResourceCard linksTo="https://www.sayweee.com/company/design/language" label="Design language" sublabel="Brand guidelines" />
  </div>

  <div>
    <p className="mt-500 enki-body-base max-w-[850px] text-surface-100-fg-minor">The Enki Design System is an adaptable system of components, tools, and guidelines intended to put people at the center of the digital food experience. We're firm believers that our system thrives on the collective strength of everyone involved. It brings together diverse talents, cultures, and perspectives, weaving them into something that ultimately enriches all of Weee!'s products.</p>
  </div>
</div>
