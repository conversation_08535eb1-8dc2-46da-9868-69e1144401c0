import React from "react";
import { twMerge } from "tailwind-merge";

interface ButtonProps {
  /**
   * Button style
   */
  style?:
    | "primary"
    | "secondary"
    | "tertiary"
    | "confirmation"
    | "surface"
    | "interactive"
    | "disabled"
    | "overlayWhite"
    | "overlay";
  /**
   * Button size
   */
  size?: "small" | "medium" | "large" | "xl";
  /**
   * Button contents
   */
  label: string;
  /**
   * Button style modifiers
   */
  modifiers?: string;
  /**
   * Optional click handler
   */
  onClick?: () => void;
}

/**
 * Primary UI component for user interaction
 */
export const Button = ({
  style = "primary",
  size = "medium",
  label,
  modifiers,
  ...props
}: ButtonProps) => {

  let buttonBaseStyles = `rounded-full appearance-none`;
  let buttonSizeStyle = {
    xl: `h-[60px]`,
    large: `h-[46px]`,
    medium: `h-[40px]`,
    small: `h-[28px]`,
  };
  let buttonSizePaddingStyle = {
    xl: `px-800`,
    large: `px-800`,
    medium: `px-600`,
    small: `px-400`,
  };
  let buttonSizeFontStyle = {
    xl: `enki-body-xl-medium`,
    large: `enki-body-base-medium`,
    medium: `enki-body-base-medium`,
    small: `enki-body-2xs-medium`,
  };
  let buttonVisualStyle = {
    primary: `enki-button-primary`,
    secondary: `enki-button-secondary`,
    tertiary: `enki-button-tertiary`,
    confirmation: `enki-button-confirmation`,
    surface: `border border-surface-100-hairline text-surface-100-fg-default hover:enki-filter-darken-1-hover active:enki-filter-darken-1-pressed`,
    interactive: `border border-link-base-1 text-link-base-1 hover:enki-filter-darken-1-hover active:enki-filter-darken-1-pressed`,
    disabled: `enki-button-disabled`,
    overlay: `bg-tint-black-600 text-reserved-white hover:bg-tint-black-700 active:bg-tint-black-800`,
    overlayWhite: `bg-tint-white-300 text-reserved-white hover:bg-tint-white-400 active:bg-tint-white-500`,
  };

  return (
    <button
      type="button"
      className={twMerge(
        buttonBaseStyles,
        buttonVisualStyle[style],
        buttonSizeFontStyle[size],
        buttonSizePaddingStyle[size],
        buttonSizeStyle[size],
        modifiers ? modifiers : ""
      )}
      {...props}
    >
      {label}
    </button>
  );
};
