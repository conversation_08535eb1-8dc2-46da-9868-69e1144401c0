import type { <PERSON>a, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import { Input } from './index';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  title: 'Input',
  component: Input,
  parameters: {
    layout: 'centered',
  },
  // tags: ['autodocs'],
} satisfies Meta<typeof Input>;

export default meta;
type Story = StoryObj<typeof meta>;

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Idle: Story = {
  args: {
    state: 'idle',
    placeholder: 'Enter email address',
  },
};

export const Error: Story = {
  args: {
    state: 'error',
    placeholder: 'Enter email address'
  },
};
