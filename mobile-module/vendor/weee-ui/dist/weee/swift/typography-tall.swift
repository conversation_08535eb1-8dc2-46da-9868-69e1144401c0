
//
// typography-tall.swift
//

// Do not edit directly
// Generated on Fri, 22 Aug 2025 03:03:52 GMT


import UIKit

public class EnkiKitTypography {
    public static let fontTrackingWidest = 0.60
    public static let fontTrackingWider = 0.30
    public static let fontTrackingWide = 0.20
    public static let fontTrackingBase = 0
    public static let fontTrackingTight = -0.20
    public static let fontTrackingTighter = -0.30
    public static let fontTrackingTightest = -0.60
    public static let fontLineheight150 = 1.50
    public static let fontLineheight125 = 1.25
    public static let fontLineheight115 = 1.15
    public static let fontLineheight110 = 1.10
    public static let fontLineheight105 = 1.05
    public static let fontLineheight100 = 1
    public static let fontWeight800Extrabold = 800
    public static let fontWeight700Bold = 700
    public static let fontWeight600Semibold = 600
    public static let fontWeight500Medium = 500
    public static let fontWeight400Regular = 400
    public static let fontSize10xl = 170
    public static let fontSize9xl = 128
    public static let fontSize8xl = 96
    public static let fontSize7xl = 72
    public static let fontSize6xl = 60
    public static let fontSize5xl = 48
    public static let fontSize4xl = 36
    public static let fontSize3xl = 30
    public static let fontSize2xl = 24
    public static let fontSizeXl = 20
    public static let fontSizeLg = 18
    public static let fontSizeBase = 16
    public static let fontSizeSm = 14
    public static let fontSizeXs = 13
    public static let fontSize2xs = 12
    public static let fontSize3xs = 11
    public static let fontFamilyTallExpressive = "Random Grotesque Standard Medium"
    public static let fontFamilyTallMain = "Be Vietnam Pro"
    public static let fontTallBody3xsWeightstrong = fontWeight600Semibold
    public static let fontTallBody3xsWeightmedium = fontWeight500Medium
    public static let fontTallBody3xsWeight = fontWeight400Regular
    public static let fontTallBody3xsLineheight = fontLineheight125
    public static let fontTallBody3xsSize = fontSize3xs
    public static let fontTallBody3xsTracking = fontTrackingBase
    public static let fontTallBody2xsWeightstrong = fontWeight600Semibold
    public static let fontTallBody2xsWeightmedium = fontWeight500Medium
    public static let fontTallBody2xsWeight = fontWeight400Regular
    public static let fontTallBody2xsLineheight = fontLineheight125
    public static let fontTallBody2xsSize = fontSize2xs
    public static let fontTallBody2xsTracking = fontTrackingBase
    public static let fontTallBodyXsWeightstrong = fontWeight600Semibold
    public static let fontTallBodyXsWeightmedium = fontWeight500Medium
    public static let fontTallBodyXsWeight = fontWeight400Regular
    public static let fontTallBodyXsLineheight = fontLineheight125
    public static let fontTallBodyXsSize = fontSizeXs
    public static let fontTallBodyXsTracking = fontTrackingBase
    public static let fontTallBodySmWeightstrong = fontWeight600Semibold
    public static let fontTallBodySmWeightmedium = fontWeight500Medium
    public static let fontTallBodySmWeight = fontWeight400Regular
    public static let fontTallBodySmLineheight = fontLineheight125
    public static let fontTallBodySmSize = fontSizeSm
    public static let fontTallBodySmTracking = fontTrackingBase
    public static let fontTallBodyBaseWeightstrong = fontWeight600Semibold
    public static let fontTallBodyBaseWeightmedium = fontWeight500Medium
    public static let fontTallBodyBaseWeight = fontWeight400Regular
    public static let fontTallBodyBaseLineheight = fontLineheight125
    public static let fontTallBodyBaseSize = fontSizeBase
    public static let fontTallBodyBaseTracking = fontTrackingBase
    public static let fontTallBodyLgWeightstrong = fontWeight600Semibold
    public static let fontTallBodyLgWeightmedium = fontWeight500Medium
    public static let fontTallBodyLgWeight = fontWeight400Regular
    public static let fontTallBodyLgLineheight = fontLineheight125
    public static let fontTallBodyLgSize = fontSizeLg
    public static let fontTallBodyLgTracking = fontTrackingBase
    public static let fontTallBodyXlWeightstrong = fontWeight600Semibold
    public static let fontTallBodyXlWeightmedium = fontWeight500Medium
    public static let fontTallBodyXlWeight = fontWeight400Regular
    public static let fontTallBodyXlLineheight = fontLineheight125
    public static let fontTallBodyXlSize = fontSizeXl
    public static let fontTallBodyXlTracking = fontTrackingBase
    public static let fontTallBody2xlWeightstrong = fontWeight600Semibold
    public static let fontTallBody2xlWeightmedium = fontWeight500Medium
    public static let fontTallBody2xlWeight = fontWeight400Regular
    public static let fontTallBody2xlLineheight = fontLineheight125
    public static let fontTallBody2xlSize = fontSize2xl
    public static let fontTallBody2xlTracking = fontTrackingBase
    public static let fontTallHeadingSmWeightstrong = fontWeight700Bold
    public static let fontTallHeadingSmWeight = fontWeight500Medium
    public static let fontTallHeadingSmLineheight = fontLineheight125
    public static let fontTallHeadingSmSize = fontSizeLg
    public static let fontTallHeadingSmTracking = fontTrackingBase
    public static let fontTallHeadingLgWeightstrong = fontWeight700Bold
    public static let fontTallHeadingLgWeight = fontWeight500Medium
    public static let fontTallHeadingLgLineheight = fontLineheight125
    public static let fontTallHeadingLgSize = fontSizeXl
    public static let fontTallHeadingLgTracking = fontTrackingBase
    public static let fontTallHeadingXlWeightstrong = fontWeight700Bold
    public static let fontTallHeadingXlWeight = fontWeight500Medium
    public static let fontTallHeadingXlLineheight = fontLineheight125
    public static let fontTallHeadingXlSize = fontSize2xl
    public static let fontTallHeadingXlTracking = fontTrackingBase
    public static let fontTallHeading2xlWeightstrong = fontWeight700Bold
    public static let fontTallHeading2xlWeight = fontWeight500Medium
    public static let fontTallHeading2xlLineheight = fontLineheight125
    public static let fontTallHeading2xlSize = fontSize3xl
    public static let fontTallHeading2xlTracking = fontTrackingBase
    public static let fontTallHeading3xlWeightstrong = fontWeight700Bold
    public static let fontTallHeading3xlWeight = fontWeight500Medium
    public static let fontTallHeading3xlLineheight = fontLineheight125
    public static let fontTallHeading3xlSize = fontSize4xl
    public static let fontTallHeading3xlTracking = fontTrackingBase
    public static let fontTallHeading4xlWeightstrong = fontWeight700Bold
    public static let fontTallHeading4xlWeight = fontWeight500Medium
    public static let fontTallHeading4xlLineheight = fontLineheight125
    public static let fontTallHeading4xlSize = fontSize5xl
    public static let fontTallHeading4xlTracking = fontTrackingBase
    public static let fontTallHeading5xlWeightstrong = fontWeight700Bold
    public static let fontTallHeading5xlWeight = fontWeight500Medium
    public static let fontTallHeading5xlLineheight = fontLineheight125
    public static let fontTallHeading5xlSize = fontSize6xl
    public static let fontTallHeading5xlTracking = fontTrackingBase
    public static let fontTallDisplaySmWeightstrong = fontWeight600Semibold
    public static let fontTallDisplaySmWeight = fontWeight500Medium
    public static let fontTallDisplaySmLineheight = fontLineheight110
    public static let fontTallDisplaySmSize = fontSizeXl
    public static let fontTallDisplaySmTracking = fontTrackingBase
    public static let fontTallDisplayLgWeightstrong = fontWeight600Semibold
    public static let fontTallDisplayLgWeight = fontWeight500Medium
    public static let fontTallDisplayLgLineheight = fontLineheight110
    public static let fontTallDisplayLgSize = fontSize2xl
    public static let fontTallDisplayLgTracking = fontTrackingBase
    public static let fontTallDisplayXlWeightstrong = fontWeight600Semibold
    public static let fontTallDisplayXlWeight = fontWeight500Medium
    public static let fontTallDisplayXlLineheight = fontLineheight110
    public static let fontTallDisplayXlSize = fontSize3xl
    public static let fontTallDisplayXlTracking = fontTrackingBase
    public static let fontTallDisplay2xlWeightstrong = fontWeight600Semibold
    public static let fontTallDisplay2xlWeight = fontWeight500Medium
    public static let fontTallDisplay2xlLineheight = fontLineheight110
    public static let fontTallDisplay2xlSize = fontSize4xl
    public static let fontTallDisplay2xlTracking = fontTrackingBase
    public static let fontTallDisplay3xlWeightstrong = fontWeight600Semibold
    public static let fontTallDisplay3xlWeight = fontWeight500Medium
    public static let fontTallDisplay3xlLineheight = fontLineheight110
    public static let fontTallDisplay3xlSize = fontSize5xl
    public static let fontTallDisplay3xlTracking = fontTrackingBase
    public static let fontTallDisplay4xlWeightstrong = fontWeight600Semibold
    public static let fontTallDisplay4xlWeight = fontWeight500Medium
    public static let fontTallDisplay4xlLineheight = fontLineheight110
    public static let fontTallDisplay4xlSize = fontSize6xl
    public static let fontTallDisplay4xlTracking = fontTrackingBase
    public static let fontTallDisplay5xlWeightstrong = fontWeight600Semibold
    public static let fontTallDisplay5xlWeight = fontWeight500Medium
    public static let fontTallDisplay5xlLineheight = fontLineheight110
    public static let fontTallDisplay5xlSize = fontSize7xl
    public static let fontTallDisplay5xlTracking = fontTrackingBase
    public static let fontFamilyTallDisplay = fontFamilyTallExpressive
    public static let fontFamilyTallHeading = fontFamilyTallMain
    public static let fontFamilyTallBody = fontFamilyTallMain
    public static let fontTallBody3xsFamily = fontFamilyTallBody
    public static let fontTallBody2xsFamily = fontFamilyTallBody
    public static let fontTallBodyXsFamily = fontFamilyTallBody
    public static let fontTallBodySmFamily = fontFamilyTallBody
    public static let fontTallBodyBaseFamily = fontFamilyTallBody
    public static let fontTallBodyLgFamily = fontFamilyTallBody
    public static let fontTallBodyXlFamily = fontFamilyTallBody
    public static let fontTallBody2xlFamily = fontFamilyTallBody
    public static let fontTallHeadingSmFamily = fontFamilyTallHeading
    public static let fontTallHeadingLgFamily = fontFamilyTallHeading
    public static let fontTallHeadingXlFamily = fontFamilyTallHeading
    public static let fontTallHeading2xlFamily = fontFamilyTallHeading
    public static let fontTallHeading3xlFamily = fontFamilyTallHeading
    public static let fontTallHeading4xlFamily = fontFamilyTallHeading
    public static let fontTallHeading5xlFamily = fontFamilyTallHeading
    public static let fontTallDisplaySmFamily = fontFamilyTallDisplay
    public static let fontTallDisplayLgFamily = fontFamilyTallDisplay
    public static let fontTallDisplayXlFamily = fontFamilyTallDisplay
    public static let fontTallDisplay2xlFamily = fontFamilyTallDisplay
    public static let fontTallDisplay3xlFamily = fontFamilyTallDisplay
    public static let fontTallDisplay4xlFamily = fontFamilyTallDisplay
    public static let fontTallDisplay5xlFamily = fontFamilyTallDisplay
}
