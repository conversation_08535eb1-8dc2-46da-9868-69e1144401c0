
// Do not edit directly
// Generated on Fri, 22 Aug 2025 03:03:51 GMT

$font-family-number-main: SF Pro Text, SF Pro, Microsoft YaHei, PingFang SC, Roboto, Helvetica Neue, Helvetica, Arial, Apple SD Gothic Neo, Malgun Gothic, BlinkMacSystemFont, -apple-system, Segoe UI, Ubuntu, sans-serif;
$font-family-latin-expressive: 'Random Grotesque Standard Medium', SF Pro Text, SF Pro, Microsoft YaHei, PingFang SC, Roboto, Helvetica Neue, Helvetica, Arial, Apple SD Gothic Neo, Malgun Gothic, BlinkMacSystemFont, -apple-system, Segoe UI, Ubuntu, sans-serif;
$font-family-latin-main: 'Poppins', SF Pro Text, SF Pro, Microsoft YaHei, PingFang SC, Roboto, Helvetica Neue, Helvetica, Arial, Apple SD Gothic Neo, Malgun Gothic, BlinkMacSystemFont, -apple-system, Segoe UI, Ubuntu, sans-serif;
$font-tracking-widest: 0.60px;
$font-tracking-wider: 0.30px;
$font-tracking-wide: 0.20px;
$font-tracking-base: 0px;
$font-tracking-tight: -0.20px;
$font-tracking-tighter: -0.30px;
$font-tracking-tightest: -0.60px;
$font-lineheight-150: 1.50;
$font-lineheight-125: 1.25;
$font-lineheight-115: 1.15;
$font-lineheight-110: 1.10;
$font-lineheight-105: 1.05;
$font-lineheight-100: 1;
$font-weight-800-extrabold: 800;
$font-weight-700-bold: 700;
$font-weight-600-semibold: 600;
$font-weight-500-medium: 500;
$font-weight-400-regular: 400;
$font-size-10xl: 170px;
$font-size-9xl: 128px;
$font-size-8xl: 96px;
$font-size-7xl: 72px;
$font-size-6xl: 60px;
$font-size-5xl: 48px;
$font-size-4xl: 36px;
$font-size-3xl: 30px;
$font-size-2xl: 24px;
$font-size-xl: 20px;
$font-size-lg: 18px;
$font-size-base: 16px;
$font-size-sm: 14px;
$font-size-xs: 13px;
$font-size-2xs: 12px;
$font-size-3xs: 11px;
$font-family-latin-display: $font-family-latin-expressive;
$font-family-latin-heading: $font-family-latin-main;
$font-family-latin-body: $font-family-latin-main;
