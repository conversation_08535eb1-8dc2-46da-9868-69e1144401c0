
// Do not edit directly
// Generated on Fri, 22 Aug 2025 03:03:51 GMT

$size-radius-full: 9999px;
$size-radius-800: 28px;
$size-radius-700: 24px;
$size-radius-600: 20px;
$size-radius-500: 16px;
$size-radius-400: 12px;
$size-radius-300: 8px;
$size-radius-200: 4px;
$size-radius-100: 2px;
$size-device-mobile-sm-w: 360px;
$size-device-mobile-lg-w: 390px;
$size-device-tablet-sm-w: 600px;
$size-device-tablet-md-w: 768px;
$size-device-tablet-lg-w: 1100px;
$size-device-desktop-xs-w: 1280px;
$size-device-desktop-sm-w: 1440px;
$size-device-desktop-md-w: 1600px;
$size-device-desktop-lg-w: 1920px;
$size-device-desktop-xl-w: 2000px;
$size-spacing-2000: 80px;
$size-spacing-1900: 76px;
$size-spacing-1800: 72px;
$size-spacing-1700: 68px;
$size-spacing-1600: 64px;
$size-spacing-1500: 60px;
$size-spacing-1400: 56px;
$size-spacing-1300: 52px;
$size-spacing-1200: 48px;
$size-spacing-1100: 44px;
$size-spacing-1000: 40px;
$size-spacing-900: 36px;
$size-spacing-800: 32px;
$size-spacing-700: 28px;
$size-spacing-600: 24px;
$size-spacing-500: 20px;
$size-spacing-400: 16px;
$size-spacing-300: 12px;
$size-spacing-200: 8px;
$size-spacing-100: 4px;
$size-spacing-50: 2px;
$size-spacing-0: 0px;
$size-elevation-spread-300: 3px;
$size-elevation-spread-200: 2px;
$size-elevation-spread-100: 1px;
$size-elevation-blur-2200: 80px;
$size-elevation-blur-1800: 64px;
$size-elevation-blur-1400: 48px;
$size-elevation-blur-1300: 44px;
$size-elevation-blur-1200: 40px;
$size-elevation-blur-1100: 36px;
$size-elevation-blur-1000: 32px;
$size-elevation-blur-900: 28px;
$size-elevation-blur-800: 24px;
$size-elevation-blur-700: 20px;
$size-elevation-blur-600: 16px;
$size-elevation-blur-500: 12px;
$size-elevation-blur-400: 8px;
$size-elevation-blur-300: 6px;
$size-elevation-blur-200: 4px;
$size-elevation-blur-100: 0px;
$size-elevation-distance-1200: 24px;
$size-elevation-distance-1100: 22px;
$size-elevation-distance-1000: 20px;
$size-elevation-distance-900: 18px;
$size-elevation-distance-800: 16px;
$size-elevation-distance-700: 14px;
$size-elevation-distance-600: 12px;
$size-elevation-distance-500: 10px;
$size-elevation-distance-400: 8px;
$size-elevation-distance-300: 6px;
$size-elevation-distance-200: 2px;
$size-elevation-distance-100: 1px;
