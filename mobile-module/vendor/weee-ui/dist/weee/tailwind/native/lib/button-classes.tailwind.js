
  /** 
   * Do not edit directly
   * Generated on Fri Aug 22 2025 11:03:52 GMT+0800 (China Standard Time)
   */
  

module.exports = {
  ".enki-button-primary": {
    "appearance": "none",
    "outline": "none",
    "cursor": "pointer",
    "backgroundColor": "#00b2ea",
    "color": "#ffffff",
    "@media(hover:hover)": {
      "&:hover": {
        "filter": "brightness(105%) saturate(105%)"
      }
    },
    "&:active": {
      "filter": "brightness(108%) saturate(108%)"
    }
  },
  ".enki-button-secondary": {
    "appearance": "none",
    "outline": "none",
    "cursor": "pointer",
    "backgroundColor": "#001ba5",
    "color": "#ffffff",
    "@media(hover:hover)": {
      "&:hover": {
        "filter": "brightness(105%) saturate(105%)"
      }
    },
    "&:active": {
      "filter": "brightness(108%) saturate(108%)"
    }
  },
  ".enki-button-tertiary": {
    "appearance": "none",
    "outline": "none",
    "cursor": "pointer",
    "backgroundColor": "#eef2fb",
    "color": "#07101a",
    "@media(hover:hover)": {
      "&:hover": {
        "filter": "brightness(95%) saturate(105%)"
      }
    },
    "&:active": {
      "filter": "brightness(92%) saturate(108%)"
    }
  },
  ".enki-button-confirmation": {
    "appearance": "none",
    "outline": "none",
    "cursor": "pointer",
    "backgroundColor": "#1eba9c",
    "color": "#ffffff",
    "@media(hover:hover)": {
      "&:hover": {
        "filter": "brightness(95%) saturate(105%)"
      }
    },
    "&:active": {
      "filter": "brightness(92%) saturate(108%)"
    }
  },
  ".enki-button-critical": {
    "appearance": "none",
    "outline": "none",
    "cursor": "pointer",
    "backgroundColor": "#ec4143",
    "color": "#ffffff",
    "@media(hover:hover)": {
      "&:hover": {
        "filter": "brightness(95%) saturate(105%)"
      }
    },
    "&:active": {
      "filter": "brightness(92%) saturate(108%)"
    }
  },
  ".enki-button-disabled": {
    "appearance": "none",
    "outline": "none",
    "cursor": "not-allowed",
    "backgroundColor": "#dee4f3",
    "color": "#758296"
  }
}