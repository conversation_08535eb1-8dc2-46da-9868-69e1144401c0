
/**
 * Do not edit directly
 * Autogenerated on Fri Aug 22 2025 11:03:59 GMT+0800 (China Standard Time)
 */
    

.enki-button-primary {
  appearance: none;
  outline: none;
  cursor: pointer;

  background-color: var(--color-btn-primary-bg);
  color: var(--color-btn-primary-fg-default);
}

@media (hover: hover) {
  .enki-button-primary:hover {
    filter: var(--style-filter-lighten-1-hover);
  }
}

.enki-button-primary:active {
  filter: var(--style-filter-lighten-1-pressed);
}

.enki-button-secondary {
  appearance: none;
  outline: none;
  cursor: pointer;

  background-color: var(--color-btn-secondary-bg);
  color: var(--color-btn-secondary-fg-default);
}

@media (hover: hover) {
  .enki-button-secondary:hover {
    filter: var(--style-filter-lighten-1-hover);
  }
}

.enki-button-secondary:active {
  filter: var(--style-filter-lighten-1-pressed);
}

.enki-button-tertiary {
  appearance: none;
  outline: none;
  cursor: pointer;

  background-color: var(--color-btn-tertiary-bg);
  color: var(--color-btn-tertiary-fg-default);
}

@media (hover: hover) {
  .enki-button-tertiary:hover {
    filter: var(--style-filter-darken-1-hover);
  }
}

.enki-button-tertiary:active {
  filter: var(--style-filter-darken-1-pressed);
}

.enki-button-confirmation {
  appearance: none;
  outline: none;
  cursor: pointer;

  background-color: var(--color-btn-confirmation-bg);
  color: var(--color-btn-confirmation-fg-default);
}

@media (hover: hover) {
  .enki-button-confirmation:hover {
    filter: var(--style-filter-darken-1-hover);
  }
}

.enki-button-confirmation:active {
  filter: var(--style-filter-darken-1-pressed);
}

.enki-button-critical {
  appearance: none;
  outline: none;
  cursor: pointer;

  background-color: var(--color-btn-critical-bg);
  color: var(--color-btn-critical-fg-default);
}

@media (hover: hover) {
  .enki-button-critical:hover {
    filter: var(--style-filter-darken-1-hover);
  }
}

.enki-button-critical:active {
  filter: var(--style-filter-darken-1-pressed);
}

.enki-button-disabled {
  appearance: none;
  outline: none;
  cursor: not-allowed;

  background-color: var(--color-btn-disabled-bg);
  color: var(--color-btn-disabled-fg-default);
}

