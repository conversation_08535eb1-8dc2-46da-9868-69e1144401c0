
  /** 
   * Do not edit directly
   * Generated on Fri Aug 22 2025 11:03:59 GMT+0800 (China Standard Time)
   */
  

module.exports = {
  ".enki-display-5xl": {
    "letterSpacing": "0.60px",
    "fontSize": "60px",
    "fontFamily": "var(--fd-5xl-fm)",
    "fontWeight": "500",
    "lineHeight": "1.25"
  },
  ".enki-display-5xl-strong": {
    "letterSpacing": "0.60px",
    "fontSize": "60px",
    "fontFamily": "var(--fd-5xl-fm)",
    "lineHeight": "1.25",
    "fontWeight": "700"
  },
  ".enki-display-4xl": {
    "letterSpacing": "0.60px",
    "fontSize": "48px",
    "fontFamily": "var(--fd-4xl-fm)",
    "fontWeight": "500",
    "lineHeight": "1.25"
  },
  ".enki-display-4xl-strong": {
    "letterSpacing": "0.60px",
    "fontSize": "48px",
    "fontFamily": "var(--fd-4xl-fm)",
    "lineHeight": "1.25",
    "fontWeight": "700"
  },
  ".enki-display-3xl": {
    "letterSpacing": "0.60px",
    "fontSize": "36px",
    "fontFamily": "var(--fd-3xl-fm)",
    "fontWeight": "500",
    "lineHeight": "1.25"
  },
  ".enki-display-3xl-strong": {
    "letterSpacing": "0.60px",
    "fontSize": "36px",
    "fontFamily": "var(--fd-3xl-fm)",
    "lineHeight": "1.25",
    "fontWeight": "700"
  },
  ".enki-display-2xl": {
    "letterSpacing": "0.60px",
    "fontSize": "30px",
    "fontFamily": "var(--fd-2xl-fm)",
    "fontWeight": "500",
    "lineHeight": "1.25"
  },
  ".enki-display-2xl-strong": {
    "letterSpacing": "0.60px",
    "fontSize": "30px",
    "fontFamily": "var(--fd-2xl-fm)",
    "lineHeight": "1.25",
    "fontWeight": "700"
  },
  ".enki-display-xl": {
    "letterSpacing": "0.60px",
    "fontSize": "24px",
    "fontFamily": "var(--fd-xl-fm)",
    "fontWeight": "500",
    "lineHeight": "1.25"
  },
  ".enki-display-xl-strong": {
    "letterSpacing": "0.60px",
    "fontSize": "24px",
    "fontFamily": "var(--fd-xl-fm)",
    "lineHeight": "1.25",
    "fontWeight": "700"
  },
  ".enki-display-lg": {
    "letterSpacing": "0.60px",
    "fontSize": "20px",
    "fontFamily": "var(--fd-lg-fm)",
    "fontWeight": "500",
    "lineHeight": "1.25"
  },
  ".enki-display-lg-strong": {
    "letterSpacing": "0.60px",
    "fontSize": "20px",
    "fontFamily": "var(--fd-lg-fm)",
    "lineHeight": "1.25",
    "fontWeight": "700"
  },
  ".enki-display-sm": {
    "letterSpacing": "0.60px",
    "fontSize": "18px",
    "fontFamily": "var(--fd-sm-fm)",
    "fontWeight": "500",
    "lineHeight": "1.25"
  },
  ".enki-display-sm-strong": {
    "letterSpacing": "0.60px",
    "fontSize": "18px",
    "fontFamily": "var(--fd-sm-fm)",
    "lineHeight": "1.25",
    "fontWeight": "700"
  },
  ".enki-heading-5xl": {
    "letterSpacing": "0.60px",
    "fontSize": "60px",
    "fontFamily": "var(--fh-5xl-fm)",
    "fontWeight": "500",
    "lineHeight": "1.25"
  },
  ".enki-heading-5xl-strong": {
    "letterSpacing": "0.60px",
    "fontSize": "60px",
    "fontFamily": "var(--fh-5xl-fm)",
    "lineHeight": "1.25",
    "fontWeight": "700"
  },
  ".enki-heading-4xl": {
    "letterSpacing": "0.60px",
    "fontSize": "48px",
    "fontFamily": "var(--fh-4xl-fm)",
    "fontWeight": "500",
    "lineHeight": "1.25"
  },
  ".enki-heading-4xl-strong": {
    "letterSpacing": "0.60px",
    "fontSize": "48px",
    "fontFamily": "var(--fh-4xl-fm)",
    "lineHeight": "1.25",
    "fontWeight": "700"
  },
  ".enki-heading-3xl": {
    "letterSpacing": "0.60px",
    "fontSize": "36px",
    "fontFamily": "var(--fh-3xl-fm)",
    "fontWeight": "500",
    "lineHeight": "1.25"
  },
  ".enki-heading-3xl-strong": {
    "letterSpacing": "0.60px",
    "fontSize": "36px",
    "fontFamily": "var(--fh-3xl-fm)",
    "lineHeight": "1.25",
    "fontWeight": "700"
  },
  ".enki-heading-2xl": {
    "letterSpacing": "0.60px",
    "fontSize": "30px",
    "fontFamily": "var(--fh-2xl-fm)",
    "fontWeight": "500",
    "lineHeight": "1.25"
  },
  ".enki-heading-2xl-strong": {
    "letterSpacing": "0.60px",
    "fontSize": "30px",
    "fontFamily": "var(--fh-2xl-fm)",
    "lineHeight": "1.25",
    "fontWeight": "700"
  },
  ".enki-heading-xl": {
    "letterSpacing": "0.60px",
    "fontSize": "24px",
    "fontFamily": "var(--fh-xl-fm)",
    "fontWeight": "500",
    "lineHeight": "1.25"
  },
  ".enki-heading-xl-strong": {
    "letterSpacing": "0.60px",
    "fontSize": "24px",
    "fontFamily": "var(--fh-xl-fm)",
    "lineHeight": "1.25",
    "fontWeight": "700"
  },
  ".enki-heading-lg": {
    "letterSpacing": "0.60px",
    "fontSize": "20px",
    "fontFamily": "var(--fh-lg-fm)",
    "fontWeight": "500",
    "lineHeight": "1.25"
  },
  ".enki-heading-lg-strong": {
    "letterSpacing": "0.60px",
    "fontSize": "20px",
    "fontFamily": "var(--fh-lg-fm)",
    "lineHeight": "1.25",
    "fontWeight": "700"
  },
  ".enki-heading-sm": {
    "letterSpacing": "0.60px",
    "fontSize": "18px",
    "fontFamily": "var(--fh-sm-fm)",
    "fontWeight": "500",
    "lineHeight": "1.25"
  },
  ".enki-heading-sm-strong": {
    "letterSpacing": "0.60px",
    "fontSize": "18px",
    "fontFamily": "var(--fh-sm-fm)",
    "lineHeight": "1.25",
    "fontWeight": "700"
  },
  ".enki-body-2xl": {
    "letterSpacing": "0.60px",
    "fontSize": "24px",
    "fontFamily": "var(--fb-2xl-fm)",
    "fontWeight": "400",
    "lineHeight": "1.25"
  },
  ".enki-body-2xl-strong": {
    "letterSpacing": "0.60px",
    "fontSize": "24px",
    "fontFamily": "var(--fb-2xl-fm)",
    "lineHeight": "1.25",
    "fontWeight": "700"
  },
  ".enki-body-2xl-medium": {
    "letterSpacing": "0.60px",
    "fontSize": "24px",
    "fontFamily": "var(--fb-2xl-fm)",
    "lineHeight": "1.25",
    "fontWeight": "500"
  },
  ".enki-body-xl": {
    "letterSpacing": "0.60px",
    "fontSize": "20px",
    "fontFamily": "var(--fb-xl-fm)",
    "fontWeight": "400",
    "lineHeight": "1.25"
  },
  ".enki-body-xl-strong": {
    "letterSpacing": "0.60px",
    "fontSize": "20px",
    "fontFamily": "var(--fb-xl-fm)",
    "lineHeight": "1.25",
    "fontWeight": "700"
  },
  ".enki-body-xl-medium": {
    "letterSpacing": "0.60px",
    "fontSize": "20px",
    "fontFamily": "var(--fb-xl-fm)",
    "lineHeight": "1.25",
    "fontWeight": "500"
  },
  ".enki-body-lg": {
    "letterSpacing": "0.60px",
    "fontSize": "18px",
    "fontFamily": "var(--fb-lg-fm)",
    "fontWeight": "400",
    "lineHeight": "1.25"
  },
  ".enki-body-lg-strong": {
    "letterSpacing": "0.60px",
    "fontSize": "18px",
    "fontFamily": "var(--fb-lg-fm)",
    "lineHeight": "1.25",
    "fontWeight": "700"
  },
  ".enki-body-lg-medium": {
    "letterSpacing": "0.60px",
    "fontSize": "18px",
    "fontFamily": "var(--fb-lg-fm)",
    "lineHeight": "1.25",
    "fontWeight": "500"
  },
  ".enki-body-base": {
    "letterSpacing": "0.60px",
    "fontSize": "16px",
    "fontFamily": "var(--fb-bs-fm)",
    "fontWeight": "400",
    "lineHeight": "1.25"
  },
  ".enki-body-base-strong": {
    "letterSpacing": "0.60px",
    "fontSize": "16px",
    "fontFamily": "var(--fb-bs-fm)",
    "lineHeight": "1.25",
    "fontWeight": "700"
  },
  ".enki-body-base-medium": {
    "letterSpacing": "0.60px",
    "fontSize": "16px",
    "fontFamily": "var(--fb-bs-fm)",
    "lineHeight": "1.25",
    "fontWeight": "500"
  },
  ".enki-body-sm": {
    "letterSpacing": "0.60px",
    "fontSize": "14px",
    "fontFamily": "var(--fb-sm-fm)",
    "fontWeight": "400",
    "lineHeight": "1.25"
  },
  ".enki-body-sm-strong": {
    "letterSpacing": "0.60px",
    "fontSize": "14px",
    "fontFamily": "var(--fb-sm-fm)",
    "lineHeight": "1.25",
    "fontWeight": "700"
  },
  ".enki-body-sm-medium": {
    "letterSpacing": "0.60px",
    "fontSize": "14px",
    "fontFamily": "var(--fb-sm-fm)",
    "lineHeight": "1.25",
    "fontWeight": "500"
  },
  ".enki-body-xs": {
    "letterSpacing": "0.60px",
    "fontSize": "13px",
    "fontFamily": "var(--fb-xs-fm)",
    "fontWeight": "400",
    "lineHeight": "1.25"
  },
  ".enki-body-xs-strong": {
    "letterSpacing": "0.60px",
    "fontSize": "13px",
    "fontFamily": "var(--fb-xs-fm)",
    "lineHeight": "1.25",
    "fontWeight": "700"
  },
  ".enki-body-xs-medium": {
    "letterSpacing": "0.60px",
    "fontSize": "13px",
    "fontFamily": "var(--fb-xs-fm)",
    "lineHeight": "1.25",
    "fontWeight": "500"
  },
  ".enki-body-2xs": {
    "letterSpacing": "0.60px",
    "fontSize": "12px",
    "fontFamily": "var(--fb-2xs-fm)",
    "fontWeight": "400",
    "lineHeight": "1.25"
  },
  ".enki-body-2xs-strong": {
    "letterSpacing": "0.60px",
    "fontSize": "12px",
    "fontFamily": "var(--fb-2xs-fm)",
    "lineHeight": "1.25",
    "fontWeight": "700"
  },
  ".enki-body-2xs-medium": {
    "letterSpacing": "0.60px",
    "fontSize": "12px",
    "fontFamily": "var(--fb-2xs-fm)",
    "lineHeight": "1.25",
    "fontWeight": "500"
  },
  ".enki-body-3xs": {
    "letterSpacing": "0.60px",
    "fontSize": "11px",
    "fontFamily": "var(--fb-3xs-fm)",
    "fontWeight": "400",
    "lineHeight": "1.25"
  },
  ".enki-body-3xs-strong": {
    "letterSpacing": "0.60px",
    "fontSize": "11px",
    "fontFamily": "var(--fb-3xs-fm)",
    "lineHeight": "1.25",
    "fontWeight": "700"
  },
  ".enki-body-3xs-medium": {
    "letterSpacing": "0.60px",
    "fontSize": "11px",
    "fontFamily": "var(--fb-3xs-fm)",
    "lineHeight": "1.25",
    "fontWeight": "500"
  }
}