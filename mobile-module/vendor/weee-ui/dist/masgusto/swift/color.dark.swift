
//
// color.dark.swift
//

// Do not edit directly
// Generated on Fri, 22 Aug 2025 03:03:58 GMT


import UIKit

public extension EnkiKitColorDark {
    static let colorProductBgImgTint = UIColor(red: 0.000, green: 0.000, blue: 0.000, alpha: 0)
    static let colorNavbarBgTransluscent = UIColor(red: 0.024, green: 0.071, blue: 0.114, alpha: 0.88)
    static let colorNavbarBgSelected = UIColor(red: 0.008, green: 0.498, blue: 1.000, alpha: 0.12)
    static let colorTintBlack1000 = UIColor(red: 0.000, green: 0.000, blue: 0.000, alpha: 1)
    static let colorTintBlack950 = UIColor(red: 0.000, green: 0.000, blue: 0.000, alpha: 0.93)
    static let colorTintBlack900 = UIColor(red: 0.000, green: 0.000, blue: 0.000, alpha: 0.88)
    static let colorTintBlack850 = UIColor(red: 0.000, green: 0.000, blue: 0.000, alpha: 0.82)
    static let colorTintBlack800 = UIColor(red: 0.000, green: 0.000, blue: 0.000, alpha: 0.78)
    static let colorTintBlack750 = UIColor(red: 0.000, green: 0.000, blue: 0.000, alpha: 0.74)
    static let colorTintBlack700 = UIColor(red: 0.000, green: 0.000, blue: 0.000, alpha: 0.7)
    static let colorTintBlack650 = UIColor(red: 0.000, green: 0.000, blue: 0.000, alpha: 0.65)
    static let colorTintBlack600 = UIColor(red: 0.000, green: 0.000, blue: 0.000, alpha: 0.6)
    static let colorTintBlack550 = UIColor(red: 0.000, green: 0.000, blue: 0.000, alpha: 0.57)
    static let colorTintBlack500 = UIColor(red: 0.000, green: 0.000, blue: 0.000, alpha: 0.54)
    static let colorTintBlack450 = UIColor(red: 0.000, green: 0.000, blue: 0.000, alpha: 0.51)
    static let colorTintBlack400 = UIColor(red: 0.000, green: 0.000, blue: 0.000, alpha: 0.46)
    static let colorTintBlack350 = UIColor(red: 0.000, green: 0.000, blue: 0.000, alpha: 0.4)
    static let colorTintBlack300 = UIColor(red: 0.000, green: 0.000, blue: 0.000, alpha: 0.35)
    static let colorTintBlack250 = UIColor(red: 0.000, green: 0.000, blue: 0.000, alpha: 0.29)
    static let colorTintBlack200 = UIColor(red: 0.000, green: 0.000, blue: 0.000, alpha: 0.25)
    static let colorTintBlack150 = UIColor(red: 0.000, green: 0.000, blue: 0.000, alpha: 0.2)
    static let colorTintBlack100 = UIColor(red: 0.000, green: 0.000, blue: 0.000, alpha: 0.15)
    static let colorTintBlack50 = UIColor(red: 0.000, green: 0.000, blue: 0.000, alpha: 0.1)
    static let colorTintBlack25 = UIColor(red: 0.000, green: 0.000, blue: 0.000, alpha: 0.05)
    static let colorTintWhite1000 = UIColor(red: 1.000, green: 1.000, blue: 1.000, alpha: 1)
    static let colorTintWhite950 = UIColor(red: 1.000, green: 1.000, blue: 1.000, alpha: 0.93)
    static let colorTintWhite900 = UIColor(red: 1.000, green: 1.000, blue: 1.000, alpha: 0.88)
    static let colorTintWhite850 = UIColor(red: 1.000, green: 1.000, blue: 1.000, alpha: 0.82)
    static let colorTintWhite800 = UIColor(red: 1.000, green: 1.000, blue: 1.000, alpha: 0.78)
    static let colorTintWhite750 = UIColor(red: 1.000, green: 1.000, blue: 1.000, alpha: 0.74)
    static let colorTintWhite700 = UIColor(red: 1.000, green: 1.000, blue: 1.000, alpha: 0.7)
    static let colorTintWhite650 = UIColor(red: 1.000, green: 1.000, blue: 1.000, alpha: 0.65)
    static let colorTintWhite600 = UIColor(red: 1.000, green: 1.000, blue: 1.000, alpha: 0.6)
    static let colorTintWhite550 = UIColor(red: 1.000, green: 1.000, blue: 1.000, alpha: 0.57)
    static let colorTintWhite500 = UIColor(red: 1.000, green: 1.000, blue: 1.000, alpha: 0.54)
    static let colorTintWhite450 = UIColor(red: 1.000, green: 1.000, blue: 1.000, alpha: 0.51)
    static let colorTintWhite400 = UIColor(red: 1.000, green: 1.000, blue: 1.000, alpha: 0.46)
    static let colorTintWhite350 = UIColor(red: 1.000, green: 1.000, blue: 1.000, alpha: 0.4)
    static let colorTintWhite300 = UIColor(red: 1.000, green: 1.000, blue: 1.000, alpha: 0.35)
    static let colorTintWhite250 = UIColor(red: 1.000, green: 1.000, blue: 1.000, alpha: 0.29)
    static let colorTintWhite200 = UIColor(red: 1.000, green: 1.000, blue: 1.000, alpha: 0.25)
    static let colorTintWhite150 = UIColor(red: 1.000, green: 1.000, blue: 1.000, alpha: 0.2)
    static let colorTintWhite100 = UIColor(red: 1.000, green: 1.000, blue: 1.000, alpha: 0.15)
    static let colorTintWhite50 = UIColor(red: 1.000, green: 1.000, blue: 1.000, alpha: 0.1)
    static let colorTintWhite25 = UIColor(red: 1.000, green: 1.000, blue: 1.000, alpha: 0.05)
    static let colorReservedDurianYellowElectric = UIColor(red: 0.949, green: 0.961, blue: 0.000, alpha: 1)
    static let colorReservedTrueBlack = UIColor(red: 0.000, green: 0.000, blue: 0.000, alpha: 1)
    static let colorReservedTrueWhite = UIColor(red: 1.000, green: 1.000, blue: 1.000, alpha: 1)
    static let colorRootEggplantPurpleDark7 = UIColor(red: 0.086, green: 0.000, blue: 0.239, alpha: 1)
    static let colorRootEggplantPurpleDark6 = UIColor(red: 0.153, green: 0.008, blue: 0.459, alpha: 1)
    static let colorRootEggplantPurpleDark5 = UIColor(red: 0.247, green: 0.078, blue: 0.604, alpha: 1)
    static let colorRootEggplantPurpleDark4 = UIColor(red: 0.302, green: 0.110, blue: 0.710, alpha: 1)
    static let colorRootEggplantPurpleDark3 = UIColor(red: 0.322, green: 0.161, blue: 0.800, alpha: 1)
    static let colorRootEggplantPurpleDark2 = UIColor(red: 0.373, green: 0.216, blue: 0.835, alpha: 1)
    static let colorRootEggplantPurpleDark1 = UIColor(red: 0.427, green: 0.278, blue: 0.882, alpha: 1)
    static let colorRootEggplantPurpleLight7 = UIColor(red: 0.655, green: 0.600, blue: 0.969, alpha: 1)
    static let colorRootEggplantPurpleLight6 = UIColor(red: 0.765, green: 0.671, blue: 0.969, alpha: 1)
    static let colorRootEggplantPurpleLight5 = UIColor(red: 0.824, green: 0.718, blue: 0.969, alpha: 1)
    static let colorRootEggplantPurpleLight4 = UIColor(red: 0.867, green: 0.765, blue: 0.980, alpha: 1)
    static let colorRootEggplantPurpleLight3 = UIColor(red: 0.902, green: 0.820, blue: 1.000, alpha: 1)
    static let colorRootEggplantPurpleLight2 = UIColor(red: 0.933, green: 0.871, blue: 1.000, alpha: 1)
    static let colorRootEggplantPurpleLight1 = UIColor(red: 0.965, green: 0.929, blue: 1.000, alpha: 1)
    static let colorRootEggplantPurpleBase7 = UIColor(red: 0.357, green: 0.231, blue: 1.000, alpha: 1)
    static let colorRootEggplantPurpleBase6 = UIColor(red: 0.435, green: 0.278, blue: 1.000, alpha: 1)
    static let colorRootEggplantPurpleBase5 = UIColor(red: 0.506, green: 0.329, blue: 1.000, alpha: 1)
    static let colorRootEggplantPurpleBase4 = UIColor(red: 0.588, green: 0.329, blue: 1.000, alpha: 1)
    static let colorRootEggplantPurpleBase3 = UIColor(red: 0.631, green: 0.369, blue: 1.000, alpha: 1)
    static let colorRootEggplantPurpleBase2 = UIColor(red: 0.635, green: 0.439, blue: 1.000, alpha: 1)
    static let colorRootEggplantPurpleBase1 = UIColor(red: 0.647, green: 0.522, blue: 1.000, alpha: 1)
    static let colorRootTomatoRedDark7 = UIColor(red: 0.341, green: 0.004, blue: 0.110, alpha: 1)
    static let colorRootTomatoRedDark6 = UIColor(red: 0.412, green: 0.004, blue: 0.090, alpha: 1)
    static let colorRootTomatoRedDark5 = UIColor(red: 0.490, green: 0.000, blue: 0.004, alpha: 1)
    static let colorRootTomatoRedDark4 = UIColor(red: 0.537, green: 0.000, blue: 0.004, alpha: 1)
    static let colorRootTomatoRedDark3 = UIColor(red: 0.588, green: 0.000, blue: 0.004, alpha: 1)
    static let colorRootTomatoRedDark2 = UIColor(red: 0.651, green: 0.000, blue: 0.004, alpha: 1)
    static let colorRootTomatoRedDark1 = UIColor(red: 0.722, green: 0.024, blue: 0.090, alpha: 1)
    static let colorRootTomatoRedLight7 = UIColor(red: 0.988, green: 0.494, blue: 0.533, alpha: 1)
    static let colorRootTomatoRedLight6 = UIColor(red: 1.000, green: 0.580, blue: 0.604, alpha: 1)
    static let colorRootTomatoRedLight5 = UIColor(red: 1.000, green: 0.651, blue: 0.663, alpha: 1)
    static let colorRootTomatoRedLight4 = UIColor(red: 0.992, green: 0.702, blue: 0.706, alpha: 1)
    static let colorRootTomatoRedLight3 = UIColor(red: 1.000, green: 0.804, blue: 0.804, alpha: 1)
    static let colorRootTomatoRedLight2 = UIColor(red: 1.000, green: 0.882, blue: 0.882, alpha: 1)
    static let colorRootTomatoRedLight1 = UIColor(red: 1.000, green: 0.929, blue: 0.929, alpha: 1)
    static let colorRootTomatoRedBase7 = UIColor(red: 0.851, green: 0.165, blue: 0.220, alpha: 1)
    static let colorRootTomatoRedBase6 = UIColor(red: 0.890, green: 0.173, blue: 0.231, alpha: 1)
    static let colorRootTomatoRedBase5 = UIColor(red: 0.925, green: 0.180, blue: 0.239, alpha: 1)
    static let colorRootTomatoRedBase4 = UIColor(red: 0.925, green: 0.255, blue: 0.263, alpha: 1)
    static let colorRootTomatoRedBase3 = UIColor(red: 0.976, green: 0.275, blue: 0.282, alpha: 1)
    static let colorRootTomatoRedBase2 = UIColor(red: 1.000, green: 0.376, blue: 0.384, alpha: 1)
    static let colorRootTomatoRedBase1 = UIColor(red: 1.000, green: 0.439, blue: 0.447, alpha: 1)
    static let colorRootDurianYellowDark7 = UIColor(red: 0.341, green: 0.247, blue: 0.114, alpha: 1)
    static let colorRootDurianYellowDark6 = UIColor(red: 0.490, green: 0.298, blue: 0.008, alpha: 1)
    static let colorRootDurianYellowDark5 = UIColor(red: 0.620, green: 0.361, blue: 0.000, alpha: 1)
    static let colorRootDurianYellowDark4 = UIColor(red: 0.639, green: 0.388, blue: 0.012, alpha: 1)
    static let colorRootDurianYellowDark3 = UIColor(red: 0.651, green: 0.424, blue: 0.031, alpha: 1)
    static let colorRootDurianYellowDark2 = UIColor(red: 0.651, green: 0.463, blue: 0.020, alpha: 1)
    static let colorRootDurianYellowDark1 = UIColor(red: 0.671, green: 0.518, blue: 0.012, alpha: 1)
    static let colorRootDurianYellowLight7 = UIColor(red: 1.000, green: 0.886, blue: 0.239, alpha: 1)
    static let colorRootDurianYellowLight6 = UIColor(red: 0.992, green: 0.922, blue: 0.157, alpha: 1)
    static let colorRootDurianYellowLight5 = UIColor(red: 0.988, green: 0.957, blue: 0.157, alpha: 1)
    static let colorRootDurianYellowLight4 = UIColor(red: 1.000, green: 0.973, blue: 0.345, alpha: 1)
    static let colorRootDurianYellowLight3 = UIColor(red: 1.000, green: 0.988, blue: 0.388, alpha: 1)
    static let colorRootDurianYellowLight2 = UIColor(red: 1.000, green: 0.992, blue: 0.616, alpha: 1)
    static let colorRootDurianYellowLight1 = UIColor(red: 1.000, green: 0.996, blue: 0.769, alpha: 1)
    static let colorRootDurianYellowBase7 = UIColor(red: 1.000, green: 0.718, blue: 0.000, alpha: 1)
    static let colorRootDurianYellowBase6 = UIColor(red: 1.000, green: 0.765, blue: 0.000, alpha: 1)
    static let colorRootDurianYellowBase5 = UIColor(red: 1.000, green: 0.800, blue: 0.000, alpha: 1)
    static let colorRootDurianYellowBase4 = UIColor(red: 1.000, green: 0.839, blue: 0.000, alpha: 1)
    static let colorRootDurianYellowBase3 = UIColor(red: 1.000, green: 0.871, blue: 0.180, alpha: 1)
    static let colorRootDurianYellowBase2 = UIColor(red: 1.000, green: 0.902, blue: 0.384, alpha: 1)
    static let colorRootDurianYellowBase1 = UIColor(red: 1.000, green: 0.929, blue: 0.557, alpha: 1)
    static let colorRootDragonfruitPinkDark7 = UIColor(red: 0.306, green: 0.027, blue: 0.161, alpha: 1)
    static let colorRootDragonfruitPinkDark6 = UIColor(red: 0.353, green: 0.063, blue: 0.200, alpha: 1)
    static let colorRootDragonfruitPinkDark5 = UIColor(red: 0.431, green: 0.027, blue: 0.220, alpha: 1)
    static let colorRootDragonfruitPinkDark4 = UIColor(red: 0.518, green: 0.000, blue: 0.247, alpha: 1)
    static let colorRootDragonfruitPinkDark3 = UIColor(red: 0.616, green: 0.004, blue: 0.298, alpha: 1)
    static let colorRootDragonfruitPinkDark2 = UIColor(red: 0.714, green: 0.000, blue: 0.341, alpha: 1)
    static let colorRootDragonfruitPinkDark1 = UIColor(red: 0.796, green: 0.008, blue: 0.384, alpha: 1)
    static let colorRootDragonfruitPinkLight7 = UIColor(red: 0.961, green: 0.502, blue: 0.898, alpha: 1)
    static let colorRootDragonfruitPinkLight6 = UIColor(red: 0.965, green: 0.627, blue: 0.918, alpha: 1)
    static let colorRootDragonfruitPinkLight5 = UIColor(red: 1.000, green: 0.780, blue: 0.969, alpha: 1)
    static let colorRootDragonfruitPinkLight4 = UIColor(red: 0.988, green: 0.882, blue: 0.973, alpha: 1)
    static let colorRootDragonfruitPinkLight3 = UIColor(red: 1.000, green: 0.925, blue: 0.988, alpha: 1)
    static let colorRootDragonfruitPinkLight2 = UIColor(red: 1.000, green: 0.957, blue: 0.992, alpha: 1)
    static let colorRootDragonfruitPinkLight1 = UIColor(red: 1.000, green: 0.976, blue: 0.996, alpha: 1)
    static let colorRootDragonfruitPinkBase7 = UIColor(red: 0.816, green: 0.137, blue: 0.380, alpha: 1)
    static let colorRootDragonfruitPinkBase6 = UIColor(red: 0.871, green: 0.169, blue: 0.420, alpha: 1)
    static let colorRootDragonfruitPinkBase5 = UIColor(red: 0.933, green: 0.125, blue: 0.416, alpha: 1)
    static let colorRootDragonfruitPinkBase4 = UIColor(red: 0.957, green: 0.235, blue: 0.494, alpha: 1)
    static let colorRootDragonfruitPinkBase3 = UIColor(red: 0.957, green: 0.349, blue: 0.569, alpha: 1)
    static let colorRootDragonfruitPinkBase2 = UIColor(red: 0.973, green: 0.463, blue: 0.647, alpha: 1)
    static let colorRootDragonfruitPinkBase1 = UIColor(red: 1.000, green: 0.545, blue: 0.706, alpha: 1)
    static let colorRootJadeGreenDark7 = UIColor(red: 0.008, green: 0.239, blue: 0.212, alpha: 1)
    static let colorRootJadeGreenDark6 = UIColor(red: 0.012, green: 0.278, blue: 0.263, alpha: 1)
    static let colorRootJadeGreenDark5 = UIColor(red: 0.012, green: 0.329, blue: 0.286, alpha: 1)
    static let colorRootJadeGreenDark4 = UIColor(red: 0.008, green: 0.380, blue: 0.314, alpha: 1)
    static let colorRootJadeGreenDark3 = UIColor(red: 0.008, green: 0.427, blue: 0.333, alpha: 1)
    static let colorRootJadeGreenDark2 = UIColor(red: 0.008, green: 0.459, blue: 0.357, alpha: 1)
    static let colorRootJadeGreenDark1 = UIColor(red: 0.004, green: 0.510, blue: 0.376, alpha: 1)
    static let colorRootJadeGreenLight7 = UIColor(red: 0.176, green: 0.765, blue: 0.620, alpha: 1)
    static let colorRootJadeGreenLight6 = UIColor(red: 0.243, green: 0.808, blue: 0.682, alpha: 1)
    static let colorRootJadeGreenLight5 = UIColor(red: 0.310, green: 0.859, blue: 0.745, alpha: 1)
    static let colorRootJadeGreenLight4 = UIColor(red: 0.376, green: 0.910, blue: 0.808, alpha: 1)
    static let colorRootJadeGreenLight3 = UIColor(red: 0.722, green: 0.914, blue: 0.875, alpha: 1)
    static let colorRootJadeGreenLight2 = UIColor(red: 0.871, green: 0.945, blue: 0.929, alpha: 1)
    static let colorRootJadeGreenLight1 = UIColor(red: 0.953, green: 0.996, blue: 0.984, alpha: 1)
    static let colorRootJadeGreenBase7 = UIColor(red: 0.067, green: 0.627, blue: 0.490, alpha: 1)
    static let colorRootJadeGreenBase6 = UIColor(red: 0.086, green: 0.671, blue: 0.541, alpha: 1)
    static let colorRootJadeGreenBase5 = UIColor(red: 0.102, green: 0.702, blue: 0.576, alpha: 1)
    static let colorRootJadeGreenBase4 = UIColor(red: 0.118, green: 0.729, blue: 0.612, alpha: 1)
    static let colorRootJadeGreenBase3 = UIColor(red: 0.141, green: 0.776, blue: 0.651, alpha: 1)
    static let colorRootJadeGreenBase2 = UIColor(red: 0.165, green: 0.824, blue: 0.686, alpha: 1)
    static let colorRootJadeGreenBase1 = UIColor(red: 0.188, green: 0.871, blue: 0.729, alpha: 1)
    static let colorRootEnergyBlueDark7 = UIColor(red: 0.000, green: 0.039, blue: 0.231, alpha: 1)
    static let colorRootEnergyBlueDark6 = UIColor(red: 0.000, green: 0.059, blue: 0.365, alpha: 1)
    static let colorRootEnergyBlueDark5 = UIColor(red: 0.000, green: 0.063, blue: 0.541, alpha: 1)
    static let colorRootEnergyBlueDark4 = UIColor(red: 0.000, green: 0.106, blue: 0.647, alpha: 1)
    static let colorRootEnergyBlueDark3 = UIColor(red: 0.000, green: 0.188, blue: 0.671, alpha: 1)
    static let colorRootEnergyBlueDark2 = UIColor(red: 0.043, green: 0.259, blue: 0.722, alpha: 1)
    static let colorRootEnergyBlueDark1 = UIColor(red: 0.047, green: 0.314, blue: 0.812, alpha: 1)
    static let colorRootEnergyBlueLight7 = UIColor(red: 0.620, green: 0.796, blue: 0.969, alpha: 1)
    static let colorRootEnergyBlueLight6 = UIColor(red: 0.694, green: 0.831, blue: 0.988, alpha: 1)
    static let colorRootEnergyBlueLight5 = UIColor(red: 0.729, green: 0.867, blue: 1.000, alpha: 1)
    static let colorRootEnergyBlueLight4 = UIColor(red: 0.776, green: 0.898, blue: 1.000, alpha: 1)
    static let colorRootEnergyBlueLight3 = UIColor(red: 0.820, green: 0.918, blue: 1.000, alpha: 1)
    static let colorRootEnergyBlueLight2 = UIColor(red: 0.863, green: 0.941, blue: 0.969, alpha: 1)
    static let colorRootEnergyBlueLight1 = UIColor(red: 0.914, green: 0.949, blue: 0.961, alpha: 1)
    static let colorRootEnergyBlueBase7 = UIColor(red: 0.027, green: 0.361, blue: 0.898, alpha: 1)
    static let colorRootEnergyBlueBase6 = UIColor(red: 0.031, green: 0.396, blue: 0.988, alpha: 1)
    static let colorRootEnergyBlueBase5 = UIColor(red: 0.008, green: 0.455, blue: 1.000, alpha: 1)
    static let colorRootEnergyBlueBase4 = UIColor(red: 0.008, green: 0.498, blue: 1.000, alpha: 1)
    static let colorRootEnergyBlueBase3 = UIColor(red: 0.031, green: 0.549, blue: 1.000, alpha: 1)
    static let colorRootEnergyBlueBase2 = UIColor(red: 0.031, green: 0.596, blue: 1.000, alpha: 1)
    static let colorRootEnergyBlueBase1 = UIColor(red: 0.071, green: 0.675, blue: 1.000, alpha: 1)
    static let colorRootChiveGreenDark7 = UIColor(red: 0.122, green: 0.310, blue: 0.176, alpha: 1)
    static let colorRootChiveGreenDark6 = UIColor(red: 0.122, green: 0.310, blue: 0.176, alpha: 1)
    static let colorRootChiveGreenDark5 = UIColor(red: 0.153, green: 0.349, blue: 0.212, alpha: 1)
    static let colorRootChiveGreenDark4 = UIColor(red: 0.169, green: 0.400, blue: 0.200, alpha: 1)
    static let colorRootChiveGreenDark3 = UIColor(red: 0.220, green: 0.431, blue: 0.235, alpha: 1)
    static let colorRootChiveGreenDark2 = UIColor(red: 0.259, green: 0.490, blue: 0.282, alpha: 1)
    static let colorRootChiveGreenDark1 = UIColor(red: 0.298, green: 0.561, blue: 0.349, alpha: 1)
    static let colorRootChiveGreenLight7 = UIColor(red: 0.525, green: 0.749, blue: 0.592, alpha: 1)
    static let colorRootChiveGreenLight6 = UIColor(red: 0.553, green: 0.800, blue: 0.624, alpha: 1)
    static let colorRootChiveGreenLight5 = UIColor(red: 0.557, green: 0.831, blue: 0.635, alpha: 1)
    static let colorRootChiveGreenLight4 = UIColor(red: 0.573, green: 0.878, blue: 0.663, alpha: 1)
    static let colorRootChiveGreenLight3 = UIColor(red: 0.651, green: 0.906, blue: 0.725, alpha: 1)
    static let colorRootChiveGreenLight2 = UIColor(red: 0.714, green: 0.941, blue: 0.780, alpha: 1)
    static let colorRootChiveGreenLight1 = UIColor(red: 0.843, green: 0.980, blue: 0.882, alpha: 1)
    static let colorRootChiveGreenBase7 = UIColor(red: 0.196, green: 0.529, blue: 0.290, alpha: 1)
    static let colorRootChiveGreenBase6 = UIColor(red: 0.196, green: 0.580, blue: 0.306, alpha: 1)
    static let colorRootChiveGreenBase5 = UIColor(red: 0.196, green: 0.612, blue: 0.314, alpha: 1)
    static let colorRootChiveGreenBase4 = UIColor(red: 0.176, green: 0.639, blue: 0.310, alpha: 1)
    static let colorRootChiveGreenBase3 = UIColor(red: 0.188, green: 0.678, blue: 0.329, alpha: 1)
    static let colorRootChiveGreenBase2 = UIColor(red: 0.200, green: 0.722, blue: 0.349, alpha: 1)
    static let colorRootChiveGreenBase1 = UIColor(red: 0.212, green: 0.761, blue: 0.357, alpha: 1)
    static let colorRootFlowTealDark7 = UIColor(red: 0.024, green: 0.220, blue: 0.278, alpha: 1)
    static let colorRootFlowTealDark6 = UIColor(red: 0.039, green: 0.349, blue: 0.447, alpha: 1)
    static let colorRootFlowTealDark5 = UIColor(red: 0.031, green: 0.388, blue: 0.498, alpha: 1)
    static let colorRootFlowTealDark4 = UIColor(red: 0.000, green: 0.420, blue: 0.549, alpha: 1)
    static let colorRootFlowTealDark3 = UIColor(red: 0.035, green: 0.482, blue: 0.620, alpha: 1)
    static let colorRootFlowTealDark2 = UIColor(red: 0.082, green: 0.545, blue: 0.686, alpha: 1)
    static let colorRootFlowTealDark1 = UIColor(red: 0.251, green: 0.710, blue: 0.851, alpha: 1)
    static let colorRootFlowTealLight7 = UIColor(red: 0.318, green: 0.804, blue: 0.957, alpha: 1)
    static let colorRootFlowTealLight6 = UIColor(red: 0.455, green: 0.839, blue: 0.965, alpha: 1)
    static let colorRootFlowTealLight5 = UIColor(red: 0.502, green: 0.875, blue: 0.992, alpha: 1)
    static let colorRootFlowTealLight4 = UIColor(red: 0.580, green: 0.898, blue: 1.000, alpha: 1)
    static let colorRootFlowTealLight3 = UIColor(red: 0.663, green: 0.922, blue: 1.000, alpha: 1)
    static let colorRootFlowTealLight2 = UIColor(red: 0.769, green: 0.949, blue: 1.000, alpha: 1)
    static let colorRootFlowTealLight1 = UIColor(red: 0.918, green: 0.980, blue: 1.000, alpha: 1)
    static let colorRootFlowTealBase7 = UIColor(red: 0.000, green: 0.608, blue: 0.800, alpha: 1)
    static let colorRootFlowTealBase6 = UIColor(red: 0.000, green: 0.647, blue: 0.851, alpha: 1)
    static let colorRootFlowTealBase5 = UIColor(red: 0.000, green: 0.675, blue: 0.890, alpha: 1)
    static let colorRootFlowTealBase4 = UIColor(red: 0.000, green: 0.698, blue: 0.918, alpha: 1)
    static let colorRootFlowTealBase3 = UIColor(red: 0.000, green: 0.722, blue: 0.949, alpha: 1)
    static let colorRootFlowTealBase2 = UIColor(red: 0.000, green: 0.753, blue: 0.988, alpha: 1)
    static let colorRootFlowTealBase1 = UIColor(red: 0.000, green: 0.800, blue: 1.000, alpha: 1)
    static let colorRootMandarinOrangeDark7 = UIColor(red: 0.349, green: 0.125, blue: 0.027, alpha: 1)
    static let colorRootMandarinOrangeDark6 = UIColor(red: 0.522, green: 0.125, blue: 0.027, alpha: 1)
    static let colorRootMandarinOrangeDark5 = UIColor(red: 0.608, green: 0.118, blue: 0.000, alpha: 1)
    static let colorRootMandarinOrangeDark4 = UIColor(red: 0.686, green: 0.133, blue: 0.000, alpha: 1)
    static let colorRootMandarinOrangeDark3 = UIColor(red: 0.753, green: 0.145, blue: 0.000, alpha: 1)
    static let colorRootMandarinOrangeDark2 = UIColor(red: 0.808, green: 0.157, blue: 0.000, alpha: 1)
    static let colorRootMandarinOrangeDark1 = UIColor(red: 0.886, green: 0.200, blue: 0.035, alpha: 1)
    static let colorRootMandarinOrangeLight7 = UIColor(red: 0.980, green: 0.439, blue: 0.314, alpha: 1)
    static let colorRootMandarinOrangeLight6 = UIColor(red: 1.000, green: 0.471, blue: 0.349, alpha: 1)
    static let colorRootMandarinOrangeLight5 = UIColor(red: 1.000, green: 0.525, blue: 0.412, alpha: 1)
    static let colorRootMandarinOrangeLight4 = UIColor(red: 1.000, green: 0.573, blue: 0.471, alpha: 1)
    static let colorRootMandarinOrangeLight3 = UIColor(red: 1.000, green: 0.627, blue: 0.541, alpha: 1)
    static let colorRootMandarinOrangeLight2 = UIColor(red: 1.000, green: 0.824, blue: 0.722, alpha: 1)
    static let colorRootMandarinOrangeLight1 = UIColor(red: 0.996, green: 0.937, blue: 0.859, alpha: 1)
    static let colorRootMandarinOrangeBase7 = UIColor(red: 0.898, green: 0.239, blue: 0.078, alpha: 1)
    static let colorRootMandarinOrangeBase6 = UIColor(red: 0.969, green: 0.259, blue: 0.086, alpha: 1)
    static let colorRootMandarinOrangeBase5 = UIColor(red: 1.000, green: 0.259, blue: 0.078, alpha: 1)
    static let colorRootMandarinOrangeBase4 = UIColor(red: 1.000, green: 0.314, blue: 0.149, alpha: 1)
    static let colorRootMandarinOrangeBase3 = UIColor(red: 1.000, green: 0.373, blue: 0.180, alpha: 1)
    static let colorRootMandarinOrangeBase2 = UIColor(red: 1.000, green: 0.447, blue: 0.278, alpha: 1)
    static let colorRootMandarinOrangeBase1 = UIColor(red: 1.000, green: 0.510, blue: 0.388, alpha: 1)
    static let colorShadeCoolDark7 = UIColor(red: 0.027, green: 0.063, blue: 0.102, alpha: 1)
    static let colorShadeCoolDark6 = UIColor(red: 0.094, green: 0.141, blue: 0.196, alpha: 1)
    static let colorShadeCoolDark5 = UIColor(red: 0.129, green: 0.184, blue: 0.239, alpha: 1)
    static let colorShadeCoolDark4 = UIColor(red: 0.169, green: 0.224, blue: 0.282, alpha: 1)
    static let colorShadeCoolDark3 = UIColor(red: 0.227, green: 0.298, blue: 0.376, alpha: 1)
    static let colorShadeCoolDark2 = UIColor(red: 0.271, green: 0.345, blue: 0.431, alpha: 1)
    static let colorShadeCoolDark1 = UIColor(red: 0.298, green: 0.376, blue: 0.471, alpha: 1)
    static let colorShadeCoolLight7 = UIColor(red: 0.780, green: 0.808, blue: 0.871, alpha: 1)
    static let colorShadeCoolLight6 = UIColor(red: 0.827, green: 0.855, blue: 0.922, alpha: 1)
    static let colorShadeCoolLight5 = UIColor(red: 0.871, green: 0.894, blue: 0.953, alpha: 1)
    static let colorShadeCoolLight4 = UIColor(red: 0.894, green: 0.918, blue: 0.961, alpha: 1)
    static let colorShadeCoolLight3 = UIColor(red: 0.910, green: 0.933, blue: 0.973, alpha: 1)
    static let colorShadeCoolLight2 = UIColor(red: 0.933, green: 0.949, blue: 0.984, alpha: 1)
    static let colorShadeCoolLight1 = UIColor(red: 0.965, green: 0.976, blue: 0.988, alpha: 1)
    static let colorShadeCoolBase7 = UIColor(red: 0.322, green: 0.400, blue: 0.490, alpha: 1)
    static let colorShadeCoolBase6 = UIColor(red: 0.388, green: 0.451, blue: 0.545, alpha: 1)
    static let colorShadeCoolBase5 = UIColor(red: 0.459, green: 0.510, blue: 0.588, alpha: 1)
    static let colorShadeCoolBase4 = UIColor(red: 0.573, green: 0.600, blue: 0.682, alpha: 1)
    static let colorShadeCoolBase3 = UIColor(red: 0.631, green: 0.659, blue: 0.737, alpha: 1)
    static let colorShadeCoolBase2 = UIColor(red: 0.667, green: 0.694, blue: 0.769, alpha: 1)
    static let colorShadeCoolBase1 = UIColor(red: 0.722, green: 0.749, blue: 0.820, alpha: 1)
    static let colorShadeNeutralDark7 = UIColor(red: 0.067, green: 0.067, blue: 0.067, alpha: 1)
    static let colorShadeNeutralDark6 = UIColor(red: 0.098, green: 0.094, blue: 0.102, alpha: 1)
    static let colorShadeNeutralDark5 = UIColor(red: 0.145, green: 0.145, blue: 0.145, alpha: 1)
    static let colorShadeNeutralDark4 = UIColor(red: 0.200, green: 0.200, blue: 0.200, alpha: 1)
    static let colorShadeNeutralDark3 = UIColor(red: 0.231, green: 0.231, blue: 0.231, alpha: 1)
    static let colorShadeNeutralDark2 = UIColor(red: 0.259, green: 0.259, blue: 0.259, alpha: 1)
    static let colorShadeNeutralDark1 = UIColor(red: 0.302, green: 0.302, blue: 0.302, alpha: 1)
    static let colorShadeNeutralLight7 = UIColor(red: 0.733, green: 0.733, blue: 0.733, alpha: 1)
    static let colorShadeNeutralLight6 = UIColor(red: 0.765, green: 0.765, blue: 0.765, alpha: 1)
    static let colorShadeNeutralLight5 = UIColor(red: 0.800, green: 0.800, blue: 0.800, alpha: 1)
    static let colorShadeNeutralLight4 = UIColor(red: 0.886, green: 0.886, blue: 0.886, alpha: 1)
    static let colorShadeNeutralLight3 = UIColor(red: 0.886, green: 0.886, blue: 0.886, alpha: 1)
    static let colorShadeNeutralLight2 = UIColor(red: 0.953, green: 0.953, blue: 0.953, alpha: 1)
    static let colorShadeNeutralLight1 = UIColor(red: 0.980, green: 0.980, blue: 0.980, alpha: 1)
    static let colorShadeNeutralBase7 = UIColor(red: 0.404, green: 0.400, blue: 0.408, alpha: 1)
    static let colorShadeNeutralBase6 = UIColor(red: 0.467, green: 0.467, blue: 0.467, alpha: 1)
    static let colorShadeNeutralBase5 = UIColor(red: 0.529, green: 0.529, blue: 0.529, alpha: 1)
    static let colorShadeNeutralBase4 = UIColor(red: 0.600, green: 0.600, blue: 0.600, alpha: 1)
    static let colorShadeNeutralBase3 = UIColor(red: 0.620, green: 0.620, blue: 0.620, alpha: 1)
    static let colorShadeNeutralBase2 = UIColor(red: 0.655, green: 0.655, blue: 0.655, alpha: 1)
    static let colorShadeNeutralBase1 = UIColor(red: 0.690, green: 0.690, blue: 0.690, alpha: 1)
    static let colorPromo300BgDarker = colorRootJadeGreenDark4
    static let colorPromo300BgDark = colorRootJadeGreenDark1
    static let colorPromo300BgDefault = colorRootJadeGreenBase7
    static let colorPromo300BgLight = colorRootJadeGreenBase3
    static let colorPromo300BgLighter = colorRootJadeGreenLight5
    static let colorPromo300FgHairline = colorRootJadeGreenDark3
    static let colorPromo300FgMinor = colorRootJadeGreenLight1
    static let colorPromo300FgDefault = colorReservedTrueWhite
    static let colorPromo200BgDarker = colorRootMandarinOrangeDark2
    static let colorPromo200BgDark = colorRootMandarinOrangeBase3
    static let colorPromo200BgDefault = colorRootMandarinOrangeBase1
    static let colorPromo200BgLight = colorRootMandarinOrangeLight3
    static let colorPromo200BgLighter = colorRootMandarinOrangeLight2
    static let colorPromo200FgHairline = colorRootMandarinOrangeLight2
    static let colorPromo200FgMinor = colorRootMandarinOrangeLight1
    static let colorPromo200FgDefault = colorReservedTrueWhite
    static let colorPromo100BgDarker = colorRootMandarinOrangeDark7
    static let colorPromo100BgDark = colorRootMandarinOrangeDark6
    static let colorPromo100BgDefault = colorRootMandarinOrangeDark3
    static let colorPromo100BgLight = colorRootMandarinOrangeBase7
    static let colorPromo100BgLighter = colorRootMandarinOrangeBase1
    static let colorPromo100FgHairline = colorRootMandarinOrangeBase3
    static let colorPromo100FgMinor = colorRootMandarinOrangeLight1
    static let colorPromo100FgDefault = colorReservedTrueWhite
    static let colorBookmarkBgSelected = colorRootTomatoRedLight7
    static let colorNotificationFg = colorReservedTrueWhite
    static let colorInput200HairlineCritical = colorRootTomatoRedDark4
    static let colorInput200HairlineDisabled = colorShadeNeutralBase4
    static let colorInput200FgCritical = colorRootTomatoRedLight3
    static let colorInput200FgDisabled = colorShadeNeutralLight4
    static let colorInput200FgPlaceholder = colorShadeCoolBase3
    static let colorInput200BgCritical = colorRootTomatoRedDark7
    static let colorInput200BgDisabled = colorShadeNeutralBase7
    static let colorInput100HairlineCritical = colorRootTomatoRedDark4
    static let colorInput100HairlineDisabled = colorShadeNeutralBase4
    static let colorInput100FgCritical = colorRootTomatoRedLight3
    static let colorInput100FgDisabled = colorShadeNeutralLight4
    static let colorInput100BgCritical = colorRootTomatoRedDark7
    static let colorInput100BgDisabled = colorShadeNeutralBase7
    static let colorBackdrop100Bg = colorTintBlack800
    static let colorBackdrop50Bg = colorTintBlack50
    static let colorSidebarHairline = colorRootChiveGreenBase7
    static let colorSidebarFg400 = colorShadeNeutralLight1
    static let colorSidebarFg300 = colorRootChiveGreenDark7
    static let colorSidebarFg200 = colorRootChiveGreenDark7
    static let colorSidebarFg100Subdued = colorRootChiveGreenLight3
    static let colorSidebarFg100Selected = colorShadeNeutralLight1
    static let colorSidebarFg100Default = colorShadeNeutralLight1
    static let colorSidebarBg400 = colorRootChiveGreenDark2
    static let colorSidebarBg100 = colorRootChiveGreenDark7
    static let colorNavbarFgHighlight = colorRootEggplantPurpleLight6
    static let colorAtcLargeFgDisabled = colorRootFlowTealDark5
    static let colorAtcMiniHairline = colorTintWhite350
    static let colorAtcMiniFgDisabled = colorShadeCoolBase4
    static let colorAtcMiniFgAdded = colorReservedTrueWhite
    static let colorAtcMiniFgDefault = colorReservedTrueWhite
    static let colorAtcMiniBgDisabled = colorShadeCoolDark3
    static let colorAtcMiniBgDefault = colorShadeCoolDark3
    static let colorPricingHairline = colorReservedTrueWhite
    static let colorPricingTxt = colorRootTomatoRedLight7
    static let colorPricingFg = colorReservedTrueWhite
    static let colorPricingBg = colorRootTomatoRedBase4
    static let colorHighlightTxt = colorRootEggplantPurpleBase1
    static let colorHighlightHairline = colorRootEggplantPurpleBase6
    static let colorHighlightFg = colorRootEggplantPurpleDark6
    static let colorHighlightBg = colorRootEggplantPurpleLight2
    static let colorWarningTxt = colorRootDurianYellowLight7
    static let colorWarningHairline = colorRootDurianYellowDark4
    static let colorWarningFg = colorRootDurianYellowDark6
    static let colorWarningBg = colorRootDurianYellowLight2
    static let colorCriticalTxt = colorRootTomatoRedBase3
    static let colorCriticalHairline = colorRootTomatoRedLight6
    static let colorCriticalFg = colorReservedTrueWhite
    static let colorCriticalBg = colorRootTomatoRedBase3
    static let colorSuccessHairline = colorRootChiveGreenBase4
    static let colorSuccessFg = colorRootChiveGreenDark7
    static let colorSuccessBg = colorRootChiveGreenLight1
    static let colorLinkBase1 = colorRootEnergyBlueLight5
    static let colorBtnDisabledHairline = colorShadeCoolBase5
    static let colorBtnDisabledFgMinor = colorShadeCoolBase6
    static let colorBtnDisabledFgDefault = colorShadeCoolBase4
    static let colorBtnDisabledBg = colorShadeCoolDark3
    static let colorBtnCriticalBehavior = {style.filter.darken.1}
    static let colorBtnCriticalHairline = colorRootTomatoRedDark1
    static let colorBtnCriticalFgMinor = colorRootTomatoRedLight3
    static let colorBtnCriticalFgDefault = colorReservedTrueWhite
    static let colorBtnCriticalBg = colorRootTomatoRedBase4
    static let colorBtnConfirmationBehavior = {style.filter.darken.1}
    static let colorBtnTertiaryBehavior = {style.filter.darken.1}
    static let colorBtnTertiaryHairline = colorShadeCoolBase7
    static let colorBtnTertiaryBg = colorShadeCoolDark5
    static let colorBtnSecondaryBehavior = {style.filter.lighten.1}
    static let colorBtnSecondaryHairline = colorRootChiveGreenLight7
    static let colorBtnSecondaryFgMinor = colorRootChiveGreenBase7
    static let colorBtnSecondaryFgDefault = colorRootChiveGreenDark6
    static let colorBtnSecondaryBg = colorRootChiveGreenLight2
    static let colorBtnPrimaryBehavior = {style.filter.lighten.1}
    static let colorBtnPrimaryHairline = colorRootFlowTealLight7
    static let colorBtnPrimaryFgMinor = colorShadeCoolLight1
    static let colorBtnPrimaryFgDefault = colorReservedTrueWhite
    static let colorSurface600Hairline = colorShadeCoolLight4
    static let colorSurface600FgMinor = colorShadeCoolBase5
    static let colorSurface600FgDefault = colorShadeCoolDark5
    static let colorSurface600Bg = colorReservedTrueWhite
    static let colorSurface500Hairline = colorShadeCoolLight5
    static let colorSurface500FgMinor = colorShadeCoolBase5
    static let colorSurface500FgDefault = colorShadeCoolDark5
    static let colorSurface500Bg = colorShadeCoolLight1
    static let colorSurface400Hairline = colorShadeCoolLight6
    static let colorSurface400FgMinor = colorShadeCoolBase5
    static let colorSurface400FgDefault = colorShadeCoolDark5
    static let colorSurface400Bg = colorShadeCoolLight2
    static let colorSurface300Hairline = colorShadeCoolBase6
    static let colorSurface300FgMinor = colorShadeCoolLight1
    static let colorSurface300FgDefault = colorShadeCoolLight1
    static let colorSurface300Bg = colorShadeCoolDark5
    static let colorSurface200Hairline = colorShadeCoolBase7
    static let colorSurface200FgMinor = colorShadeCoolLight1
    static let colorSurface200FgDefault = colorShadeCoolLight1
    static let colorSurface200Bg = colorShadeCoolDark6
    static let colorSurface100Hairline = colorShadeCoolBase7
    static let colorSurface100FgMinor = colorShadeCoolLight7
    static let colorSurface100FgDefault = colorShadeCoolLight1
    static let colorSurface100Bg = colorShadeCoolDark7
    static let colorTertiaryElectric1 = colorReservedDurianYellowElectric
    static let colorTertiaryDark4 = colorRootChiveGreenDark6
    static let colorTertiaryDark3 = colorRootJadeGreenDark6
    static let colorTertiaryDark2 = colorRootMandarinOrangeDark3
    static let colorTertiaryDark1 = colorRootTomatoRedDark4
    static let colorTertiaryLight5 = colorRootDurianYellowLight2
    static let colorTertiaryLight4 = colorRootChiveGreenLight3
    static let colorTertiaryLight3 = colorRootJadeGreenLight2
    static let colorTertiaryLight2 = colorRootMandarinOrangeLight2
    static let colorTertiaryLight1 = colorRootTomatoRedLight1
    static let colorTertiaryBase5 = colorRootDurianYellowBase4
    static let colorTertiaryBase4 = colorRootChiveGreenBase4
    static let colorTertiaryBase3 = colorRootJadeGreenBase4
    static let colorTertiaryBase2 = colorRootMandarinOrangeBase4
    static let colorTertiaryBase1 = colorRootTomatoRedBase4
    static let colorSecondaryDark3 = colorRootDragonfruitPinkDark4
    static let colorSecondaryDark2 = colorRootEggplantPurpleDark7
    static let colorSecondaryDark1 = colorRootEnergyBlueDark6
    static let colorSecondaryLight3 = colorRootDragonfruitPinkLight4
    static let colorSecondaryLight2 = colorRootEggplantPurpleLight2
    static let colorSecondaryLight1 = colorRootEnergyBlueLight4
    static let colorSecondaryBase3 = colorRootDragonfruitPinkBase4
    static let colorSecondaryBase2 = colorRootEggplantPurpleBase4
    static let colorSecondaryBase1 = colorRootEnergyBlueBase4
    static let colorPrimary5 = colorShadeNeutralDark7
    static let colorPrimary4 = colorReservedTrueWhite
    static let colorPrimary3 = colorShadeCoolLight2
    static let colorPrimary2 = colorRootChiveGreenBase1
    static let colorPrimary1 = colorRootChiveGreenDark7
    static let colorNotificationBg = colorTertiaryBase1
    static let colorInput200IconActive = colorSurface100FgDefault
    static let colorInput200IconDefault = colorSurface100FgMinor
    static let colorInput200HairlineActive = colorSecondaryLight1
    static let colorInput200HairlineDefault = colorSurface200Hairline
    static let colorInput200FgDefault = colorSurface100FgDefault
    static let colorInput200BgActive = colorSurface100Bg
    static let colorInput200BgDefault = colorSurface200Bg
    static let colorInput100IconActive = colorSurface100FgDefault
    static let colorInput100IconDefault = colorSurface100FgMinor
    static let colorInput100HairlineActive = colorSurface100FgDefault
    static let colorInput100HairlineDefault = colorSurface100Hairline
    static let colorInput100FgPlaceholder = colorSurface100FgMinor
    static let colorInput100FgDefault = colorSurface100FgDefault
    static let colorInput100BgActive = colorSurface300Bg
    static let colorInput100BgDefault = colorSurface200Bg
    static let colorSidebarLogo = colorPrimary4
    static let colorSidebarBg300 = colorTertiaryElectric1
    static let colorSidebarBg200 = colorTertiaryElectric1
    static let colorNavbarLogo = colorSecondaryLight1
    static let colorNavbarDivider = colorSurface100Hairline
    static let colorNavbarHairline = colorSurface100Hairline
    static let colorNavbarFgSelected = colorSecondaryBase1
    static let colorNavbarFgDefault = colorSecondaryLight1
    static let colorNavbarBgHighlight = colorSurface100Bg
    static let colorNavbarBgDefault = colorSurface100Bg
    static let colorAtcLargeFgDefault = colorBtnPrimaryFgDefault
    static let colorAtcMiniBgAdded = colorBtnSecondaryBg
    static let colorSuccessTxt = colorTertiaryLight4
    static let colorBtnConfirmationHairline = colorBtnPrimaryHairline
    static let colorBtnConfirmationFgMinor = colorBtnPrimaryFgMinor
    static let colorBtnConfirmationFgDefault = colorBtnPrimaryFgDefault
    static let colorBtnTertiaryFgMinor = colorSurface200FgMinor
    static let colorBtnTertiaryFgDefault = colorSurface200FgDefault
    static let colorBtnPrimaryBg = colorPrimary2
    static let colorAtcLargeBgDisabled = colorBtnPrimaryBg
    static let colorAtcLargeBgDefault = colorBtnPrimaryBg
    static let colorBtnConfirmationBg = colorBtnPrimaryBg
}
