module.exports = {
  "primary": {
    "1": "var(--color-primary-1)",
    "2": "var(--color-primary-2)",
    "3": "var(--color-primary-3)",
    "4": "var(--color-primary-4)",
    "5": "var(--color-primary-5)"
  },
  "secondary": {
    "base": {
      "1": "var(--color-secondary-base-1)",
      "2": "var(--color-secondary-base-2)",
      "3": "var(--color-secondary-base-3)"
    },
    "light": {
      "1": "var(--color-secondary-light-1)",
      "2": "var(--color-secondary-light-2)",
      "3": "var(--color-secondary-light-3)"
    },
    "dark": {
      "1": "var(--color-secondary-dark-1)",
      "2": "var(--color-secondary-dark-2)",
      "3": "var(--color-secondary-dark-3)"
    }
  },
  "tertiary": {
    "base": {
      "1": "var(--color-tertiary-base-1)",
      "2": "var(--color-tertiary-base-2)",
      "3": "var(--color-tertiary-base-3)",
      "4": "var(--color-tertiary-base-4)",
      "5": "var(--color-tertiary-base-5)"
    },
    "light": {
      "1": "var(--color-tertiary-light-1)",
      "2": "var(--color-tertiary-light-2)",
      "3": "var(--color-tertiary-light-3)",
      "4": "var(--color-tertiary-light-4)",
      "5": "var(--color-tertiary-light-5)"
    },
    "dark": {
      "1": "var(--color-tertiary-dark-1)",
      "2": "var(--color-tertiary-dark-2)",
      "3": "var(--color-tertiary-dark-3)",
      "4": "var(--color-tertiary-dark-4)"
    },
    "electric": {
      "1": "var(--color-tertiary-electric-1)"
    }
  },
  "shade": {
    "neutral": {
      "base": {
        "1": "var(--color-shade-neutral-base-1)",
        "2": "var(--color-shade-neutral-base-2)",
        "3": "var(--color-shade-neutral-base-3)",
        "4": "var(--color-shade-neutral-base-4)",
        "5": "var(--color-shade-neutral-base-5)",
        "6": "var(--color-shade-neutral-base-6)",
        "7": "var(--color-shade-neutral-base-7)"
      },
      "light": {
        "1": "var(--color-shade-neutral-light-1)",
        "2": "var(--color-shade-neutral-light-2)",
        "3": "var(--color-shade-neutral-light-3)",
        "4": "var(--color-shade-neutral-light-4)",
        "5": "var(--color-shade-neutral-light-5)",
        "6": "var(--color-shade-neutral-light-6)",
        "7": "var(--color-shade-neutral-light-7)"
      },
      "dark": {
        "1": "var(--color-shade-neutral-dark-1)",
        "2": "var(--color-shade-neutral-dark-2)",
        "3": "var(--color-shade-neutral-dark-3)",
        "4": "var(--color-shade-neutral-dark-4)",
        "5": "var(--color-shade-neutral-dark-5)",
        "6": "var(--color-shade-neutral-dark-6)",
        "7": "var(--color-shade-neutral-dark-7)"
      }
    },
    "cool": {
      "base": {
        "1": "var(--color-shade-cool-base-1)",
        "2": "var(--color-shade-cool-base-2)",
        "3": "var(--color-shade-cool-base-3)",
        "4": "var(--color-shade-cool-base-4)",
        "5": "var(--color-shade-cool-base-5)",
        "6": "var(--color-shade-cool-base-6)",
        "7": "var(--color-shade-cool-base-7)"
      },
      "light": {
        "1": "var(--color-shade-cool-light-1)",
        "2": "var(--color-shade-cool-light-2)",
        "3": "var(--color-shade-cool-light-3)",
        "4": "var(--color-shade-cool-light-4)",
        "5": "var(--color-shade-cool-light-5)",
        "6": "var(--color-shade-cool-light-6)",
        "7": "var(--color-shade-cool-light-7)"
      },
      "dark": {
        "1": "var(--color-shade-cool-dark-1)",
        "2": "var(--color-shade-cool-dark-2)",
        "3": "var(--color-shade-cool-dark-3)",
        "4": "var(--color-shade-cool-dark-4)",
        "5": "var(--color-shade-cool-dark-5)",
        "6": "var(--color-shade-cool-dark-6)",
        "7": "var(--color-shade-cool-dark-7)"
      }
    }
  },
  "root": {
    "mandarin": {
      "orange": {
        "base": {
          "1": "var(--color-root-mandarin-orange-base-1)",
          "2": "var(--color-root-mandarin-orange-base-2)",
          "3": "var(--color-root-mandarin-orange-base-3)",
          "4": "var(--color-root-mandarin-orange-base-4)",
          "5": "var(--color-root-mandarin-orange-base-5)",
          "6": "var(--color-root-mandarin-orange-base-6)",
          "7": "var(--color-root-mandarin-orange-base-7)"
        },
        "light": {
          "1": "var(--color-root-mandarin-orange-light-1)",
          "2": "var(--color-root-mandarin-orange-light-2)",
          "3": "var(--color-root-mandarin-orange-light-3)",
          "4": "var(--color-root-mandarin-orange-light-4)",
          "5": "var(--color-root-mandarin-orange-light-5)",
          "6": "var(--color-root-mandarin-orange-light-6)",
          "7": "var(--color-root-mandarin-orange-light-7)"
        },
        "dark": {
          "1": "var(--color-root-mandarin-orange-dark-1)",
          "2": "var(--color-root-mandarin-orange-dark-2)",
          "3": "var(--color-root-mandarin-orange-dark-3)",
          "4": "var(--color-root-mandarin-orange-dark-4)",
          "5": "var(--color-root-mandarin-orange-dark-5)",
          "6": "var(--color-root-mandarin-orange-dark-6)",
          "7": "var(--color-root-mandarin-orange-dark-7)"
        }
      }
    },
    "flow": {
      "teal": {
        "base": {
          "1": "var(--color-root-flow-teal-base-1)",
          "2": "var(--color-root-flow-teal-base-2)",
          "3": "var(--color-root-flow-teal-base-3)",
          "4": "var(--color-root-flow-teal-base-4)",
          "5": "var(--color-root-flow-teal-base-5)",
          "6": "var(--color-root-flow-teal-base-6)",
          "7": "var(--color-root-flow-teal-base-7)"
        },
        "light": {
          "1": "var(--color-root-flow-teal-light-1)",
          "2": "var(--color-root-flow-teal-light-2)",
          "3": "var(--color-root-flow-teal-light-3)",
          "4": "var(--color-root-flow-teal-light-4)",
          "5": "var(--color-root-flow-teal-light-5)",
          "6": "var(--color-root-flow-teal-light-6)",
          "7": "var(--color-root-flow-teal-light-7)"
        },
        "dark": {
          "1": "var(--color-root-flow-teal-dark-1)",
          "2": "var(--color-root-flow-teal-dark-2)",
          "3": "var(--color-root-flow-teal-dark-3)",
          "4": "var(--color-root-flow-teal-dark-4)",
          "5": "var(--color-root-flow-teal-dark-5)",
          "6": "var(--color-root-flow-teal-dark-6)",
          "7": "var(--color-root-flow-teal-dark-7)"
        }
      }
    },
    "chive": {
      "green": {
        "base": {
          "1": "var(--color-root-chive-green-base-1)",
          "2": "var(--color-root-chive-green-base-2)",
          "3": "var(--color-root-chive-green-base-3)",
          "4": "var(--color-root-chive-green-base-4)",
          "5": "var(--color-root-chive-green-base-5)",
          "6": "var(--color-root-chive-green-base-6)",
          "7": "var(--color-root-chive-green-base-7)"
        },
        "light": {
          "1": "var(--color-root-chive-green-light-1)",
          "2": "var(--color-root-chive-green-light-2)",
          "3": "var(--color-root-chive-green-light-3)",
          "4": "var(--color-root-chive-green-light-4)",
          "5": "var(--color-root-chive-green-light-5)",
          "6": "var(--color-root-chive-green-light-6)",
          "7": "var(--color-root-chive-green-light-7)"
        },
        "dark": {
          "1": "var(--color-root-chive-green-dark-1)",
          "2": "var(--color-root-chive-green-dark-2)",
          "3": "var(--color-root-chive-green-dark-3)",
          "4": "var(--color-root-chive-green-dark-4)",
          "5": "var(--color-root-chive-green-dark-5)",
          "6": "var(--color-root-chive-green-dark-6)",
          "7": "var(--color-root-chive-green-dark-7)"
        }
      }
    },
    "energy": {
      "blue": {
        "base": {
          "1": "var(--color-root-energy-blue-base-1)",
          "2": "var(--color-root-energy-blue-base-2)",
          "3": "var(--color-root-energy-blue-base-3)",
          "4": "var(--color-root-energy-blue-base-4)",
          "5": "var(--color-root-energy-blue-base-5)",
          "6": "var(--color-root-energy-blue-base-6)",
          "7": "var(--color-root-energy-blue-base-7)"
        },
        "light": {
          "1": "var(--color-root-energy-blue-light-1)",
          "2": "var(--color-root-energy-blue-light-2)",
          "3": "var(--color-root-energy-blue-light-3)",
          "4": "var(--color-root-energy-blue-light-4)",
          "5": "var(--color-root-energy-blue-light-5)",
          "6": "var(--color-root-energy-blue-light-6)",
          "7": "var(--color-root-energy-blue-light-7)"
        },
        "dark": {
          "1": "var(--color-root-energy-blue-dark-1)",
          "2": "var(--color-root-energy-blue-dark-2)",
          "3": "var(--color-root-energy-blue-dark-3)",
          "4": "var(--color-root-energy-blue-dark-4)",
          "5": "var(--color-root-energy-blue-dark-5)",
          "6": "var(--color-root-energy-blue-dark-6)",
          "7": "var(--color-root-energy-blue-dark-7)"
        }
      }
    },
    "jade": {
      "green": {
        "base": {
          "1": "var(--color-root-jade-green-base-1)",
          "2": "var(--color-root-jade-green-base-2)",
          "3": "var(--color-root-jade-green-base-3)",
          "4": "var(--color-root-jade-green-base-4)",
          "5": "var(--color-root-jade-green-base-5)",
          "6": "var(--color-root-jade-green-base-6)",
          "7": "var(--color-root-jade-green-base-7)"
        },
        "light": {
          "1": "var(--color-root-jade-green-light-1)",
          "2": "var(--color-root-jade-green-light-2)",
          "3": "var(--color-root-jade-green-light-3)",
          "4": "var(--color-root-jade-green-light-4)",
          "5": "var(--color-root-jade-green-light-5)",
          "6": "var(--color-root-jade-green-light-6)",
          "7": "var(--color-root-jade-green-light-7)"
        },
        "dark": {
          "1": "var(--color-root-jade-green-dark-1)",
          "2": "var(--color-root-jade-green-dark-2)",
          "3": "var(--color-root-jade-green-dark-3)",
          "4": "var(--color-root-jade-green-dark-4)",
          "5": "var(--color-root-jade-green-dark-5)",
          "6": "var(--color-root-jade-green-dark-6)",
          "7": "var(--color-root-jade-green-dark-7)"
        }
      }
    },
    "dragonfruit": {
      "pink": {
        "base": {
          "1": "var(--color-root-dragonfruit-pink-base-1)",
          "2": "var(--color-root-dragonfruit-pink-base-2)",
          "3": "var(--color-root-dragonfruit-pink-base-3)",
          "4": "var(--color-root-dragonfruit-pink-base-4)",
          "5": "var(--color-root-dragonfruit-pink-base-5)",
          "6": "var(--color-root-dragonfruit-pink-base-6)",
          "7": "var(--color-root-dragonfruit-pink-base-7)"
        },
        "light": {
          "1": "var(--color-root-dragonfruit-pink-light-1)",
          "2": "var(--color-root-dragonfruit-pink-light-2)",
          "3": "var(--color-root-dragonfruit-pink-light-3)",
          "4": "var(--color-root-dragonfruit-pink-light-4)",
          "5": "var(--color-root-dragonfruit-pink-light-5)",
          "6": "var(--color-root-dragonfruit-pink-light-6)",
          "7": "var(--color-root-dragonfruit-pink-light-7)"
        },
        "dark": {
          "1": "var(--color-root-dragonfruit-pink-dark-1)",
          "2": "var(--color-root-dragonfruit-pink-dark-2)",
          "3": "var(--color-root-dragonfruit-pink-dark-3)",
          "4": "var(--color-root-dragonfruit-pink-dark-4)",
          "5": "var(--color-root-dragonfruit-pink-dark-5)",
          "6": "var(--color-root-dragonfruit-pink-dark-6)",
          "7": "var(--color-root-dragonfruit-pink-dark-7)"
        }
      }
    },
    "durian": {
      "yellow": {
        "base": {
          "1": "var(--color-root-durian-yellow-base-1)",
          "2": "var(--color-root-durian-yellow-base-2)",
          "3": "var(--color-root-durian-yellow-base-3)",
          "4": "var(--color-root-durian-yellow-base-4)",
          "5": "var(--color-root-durian-yellow-base-5)",
          "6": "var(--color-root-durian-yellow-base-6)",
          "7": "var(--color-root-durian-yellow-base-7)"
        },
        "light": {
          "1": "var(--color-root-durian-yellow-light-1)",
          "2": "var(--color-root-durian-yellow-light-2)",
          "3": "var(--color-root-durian-yellow-light-3)",
          "4": "var(--color-root-durian-yellow-light-4)",
          "5": "var(--color-root-durian-yellow-light-5)",
          "6": "var(--color-root-durian-yellow-light-6)",
          "7": "var(--color-root-durian-yellow-light-7)"
        },
        "dark": {
          "1": "var(--color-root-durian-yellow-dark-1)",
          "2": "var(--color-root-durian-yellow-dark-2)",
          "3": "var(--color-root-durian-yellow-dark-3)",
          "4": "var(--color-root-durian-yellow-dark-4)",
          "5": "var(--color-root-durian-yellow-dark-5)",
          "6": "var(--color-root-durian-yellow-dark-6)",
          "7": "var(--color-root-durian-yellow-dark-7)"
        }
      }
    },
    "tomato": {
      "red": {
        "base": {
          "1": "var(--color-root-tomato-red-base-1)",
          "2": "var(--color-root-tomato-red-base-2)",
          "3": "var(--color-root-tomato-red-base-3)",
          "4": "var(--color-root-tomato-red-base-4)",
          "5": "var(--color-root-tomato-red-base-5)",
          "6": "var(--color-root-tomato-red-base-6)",
          "7": "var(--color-root-tomato-red-base-7)"
        },
        "light": {
          "1": "var(--color-root-tomato-red-light-1)",
          "2": "var(--color-root-tomato-red-light-2)",
          "3": "var(--color-root-tomato-red-light-3)",
          "4": "var(--color-root-tomato-red-light-4)",
          "5": "var(--color-root-tomato-red-light-5)",
          "6": "var(--color-root-tomato-red-light-6)",
          "7": "var(--color-root-tomato-red-light-7)"
        },
        "dark": {
          "1": "var(--color-root-tomato-red-dark-1)",
          "2": "var(--color-root-tomato-red-dark-2)",
          "3": "var(--color-root-tomato-red-dark-3)",
          "4": "var(--color-root-tomato-red-dark-4)",
          "5": "var(--color-root-tomato-red-dark-5)",
          "6": "var(--color-root-tomato-red-dark-6)",
          "7": "var(--color-root-tomato-red-dark-7)"
        }
      }
    },
    "eggplant": {
      "purple": {
        "base": {
          "1": "var(--color-root-eggplant-purple-base-1)",
          "2": "var(--color-root-eggplant-purple-base-2)",
          "3": "var(--color-root-eggplant-purple-base-3)",
          "4": "var(--color-root-eggplant-purple-base-4)",
          "5": "var(--color-root-eggplant-purple-base-5)",
          "6": "var(--color-root-eggplant-purple-base-6)",
          "7": "var(--color-root-eggplant-purple-base-7)"
        },
        "light": {
          "1": "var(--color-root-eggplant-purple-light-1)",
          "2": "var(--color-root-eggplant-purple-light-2)",
          "3": "var(--color-root-eggplant-purple-light-3)",
          "4": "var(--color-root-eggplant-purple-light-4)",
          "5": "var(--color-root-eggplant-purple-light-5)",
          "6": "var(--color-root-eggplant-purple-light-6)",
          "7": "var(--color-root-eggplant-purple-light-7)"
        },
        "dark": {
          "1": "var(--color-root-eggplant-purple-dark-1)",
          "2": "var(--color-root-eggplant-purple-dark-2)",
          "3": "var(--color-root-eggplant-purple-dark-3)",
          "4": "var(--color-root-eggplant-purple-dark-4)",
          "5": "var(--color-root-eggplant-purple-dark-5)",
          "6": "var(--color-root-eggplant-purple-dark-6)",
          "7": "var(--color-root-eggplant-purple-dark-7)"
        }
      }
    }
  },
  "reserved": {
    "true": {
      "white": "var(--color-reserved-true-white)",
      "black": "var(--color-reserved-true-black)"
    },
    "durian": {
      "yellow": {
        "electric": "var(--color-reserved-durian-yellow-electric)"
      }
    }
  },
  "surface": {
    "100": {
      "bg": "var(--color-surface-100-bg)",
      "fg": {
        "default": "var(--color-surface-100-fg-default)",
        "minor": "var(--color-surface-100-fg-minor)"
      },
      "hairline": "var(--color-surface-100-hairline)"
    },
    "200": {
      "bg": "var(--color-surface-200-bg)",
      "fg": {
        "default": "var(--color-surface-200-fg-default)",
        "minor": "var(--color-surface-200-fg-minor)"
      },
      "hairline": "var(--color-surface-200-hairline)"
    },
    "300": {
      "bg": "var(--color-surface-300-bg)",
      "fg": {
        "default": "var(--color-surface-300-fg-default)",
        "minor": "var(--color-surface-300-fg-minor)"
      },
      "hairline": "var(--color-surface-300-hairline)"
    },
    "400": {
      "bg": "var(--color-surface-400-bg)",
      "fg": {
        "default": "var(--color-surface-400-fg-default)",
        "minor": "var(--color-surface-400-fg-minor)"
      },
      "hairline": "var(--color-surface-400-hairline)"
    },
    "500": {
      "bg": "var(--color-surface-500-bg)",
      "fg": {
        "default": "var(--color-surface-500-fg-default)",
        "minor": "var(--color-surface-500-fg-minor)"
      },
      "hairline": "var(--color-surface-500-hairline)"
    },
    "600": {
      "bg": "var(--color-surface-600-bg)",
      "fg": {
        "default": "var(--color-surface-600-fg-default)",
        "minor": "var(--color-surface-600-fg-minor)"
      },
      "hairline": "var(--color-surface-600-hairline)"
    }
  },
  "btn": {
    "primary": {
      "bg": "var(--color-btn-primary-bg)",
      "fg": {
        "default": "var(--color-btn-primary-fg-default)",
        "minor": "var(--color-btn-primary-fg-minor)"
      },
      "hairline": "var(--color-btn-primary-hairline)",
      "behavior": "var(--color-btn-primary-behavior)"
    },
    "secondary": {
      "bg": "var(--color-btn-secondary-bg)",
      "fg": {
        "default": "var(--color-btn-secondary-fg-default)",
        "minor": "var(--color-btn-secondary-fg-minor)"
      },
      "hairline": "var(--color-btn-secondary-hairline)",
      "behavior": "var(--color-btn-secondary-behavior)"
    },
    "tertiary": {
      "bg": "var(--color-btn-tertiary-bg)",
      "fg": {
        "default": "var(--color-btn-tertiary-fg-default)",
        "minor": "var(--color-btn-tertiary-fg-minor)"
      },
      "hairline": "var(--color-btn-tertiary-hairline)",
      "behavior": "var(--color-btn-tertiary-behavior)"
    },
    "confirmation": {
      "bg": "var(--color-btn-confirmation-bg)",
      "fg": {
        "default": "var(--color-btn-confirmation-fg-default)",
        "minor": "var(--color-btn-confirmation-fg-minor)"
      },
      "hairline": "var(--color-btn-confirmation-hairline)",
      "behavior": "var(--color-btn-confirmation-behavior)"
    },
    "critical": {
      "bg": "var(--color-btn-critical-bg)",
      "fg": {
        "default": "var(--color-btn-critical-fg-default)",
        "minor": "var(--color-btn-critical-fg-minor)"
      },
      "hairline": "var(--color-btn-critical-hairline)",
      "behavior": "var(--color-btn-critical-behavior)"
    },
    "disabled": {
      "bg": "var(--color-btn-disabled-bg)",
      "fg": {
        "default": "var(--color-btn-disabled-fg-default)",
        "minor": "var(--color-btn-disabled-fg-minor)"
      },
      "hairline": "var(--color-btn-disabled-hairline)"
    }
  },
  "tint": {
    "white": {
      "25": "var(--color-tint-white-25)",
      "50": "var(--color-tint-white-50)",
      "100": "var(--color-tint-white-100)",
      "150": "var(--color-tint-white-150)",
      "200": "var(--color-tint-white-200)",
      "250": "var(--color-tint-white-250)",
      "300": "var(--color-tint-white-300)",
      "350": "var(--color-tint-white-350)",
      "400": "var(--color-tint-white-400)",
      "450": "var(--color-tint-white-450)",
      "500": "var(--color-tint-white-500)",
      "550": "var(--color-tint-white-550)",
      "600": "var(--color-tint-white-600)",
      "650": "var(--color-tint-white-650)",
      "700": "var(--color-tint-white-700)",
      "750": "var(--color-tint-white-750)",
      "800": "var(--color-tint-white-800)",
      "850": "var(--color-tint-white-850)",
      "900": "var(--color-tint-white-900)",
      "950": "var(--color-tint-white-950)",
      "1000": "var(--color-tint-white-1000)"
    },
    "black": {
      "25": "var(--color-tint-black-25)",
      "50": "var(--color-tint-black-50)",
      "100": "var(--color-tint-black-100)",
      "150": "var(--color-tint-black-150)",
      "200": "var(--color-tint-black-200)",
      "250": "var(--color-tint-black-250)",
      "300": "var(--color-tint-black-300)",
      "350": "var(--color-tint-black-350)",
      "400": "var(--color-tint-black-400)",
      "450": "var(--color-tint-black-450)",
      "500": "var(--color-tint-black-500)",
      "550": "var(--color-tint-black-550)",
      "600": "var(--color-tint-black-600)",
      "650": "var(--color-tint-black-650)",
      "700": "var(--color-tint-black-700)",
      "750": "var(--color-tint-black-750)",
      "800": "var(--color-tint-black-800)",
      "850": "var(--color-tint-black-850)",
      "900": "var(--color-tint-black-900)",
      "950": "var(--color-tint-black-950)",
      "1000": "var(--color-tint-black-1000)"
    }
  },
  "link": {
    "base": {
      "1": "var(--color-link-base-1)"
    }
  },
  "success": {
    "bg": "var(--color-success-bg)",
    "fg": "var(--color-success-fg)",
    "hairline": "var(--color-success-hairline)",
    "txt": "var(--color-success-txt)"
  },
  "critical": {
    "bg": "var(--color-critical-bg)",
    "fg": "var(--color-critical-fg)",
    "hairline": "var(--color-critical-hairline)",
    "txt": "var(--color-critical-txt)"
  },
  "warning": {
    "bg": "var(--color-warning-bg)",
    "fg": "var(--color-warning-fg)",
    "hairline": "var(--color-warning-hairline)",
    "txt": "var(--color-warning-txt)"
  },
  "highlight": {
    "bg": "var(--color-highlight-bg)",
    "fg": "var(--color-highlight-fg)",
    "hairline": "var(--color-highlight-hairline)",
    "txt": "var(--color-highlight-txt)"
  },
  "pricing": {
    "bg": "var(--color-pricing-bg)",
    "fg": "var(--color-pricing-fg)",
    "txt": "var(--color-pricing-txt)",
    "hairline": "var(--color-pricing-hairline)"
  },
  "atc": {
    "mini": {
      "bg": {
        "default": "var(--color-atc-mini-bg-default)",
        "added": "var(--color-atc-mini-bg-added)",
        "disabled": "var(--color-atc-mini-bg-disabled)"
      },
      "fg": {
        "default": "var(--color-atc-mini-fg-default)",
        "added": "var(--color-atc-mini-fg-added)",
        "disabled": "var(--color-atc-mini-fg-disabled)"
      },
      "hairline": "var(--color-atc-mini-hairline)"
    },
    "large": {
      "bg": {
        "default": "var(--color-atc-large-bg-default)",
        "disabled": "var(--color-atc-large-bg-disabled)"
      },
      "fg": {
        "default": "var(--color-atc-large-fg-default)",
        "disabled": "var(--color-atc-large-fg-disabled)"
      }
    }
  },
  "navbar": {
    "bg": {
      "default": "var(--color-navbar-bg-default)",
      "highlight": "var(--color-navbar-bg-highlight)",
      "selected": "var(--color-navbar-bg-selected)",
      "transluscent": "var(--color-navbar-bg-transluscent)"
    },
    "fg": {
      "default": "var(--color-navbar-fg-default)",
      "highlight": "var(--color-navbar-fg-highlight)",
      "selected": "var(--color-navbar-fg-selected)"
    },
    "hairline": "var(--color-navbar-hairline)",
    "divider": "var(--color-navbar-divider)",
    "logo": "var(--color-navbar-logo)"
  },
  "sidebar": {
    "bg": {
      "100": "var(--color-sidebar-bg-100)",
      "200": "var(--color-sidebar-bg-200)",
      "300": "var(--color-sidebar-bg-300)",
      "400": "var(--color-sidebar-bg-400)"
    },
    "fg": {
      "100": {
        "default": "var(--color-sidebar-fg-100-default)",
        "selected": "var(--color-sidebar-fg-100-selected)",
        "subdued": "var(--color-sidebar-fg-100-subdued)"
      },
      "200": "var(--color-sidebar-fg-200)",
      "300": "var(--color-sidebar-fg-300)",
      "400": "var(--color-sidebar-fg-400)"
    },
    "hairline": "var(--color-sidebar-hairline)",
    "logo": "var(--color-sidebar-logo)"
  },
  "backdrop": {
    "50": {
      "bg": "var(--color-backdrop-50-bg)"
    },
    "100": {
      "bg": "var(--color-backdrop-100-bg)"
    }
  },
  "input": {
    "100": {
      "bg": {
        "default": "var(--color-input-100-bg-default)",
        "active": "var(--color-input-100-bg-active)",
        "disabled": "var(--color-input-100-bg-disabled)",
        "critical": "var(--color-input-100-bg-critical)"
      },
      "fg": {
        "default": "var(--color-input-100-fg-default)",
        "placeholder": "var(--color-input-100-fg-placeholder)",
        "disabled": "var(--color-input-100-fg-disabled)",
        "critical": "var(--color-input-100-fg-critical)"
      },
      "hairline": {
        "default": "var(--color-input-100-hairline-default)",
        "active": "var(--color-input-100-hairline-active)",
        "disabled": "var(--color-input-100-hairline-disabled)",
        "critical": "var(--color-input-100-hairline-critical)"
      },
      "icon": {
        "default": "var(--color-input-100-icon-default)",
        "active": "var(--color-input-100-icon-active)"
      }
    },
    "200": {
      "bg": {
        "default": "var(--color-input-200-bg-default)",
        "active": "var(--color-input-200-bg-active)",
        "disabled": "var(--color-input-200-bg-disabled)",
        "critical": "var(--color-input-200-bg-critical)"
      },
      "fg": {
        "default": "var(--color-input-200-fg-default)",
        "placeholder": "var(--color-input-200-fg-placeholder)",
        "disabled": "var(--color-input-200-fg-disabled)",
        "critical": "var(--color-input-200-fg-critical)"
      },
      "hairline": {
        "default": "var(--color-input-200-hairline-default)",
        "active": "var(--color-input-200-hairline-active)",
        "disabled": "var(--color-input-200-hairline-disabled)",
        "critical": "var(--color-input-200-hairline-critical)"
      },
      "icon": {
        "default": "var(--color-input-200-icon-default)",
        "active": "var(--color-input-200-icon-active)"
      }
    }
  },
  "notification": {
    "fg": "var(--color-notification-fg)",
    "bg": "var(--color-notification-bg)"
  },
  "bookmark": {
    "bg": {
      "selected": "var(--color-bookmark-bg-selected)"
    }
  },
  "product": {
    "bg": {
      "img": {
        "tint": "var(--color-product-bg-img-tint)"
      }
    }
  },
  "promo": {
    "100": {
      "fg": {
        "default": "var(--color-promo-100-fg-default)",
        "minor": "var(--color-promo-100-fg-minor)",
        "hairline": "var(--color-promo-100-fg-hairline)"
      },
      "bg": {
        "lighter": "var(--color-promo-100-bg-lighter)",
        "light": "var(--color-promo-100-bg-light)",
        "default": "var(--color-promo-100-bg-default)",
        "dark": "var(--color-promo-100-bg-dark)",
        "darker": "var(--color-promo-100-bg-darker)"
      }
    },
    "200": {
      "fg": {
        "default": "var(--color-promo-200-fg-default)",
        "minor": "var(--color-promo-200-fg-minor)",
        "hairline": "var(--color-promo-200-fg-hairline)"
      },
      "bg": {
        "lighter": "var(--color-promo-200-bg-lighter)",
        "light": "var(--color-promo-200-bg-light)",
        "default": "var(--color-promo-200-bg-default)",
        "dark": "var(--color-promo-200-bg-dark)",
        "darker": "var(--color-promo-200-bg-darker)"
      }
    },
    "300": {
      "fg": {
        "default": "var(--color-promo-300-fg-default)",
        "minor": "var(--color-promo-300-fg-minor)",
        "hairline": "var(--color-promo-300-fg-hairline)"
      },
      "bg": {
        "lighter": "var(--color-promo-300-bg-lighter)",
        "light": "var(--color-promo-300-bg-light)",
        "default": "var(--color-promo-300-bg-default)",
        "dark": "var(--color-promo-300-bg-dark)",
        "darker": "var(--color-promo-300-bg-darker)"
      }
    }
  }
};
