package com.sayweee.react.cache

import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.module.annotations.ReactModule
import com.google.gson.Gson
import com.sayweee.core.order.OrderProvider
import com.sayweee.react.NativeWeeeCacheSpec

//
// Created by <PERSON><PERSON> on 10/07/2025.
//
@ReactModule(name = NativeWeeeCacheSpec.NAME)
class RNCacheModule(reactContext: ReactApplicationContext): NativeWeeeCacheSpec(reactContext) {


    override fun zipcode(): String {
        return OrderProvider.get().zipCode
    }

    override fun deliveryDate(): String {
        return OrderProvider.get().deliveryPickupDate
    }

    override fun simpleOrderItem(productId: String?, productKey: String?): String? {
        productId ?: return ""
        productKey ?: return ""
        val item = OrderProvider.get().getSimpleOrderItem(productId.toInt(), productKey);
        return Gson().toJson(item)
    }

}