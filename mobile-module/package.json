{"name": "mobile-module", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "android:weee": "react-native run-android --mode officialDebug", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start --reset-cache", "test": "jest", "enki-update": "git submodule update --init --recursive && cd vendor/weee-ui && git pull origin web-enki-stable", "enki-sync": "node vendor/weee-ui/utils/tailwind/sync-enki-files.js && node bin/eject-enki-config.js"}, "dependencies": {"@bravemobile/react-native-code-push": "^11.0.0", "@shopify/flash-list": "^2.0.0-rc.11", "i18next": "^25.3.6", "i18next-resources-to-backend": "^1.2.1", "lottie-react-native": "^7.2.4", "moti": "^0.30.0", "nativewind": "^4.1.23", "react": "19.1.0", "react-content-loader": "^7.1.1", "react-i18next": "^15.6.1", "react-native": "^0.80.0", "react-native-auto-skeleton": "^0.1.26", "react-native-dotenv": "^3.4.11", "react-native-linear-gradient": "^2.8.3", "react-native-pager-view": "^6.8.1", "react-native-reanimated": "^4.0.2", "react-native-safe-area-context": "^5.4.0", "react-native-tab-view": "^4.1.2", "react-native-worklets": "^0.4.1", "tailwindcss": "^3.4.17", "weee-native": "file:./modules/weee-native"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "19.1.0", "@react-native-community/cli-platform-android": "19.1.0", "@react-native-community/cli-platform-ios": "19.1.0", "@react-native/babel-preset": "0.80.0", "@react-native/eslint-config": "0.80.0", "@react-native/metro-config": "0.80.0", "@react-native/typescript-config": "0.80.0", "@types/jest": "^29.5.13", "@types/node": "^24.0.3", "@types/react": "^19.1.0", "@types/react-test-renderer": "^19.1.0", "babel-plugin-module-resolver": "^5.0.2", "babel-plugin-wildcard": "^7.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.1.0", "ts-node": "^10.9.2", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "codegenConfig": {"name": "WeeeRouteModuleSpec", "type": "all", "jsSrcsDir": "specs", "android": {"javaPackageName": "com.sayweee.react.spec"}}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}